package com.quhong.mongo.data;

import com.quhong.mongo.dao.BeautifulRidListDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2022/8/26
 */
@Document(collection = BeautifulRidListDao.TABLE_NAME)
public class BeautifulRidListData {

    @Id
    private ObjectId _id;

    private int rid;

    private int level;

    private String type;

    private int status;

    private String buffer_uid;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getBuffer_uid() {
        return buffer_uid;
    }

    public void setBuffer_uid(String buffer_uid) {
        this.buffer_uid = buffer_uid;
    }
}
