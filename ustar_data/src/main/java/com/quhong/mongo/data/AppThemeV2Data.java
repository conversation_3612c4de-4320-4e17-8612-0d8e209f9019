package com.quhong.mongo.data;

import com.quhong.mongo.dao.AppThemeV2Dao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * app主题
 */
@Document(collection = AppThemeV2Dao.TABLE_NAME)
public class AppThemeV2Data {

    @Id
    private ObjectId _id;
    private String docId;
    private String themeName; // 主题名称
    private List<ThemeConfig> themeConfigList; // 进阶配置
    private String iosZipUrl; // ios主题url
    private String iosZipMd5; // 包MD5
    private String iosXZipUrl; // ios-x主题url
    private String iosXZipMd5; // 包MD5
    private String androidZipUrl; // android主题url
    private String androidZipMd5; // 包MD5
    private int zipTime; // theme生成时间
    private int status; // 是否有效  1: 有效
    private int startTime; // 开始生效时间
    private int endTime;   // 结束生效时间
    private Integer ctime; // 创建时间
    private Integer mtime; // 更新时间

    public static class ThemeConfig {
        private int themePlatform;               // 主题平台【0:android  1: ios  2: ios-x】
        private String themeType;                // 主题色调【亮色(light)、 暗色(night)】
        private BottomTabbar bottomTabbar;       // 底部Tab栏配置
        private TopNavConfig topNavConfig;       // 顶部导航栏配置
        private RoomConfig room;                 // room相关细节配置
        private MomentConfig moment;             // 朋友圈相关细节配置
        private List<String> floatScreen;        // 整体飘屏

        public int getThemePlatform() {
            return themePlatform;
        }

        public void setThemePlatform(int themePlatform) {
            this.themePlatform = themePlatform;
        }

        public String getThemeType() {
            return themeType;
        }

        public void setThemeType(String themeType) {
            this.themeType = themeType;
        }

        public BottomTabbar getBottomTabbar() {
            return bottomTabbar;
        }

        public void setBottomTabbar(BottomTabbar bottomTabbar) {
            this.bottomTabbar = bottomTabbar;
        }

        public TopNavConfig getTopNavConfig() {
            return topNavConfig;
        }

        public void setTopNavConfig(TopNavConfig topNavConfig) {
            this.topNavConfig = topNavConfig;
        }

        public RoomConfig getRoom() {
            return room;
        }

        public void setRoom(RoomConfig room) {
            this.room = room;
        }

        public MomentConfig getMoment() {
            return moment;
        }

        public void setMoment(MomentConfig moment) {
            this.moment = moment;
        }

        public List<String> getFloatScreen() {
            return floatScreen;
        }

        public void setFloatScreen(List<String> floatScreen) {
            this.floatScreen = floatScreen;
        }
    }

    // 1、顶部导航栏配置
    public static class TopNavConfig{
        private String navColor;                 // 底部未选中字体颜色
        private String navSelectedColor;         // 底部选中字体颜色
        private String topIcon;                  // 顶部背景图片3x
        private String topIcon2x;                // 顶部背景图片2x

        public String getNavColor() {
            return navColor;
        }

        public void setNavColor(String navColor) {
            this.navColor = navColor;
        }

        public String getNavSelectedColor() {
            return navSelectedColor;
        }

        public void setNavSelectedColor(String navSelectedColor) {
            this.navSelectedColor = navSelectedColor;
        }

        public String getTopIcon() {
            return topIcon;
        }

        public void setTopIcon(String topIcon) {
            this.topIcon = topIcon;
        }

        public String getTopIcon2x() {
            return topIcon2x;
        }

        public void setTopIcon2x(String topIcon2x) {
            this.topIcon2x = topIcon2x;
        }
    }

    // 2、底部Tab栏配置
    public static class BottomTabbar{
        private String backgroundIcon;           // 底部Tab栏背景图3x
        private String backgroundIcon2x;         // 底部Tab栏背景图2x
        private String navSelectedBgIcon;        // 底部Tab选中背景图3x
        private String navSelectedBgIcon2x;      // 底部Tab选中背景图2x
        private String backgroundNavIcon;        // android底部Tab栏虚拟键适配图
        private String navColor;                 // 底部Tab未选中字体颜色
        private String navSelectedColor;         // 底部Tab选中字体颜色
        private List<NavBarConfig> navList;      // 底部Tab栏列表

        public String getBackgroundIcon() {
            return backgroundIcon;
        }

        public void setBackgroundIcon(String backgroundIcon) {
            this.backgroundIcon = backgroundIcon;
        }

        public String getBackgroundIcon2x() {
            return backgroundIcon2x;
        }

        public void setBackgroundIcon2x(String backgroundIcon2x) {
            this.backgroundIcon2x = backgroundIcon2x;
        }

        public String getNavSelectedBgIcon() {
            return navSelectedBgIcon;
        }

        public void setNavSelectedBgIcon(String navSelectedBgIcon) {
            this.navSelectedBgIcon = navSelectedBgIcon;
        }

        public String getNavSelectedBgIcon2x() {
            return navSelectedBgIcon2x;
        }

        public void setNavSelectedBgIcon2x(String navSelectedBgIcon2x) {
            this.navSelectedBgIcon2x = navSelectedBgIcon2x;
        }

        public String getBackgroundNavIcon() {
            return backgroundNavIcon;
        }

        public void setBackgroundNavIcon(String backgroundNavIcon) {
            this.backgroundNavIcon = backgroundNavIcon;
        }

        public String getNavColor() {
            return navColor;
        }

        public void setNavColor(String navColor) {
            this.navColor = navColor;
        }

        public String getNavSelectedColor() {
            return navSelectedColor;
        }

        public void setNavSelectedColor(String navSelectedColor) {
            this.navSelectedColor = navSelectedColor;
        }

        public List<NavBarConfig> getNavList() {
            return navList;
        }

        public void setNavList(List<NavBarConfig> navList) {
            this.navList = navList;
        }
    }

    public static class NavBarConfig{
        private String navType;                       // home、 moment、 chat、 me
        private String navIcon;                       // tab静态图标3x
        private String navIcon2x;                     // tab静态图标2x
        private String navSelectedIcon;               // tab静态选中图标3x
        private String navSelectedIcon2x;             // tab静态选中图标2x
        private String navIconJson;                   // tab图标json图
        private String navSelectedIconJson;           // tab选中图标json图

        public String getNavType() {
            return navType;
        }

        public void setNavType(String navType) {
            this.navType = navType;
        }

        public String getNavIcon() {
            return navIcon;
        }

        public void setNavIcon(String navIcon) {
            this.navIcon = navIcon;
        }

        public String getNavIcon2x() {
            return navIcon2x;
        }

        public void setNavIcon2x(String navIcon2x) {
            this.navIcon2x = navIcon2x;
        }

        public String getNavSelectedIcon() {
            return navSelectedIcon;
        }

        public void setNavSelectedIcon(String navSelectedIcon) {
            this.navSelectedIcon = navSelectedIcon;
        }

        public String getNavSelectedIcon2x() {
            return navSelectedIcon2x;
        }

        public void setNavSelectedIcon2x(String navSelectedIcon2x) {
            this.navSelectedIcon2x = navSelectedIcon2x;
        }

        public String getNavIconJson() {
            return navIconJson;
        }

        public void setNavIconJson(String navIconJson) {
            this.navIconJson = navIconJson;
        }

        public String getNavSelectedIconJson() {
            return navSelectedIconJson;
        }

        public void setNavSelectedIconJson(String navSelectedIconJson) {
            this.navSelectedIconJson = navSelectedIconJson;
        }
    }

    // 3、home栏细节配置
    public static class RoomConfig{
        private String searchIcon2x;                     // 搜索图标2x
        private String searchIcon;                       // 搜索图标3x
        private String createRoomIcon2x;                 // 创建房间2x
        private String createRoomIcon;                   // 创建房间3x

        public String getSearchIcon2x() {
            return searchIcon2x;
        }

        public void setSearchIcon2x(String searchIcon2x) {
            this.searchIcon2x = searchIcon2x;
        }

        public String getSearchIcon() {
            return searchIcon;
        }

        public void setSearchIcon(String searchIcon) {
            this.searchIcon = searchIcon;
        }

        public String getCreateRoomIcon2x() {
            return createRoomIcon2x;
        }

        public void setCreateRoomIcon2x(String createRoomIcon2x) {
            this.createRoomIcon2x = createRoomIcon2x;
        }

        public String getCreateRoomIcon() {
            return createRoomIcon;
        }

        public void setCreateRoomIcon(String createRoomIcon) {
            this.createRoomIcon = createRoomIcon;
        }
    }


    // 4、Moment栏细节配置
    public static class MomentConfig{
        private String topicIcon2x;          // 朋友圈话题入口图标2x
        private String topicIcon;            // 朋友圈话题入口图标3x
        private String noticeIcon2x;         // 朋友圈notice图标2x
        private String noticeIcon;           // 朋友圈notice图标3x
        private String postIcon2x;           // 朋友圈发布图标2x
        private String postIcon;             // 朋友圈发布图标3x
        private String penIcon2x;            // 朋友圈笔图标2x
        private String penIcon;              // 朋友圈笔图标3x

        public String getTopicIcon2x() {
            return topicIcon2x;
        }

        public void setTopicIcon2x(String topicIcon2x) {
            this.topicIcon2x = topicIcon2x;
        }

        public String getTopicIcon() {
            return topicIcon;
        }

        public void setTopicIcon(String topicIcon) {
            this.topicIcon = topicIcon;
        }

        public String getNoticeIcon2x() {
            return noticeIcon2x;
        }

        public void setNoticeIcon2x(String noticeIcon2x) {
            this.noticeIcon2x = noticeIcon2x;
        }

        public String getNoticeIcon() {
            return noticeIcon;
        }

        public void setNoticeIcon(String noticeIcon) {
            this.noticeIcon = noticeIcon;
        }

        public String getPostIcon2x() {
            return postIcon2x;
        }

        public void setPostIcon2x(String postIcon2x) {
            this.postIcon2x = postIcon2x;
        }

        public String getPostIcon() {
            return postIcon;
        }

        public void setPostIcon(String postIcon) {
            this.postIcon = postIcon;
        }

        public String getPenIcon2x() {
            return penIcon2x;
        }

        public void setPenIcon2x(String penIcon2x) {
            this.penIcon2x = penIcon2x;
        }

        public String getPenIcon() {
            return penIcon;
        }

        public void setPenIcon(String penIcon) {
            this.penIcon = penIcon;
        }
    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getDocId() {
        return docId;
    }

    public void setDocId(String docId) {
        this.docId = docId;
    }

    public String getThemeName() {
        return themeName;
    }

    public void setThemeName(String themeName) {
        this.themeName = themeName;
    }

    public List<ThemeConfig> getThemeConfigList() {
        return themeConfigList;
    }

    public void setThemeConfigList(List<ThemeConfig> themeConfigList) {
        this.themeConfigList = themeConfigList;
    }

    public String getIosZipUrl() {
        return iosZipUrl;
    }

    public void setIosZipUrl(String iosZipUrl) {
        this.iosZipUrl = iosZipUrl;
    }

    public String getIosXZipUrl() {
        return iosXZipUrl;
    }

    public void setIosXZipUrl(String iosXZipUrl) {
        this.iosXZipUrl = iosXZipUrl;
    }

    public String getAndroidZipUrl() {
        return androidZipUrl;
    }

    public void setAndroidZipUrl(String androidZipUrl) {
        this.androidZipUrl = androidZipUrl;
    }

    public int getZipTime() {
        return zipTime;
    }

    public void setZipTime(int zipTime) {
        this.zipTime = zipTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getStartTime() {
        return startTime;
    }

    public void setStartTime(int startTime) {
        this.startTime = startTime;
    }

    public int getEndTime() {
        return endTime;
    }

    public void setEndTime(int endTime) {
        this.endTime = endTime;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public String getIosZipMd5() {
        return iosZipMd5;
    }

    public void setIosZipMd5(String iosZipMd5) {
        this.iosZipMd5 = iosZipMd5;
    }

    public String getIosXZipMd5() {
        return iosXZipMd5;
    }

    public void setIosXZipMd5(String iosXZipMd5) {
        this.iosXZipMd5 = iosXZipMd5;
    }

    public String getAndroidZipMd5() {
        return androidZipMd5;
    }

    public void setAndroidZipMd5(String androidZipMd5) {
        this.androidZipMd5 = androidZipMd5;
    }
}
