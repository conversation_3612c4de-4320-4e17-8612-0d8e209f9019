package com.quhong.mongo.data;

import com.quhong.mongo.dao.OfficialPushDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;


@Document(collection = OfficialPushDao.TABLE_NAME)
public class OfficialPushData {
    @Id
    private ObjectId _id;
    private String title;
    private String title_ar;
    private String subtitle;
    private String picture;
    private String picture_ar;
    private int width;
    private int height;
    private String body;
    private String body_ar;
    private String act;
    private String act_ar;
    private String url;
    private int lang;
    private int start_time;
    private int end_time;
    private int valid;
    private int news_type; // 类型： 1:图文链接模式  2：图文模式  3:文本模式  4：纯图片ui展示  5:各种奖励UI展示
    private int atype; // 跳转的功能  OfficialData 表一致
    private String room_id; //  指定跳转对象的值,可以是房间id，朋友圈id，或者其他
    private int ntype; //  0：notice  1:activity
    /**
     * # push_user 推送对象群体
     * # 0：付费用户(限过去30天日活用户)、 1: 非付费用户(仅限过去30天日活用户)、 2: 女性用户(仅限过去30天日活用户)、 3: 当天注册用户、
     * # 4: 最近7天注册用户、 5: 过去7天日活用户、 6: 过去30天日活用户、 7: 给指定ID推送  8:指定BC用户
     */
    private int push_user;
    private String user_rid; // 推送指定对象rid
    private int finish; //  是否已完成推送 1: 是


    public OfficialPushData() {

    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle_ar() {
        return title_ar;
    }

    public void setTitle_ar(String title_ar) {
        this.title_ar = title_ar;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getPicture() {
        return picture;
    }

    public void setPicture(String picture) {
        this.picture = picture;
    }

    public String getPicture_ar() {
        return picture_ar;
    }

    public void setPicture_ar(String picture_ar) {
        this.picture_ar = picture_ar;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getBody_ar() {
        return body_ar;
    }

    public void setBody_ar(String body_ar) {
        this.body_ar = body_ar;
    }

    public String getAct() {
        return act;
    }

    public void setAct(String act) {
        this.act = act;
    }

    public String getAct_ar() {
        return act_ar;
    }

    public void setAct_ar(String act_ar) {
        this.act_ar = act_ar;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getLang() {
        return lang;
    }

    public void setLang(int lang) {
        this.lang = lang;
    }

    public int getStart_time() {
        return start_time;
    }

    public void setStart_time(int start_time) {
        this.start_time = start_time;
    }

    public int getEnd_time() {
        return end_time;
    }

    public void setEnd_time(int end_time) {
        this.end_time = end_time;
    }

    public int getValid() {
        return valid;
    }

    public void setValid(int valid) {
        this.valid = valid;
    }

    public int getNews_type() {
        return news_type;
    }

    public void setNews_type(int news_type) {
        this.news_type = news_type;
    }

    public int getAtype() {
        return atype;
    }

    public void setAtype(int atype) {
        this.atype = atype;
    }

    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public int getNtype() {
        return ntype;
    }

    public void setNtype(int ntype) {
        this.ntype = ntype;
    }

    public int getPush_user() {
        return push_user;
    }

    public void setPush_user(int push_user) {
        this.push_user = push_user;
    }

    public String getUser_rid() {
        return user_rid;
    }

    public void setUser_rid(String user_rid) {
        this.user_rid = user_rid;
    }

    public int getFinish() {
        return finish;
    }

    public void setFinish(int finish) {
        this.finish = finish;
    }
}
