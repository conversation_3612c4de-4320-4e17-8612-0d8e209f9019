package com.quhong.mongo.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.quhong.mongo.dao.RoomGuideConfigDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 8.60房间引导弹窗配置
 */
@Document(collection = RoomGuideConfigDao.TABLE_NAME)
public class RoomGuideConfigData {

    @Id
    @JSONField(serialize = false)
    private ObjectId _id;
    private int userType;// 1 7天(包含)内注册的新用户  2 注册大于7天小于等于30天的新用户 3 流失大于等于15天的回流用户
    private int scene;// 1 语音房首页 2 社交首页
    private int homeKeepTime; // 首页停留大于等于N秒时弹出 5.3
    private int voiceRoomKeepTime; // 在房间内停留时长小于等于N秒，退出房间后弹出 5.4
    private int enterRoomCount;   // 用户进房次数等于N次时，退出房间后弹出 5.5 (5.3-5.5需要同时满足)
    private int oneDayCount; // 弹窗当天最大的弹出次数，达到配置的次数后，不再弹出。
    private int totalCountDay; // 配置弹窗的弹出天数
    private List<Integer> recommendGameTypeList; // 游戏房 推荐游戏列表
    private int recommendRoomType; //  1 迎新房 2 同国家（同地区房间） 3 优质房 4 大R房
    private int status; // 0 无效 1 有效
    private int roomType;// 1 语音房间弹窗 2 游戏房间弹窗
    private int ctime;
    private int mtime;

    private int area;// 用户地区   1 大区 2 小区 3不指定区域
    private int gender; // 用户性别 1 男 2 女 3 全部

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public int getUserType() {
        return userType;
    }

    public void setUserType(int userType) {
        this.userType = userType;
    }

    public int getScene() {
        return scene;
    }

    public void setScene(int scene) {
        this.scene = scene;
    }

    public int getHomeKeepTime() {
        return homeKeepTime;
    }

    public void setHomeKeepTime(int homeKeepTime) {
        this.homeKeepTime = homeKeepTime;
    }

    public int getVoiceRoomKeepTime() {
        return voiceRoomKeepTime;
    }

    public void setVoiceRoomKeepTime(int voiceRoomKeepTime) {
        this.voiceRoomKeepTime = voiceRoomKeepTime;
    }

    public int getEnterRoomCount() {
        return enterRoomCount;
    }

    public void setEnterRoomCount(int enterRoomCount) {
        this.enterRoomCount = enterRoomCount;
    }

    public int getOneDayCount() {
        return oneDayCount;
    }

    public void setOneDayCount(int oneDayCount) {
        this.oneDayCount = oneDayCount;
    }

    public int getTotalCountDay() {
        return totalCountDay;
    }

    public void setTotalCountDay(int totalCountDay) {
        this.totalCountDay = totalCountDay;
    }

    public List<Integer> getRecommendGameTypeList() {
        return recommendGameTypeList;
    }

    public void setRecommendGameTypeList(List<Integer> recommendGameTypeList) {
        this.recommendGameTypeList = recommendGameTypeList;
    }

    public int getRecommendRoomType() {
        return recommendRoomType;
    }

    public void setRecommendRoomType(int recommendRoomType) {
        this.recommendRoomType = recommendRoomType;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public int getMtime() {
        return mtime;
    }

    public void setMtime(int mtime) {
        this.mtime = mtime;
    }

    public int getRoomType() {
        return roomType;
    }

    public void setRoomType(int roomType) {
        this.roomType = roomType;
    }

    public int getArea() {
        return area;
    }

    public void setArea(int area) {
        this.area = area;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }
}
