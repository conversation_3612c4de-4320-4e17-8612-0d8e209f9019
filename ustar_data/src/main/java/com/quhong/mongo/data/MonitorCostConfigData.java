package com.quhong.mongo.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.quhong.mongo.dao.MonitorCostConfigDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;


/**
 * 通过钻石标题进行成本总盈亏告警（下发 + 回收）
 */
@Document(collection = MonitorCostConfigDao.TABLE_NAME)
public class MonitorCostConfigData {

    @Id
    @JSONField(serialize = false)
    private ObjectId _id;
    private String warnName;            // 告警标题  xx活动、 xx游戏
    private String beanTitle;           // 钻石标题 【使用包含过滤, 可以是多个标题, 可能不同type对应不同标题, 都写在一个字段里】
    private int warnType;               // 0: 持续累计告警  1: 按天累计告警
    private List<Integer> typeList;     // 告警包含钻石类型
    private long maxLimit;              // 总支出告警额L, 值必须大于0,  2*L、4*L 也要告警  为0不告警
    private long minLimit;              // 总收入告警额L, 值必须小于0,  2*L、4*L 也要告警  为0不告警
    private int status;                 // 状态 1: 有效

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getWarnName() {
        return warnName;
    }

    public void setWarnName(String warnName) {
        this.warnName = warnName;
    }

    public String getBeanTitle() {
        return beanTitle;
    }

    public void setBeanTitle(String beanTitle) {
        this.beanTitle = beanTitle;
    }

    public int getWarnType() {
        return warnType;
    }

    public void setWarnType(int warnType) {
        this.warnType = warnType;
    }

    public List<Integer> getTypeList() {
        return typeList;
    }

    public void setTypeList(List<Integer> typeList) {
        this.typeList = typeList;
    }

    public long getMaxLimit() {
        return maxLimit;
    }

    public void setMaxLimit(long maxLimit) {
        this.maxLimit = maxLimit;
    }

    public long getMinLimit() {
        return minLimit;
    }

    public void setMinLimit(long minLimit) {
        this.minLimit = minLimit;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
