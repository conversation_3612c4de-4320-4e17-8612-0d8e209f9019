package com.quhong.mongo.data;

import com.quhong.mongo.dao.CelebrityActivityDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 最受欢迎的名人活动模板
 */
@Document(collection = CelebrityActivityDao.TABLE_NAME)
public class CelebrityActivity {

    @Id
    private ObjectId _id;
    private String acNameEn; // 英文活动名
    private String acNameAr; // 阿语活动名
    private String acUrl; // h5地址(自动生成)
    private Integer startTime; // 活动开始时间
    private Integer endTime; // 活动结束时间
    private ActivityConfig config; // 活动配置
    private List<ActivityGift> activityGiftList; // 活动礼物，都是按照钻石数来进行排行
    private Integer status; // 1 是否已结束
    private Integer ctime; // 创建时间
    private Integer mtime; // 更新时间


    public static class ActivityConfig {
        private Integer homePageType; // 大头页类型 1静态 2动态 3静态动态
        private String homePagePicture; // 大头页静态图
        private String homePagePictureAr; // 大头页静态图ar
        private String homePagePictureDt; // 大头页动态图
        private String homePagePictureDtAr; // 大头页动态图ar
        private int fontSize;

        public Integer getHomePageType() {
            return homePageType;
        }

        public void setHomePageType(Integer homePageType) {
            this.homePageType = homePageType;
        }

        public String getHomePagePicture() {
            return homePagePicture;
        }

        public void setHomePagePicture(String homePagePicture) {
            this.homePagePicture = homePagePicture;
        }

        public String getHomePagePictureAr() {
            return homePagePictureAr;
        }

        public void setHomePagePictureAr(String homePagePictureAr) {
            this.homePagePictureAr = homePagePictureAr;
        }

        public String getHomePagePictureDt() {
            return homePagePictureDt;
        }

        public void setHomePagePictureDt(String homePagePictureDt) {
            this.homePagePictureDt = homePagePictureDt;
        }

        public String getHomePagePictureDtAr() {
            return homePagePictureDtAr;
        }

        public void setHomePagePictureDtAr(String homePagePictureDtAr) {
            this.homePagePictureDtAr = homePagePictureDtAr;
        }

        public int getFontSize() {
            return fontSize;
        }

        public void setFontSize(int fontSize) {
            this.fontSize = fontSize;
        }
    }

    public static class ActivityGift {
        private Integer giftId; // 活动礼物id
        private String giftName; // 活动礼物名称
        private String giftNameAr; // 阿语活动礼物名称
        private Integer giftPrice; // 活动礼物价格
        private String giftPictureUrl; // 活动礼物图片地址
        private String giftDescribeOneEn;
        private String giftDescribeOneAr;
        private String giftDescribeTowEn;
        private String giftDescribeTowAr;
        private String giftDescribeThreeEn;
        private String giftDescribeThreeAr;

        public Integer getGiftId() {
            return giftId;
        }

        public void setGiftId(Integer giftId) {
            this.giftId = giftId;
        }

        public String getGiftName() {
            return giftName;
        }

        public void setGiftName(String giftName) {
            this.giftName = giftName;
        }

        public String getGiftNameAr() {
            return giftNameAr;
        }

        public void setGiftNameAr(String giftNameAr) {
            this.giftNameAr = giftNameAr;
        }

        public Integer getGiftPrice() {
            return giftPrice;
        }

        public void setGiftPrice(Integer giftPrice) {
            this.giftPrice = giftPrice;
        }

        public String getGiftPictureUrl() {
            return giftPictureUrl;
        }

        public void setGiftPictureUrl(String giftPictureUrl) {
            this.giftPictureUrl = giftPictureUrl;
        }

        public String getGiftDescribeOneEn() {
            return giftDescribeOneEn;
        }

        public void setGiftDescribeOneEn(String giftDescribeOneEn) {
            this.giftDescribeOneEn = giftDescribeOneEn;
        }

        public String getGiftDescribeOneAr() {
            return giftDescribeOneAr;
        }

        public void setGiftDescribeOneAr(String giftDescribeOneAr) {
            this.giftDescribeOneAr = giftDescribeOneAr;
        }

        public String getGiftDescribeTowEn() {
            return giftDescribeTowEn;
        }

        public void setGiftDescribeTowEn(String giftDescribeTowEn) {
            this.giftDescribeTowEn = giftDescribeTowEn;
        }

        public String getGiftDescribeTowAr() {
            return giftDescribeTowAr;
        }

        public void setGiftDescribeTowAr(String giftDescribeTowAr) {
            this.giftDescribeTowAr = giftDescribeTowAr;
        }

        public String getGiftDescribeThreeEn() {
            return giftDescribeThreeEn;
        }

        public void setGiftDescribeThreeEn(String giftDescribeThreeEn) {
            this.giftDescribeThreeEn = giftDescribeThreeEn;
        }

        public String getGiftDescribeThreeAr() {
            return giftDescribeThreeAr;
        }

        public void setGiftDescribeThreeAr(String giftDescribeThreeAr) {
            this.giftDescribeThreeAr = giftDescribeThreeAr;
        }
    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getAcNameEn() {
        return acNameEn;
    }

    public void setAcNameEn(String acNameEn) {
        this.acNameEn = acNameEn;
    }

    public String getAcNameAr() {
        return acNameAr;
    }

    public void setAcNameAr(String acNameAr) {
        this.acNameAr = acNameAr;
    }

    public String getAcUrl() {
        return acUrl;
    }

    public void setAcUrl(String acUrl) {
        this.acUrl = acUrl;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public ActivityConfig getConfig() {
        return config;
    }

    public void setConfig(ActivityConfig config) {
        this.config = config;
    }

    public List<ActivityGift> getActivityGiftList() {
        return activityGiftList;
    }

    public void setActivityGiftList(List<ActivityGift> activityGiftList) {
        this.activityGiftList = activityGiftList;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }
}
