package com.quhong.mongo.dao;

import com.alibaba.fastjson.JSONObject;
import com.quhong.cache.CacheMap;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.BaseInitData;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.MongoRoomData;
import org.bson.BsonRegularExpression;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class MongoRoomDao {
    private static final Logger logger = LoggerFactory.getLogger(MongoRoomDao.class);

    public static final String TABLE_NAME = "room";

    private static final int EXPIRE_TIME = 30 * 1000;

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    private final CacheMap<String, MongoRoomData> cacheMap;

    public MongoRoomDao() {
        cacheMap = new CacheMap<>(EXPIRE_TIME);
    }

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public MongoRoomData getDataFromCache(String roomId) {
        if (StringUtils.isEmpty(roomId)) {
            return null;
        }
        if (cacheMap.hasData(roomId)) {
            return cacheMap.getData(roomId);
        }
        MongoRoomData mongoRoomData = findData(roomId);
        cacheMap.cacheData(roomId, mongoRoomData);
        return mongoRoomData;
    }

    public MongoRoomData findData(String roomId) {
        try {
            Criteria criteria = Criteria.where("rid").is(roomId);
            return mongoTemplate.findOne(new Query(criteria), MongoRoomData.class);
        } catch (Exception e) {
            logger.error("get room data from db error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return null;
    }

    public List<MongoRoomData> searchDataByName(String key) {
        if (key.length() >= 30) {
            return Collections.emptyList();
        }
        try {
            key = key.trim();
            Criteria criteria = Criteria.where("name").is(key);
            Query query = new Query(criteria);
            query.limit(10);
            return mongoTemplate.find(query, MongoRoomData.class, TABLE_NAME);

        } catch (Exception e) {
            logger.error("searchDataByName, error. key={}, e={}", key, e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public int getRoomType(String roomId) {
        return getRoomType(findData(roomId));
    }

    public int getRoomType(MongoRoomData mongoRoomData) {
        if (mongoRoomData == null) {
            return -1;
        }
        return mongoRoomData.getRoomMode();

        // if (mongoRoomData.getRoom_type() > 0) {
        //     return mongoRoomData.getRoom_type();
        // }
        // return mongoRoomData.getComp();
    }

    /**
     * 不展示在列表
     */
    public void updateRoomDisplay(String roomId, int display) {
        try {
            logger.info("update room display. display={} roomId={}", display, roomId);
            Criteria criteria = Criteria.where("rid").is(roomId);
            Update update = new Update();
            update.set("head", BaseInitData.SYS_DEFAULT_AVATARS);
            update.set("display", 0);
            mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("update room display error. display={} roomId={} {}", display, roomId, e.getMessage(), e);
        }
    }

    /**
     * 更新某个字段
     */
    public long updateField(String roomId, String field, Object newValue) {
        try {
            logger.info("update room field. roomId={} field={} value={}", roomId, field, newValue);
            Criteria criteria = Criteria.where("rid").is(roomId);
            Update update = new Update();
            update.set(field, newValue);
            cacheMap.remove(roomId);
            return mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME).getMatchedCount();
        } catch (Exception e) {
            logger.error("update room field error. roomId={} field={} value={}", roomId, field, newValue, e);
            return 0;
        }
    }

    /**
     * 房主进入房间
     */
    public void updateOwnerEnterRoom(String roomId, int owner) {
        try {
            logger.info("update owner enter room. roomId={} owner={}", roomId, owner);
            Criteria criteria = Criteria.where("rid").is(roomId);
            Update update = new Update();
            update.set("owner", owner);
            update.set("btime", DateHelper.getNowSeconds());
            mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("update owner quit room error. roomId={} owner={} {}", roomId, owner, e.getMessage(), e);
        }
    }

    /**
     * 房主退出房间
     */
    public void updateOwnerQuitRoom(String roomId, int owner, int etime) {
        try {
            logger.info("update owner quit room. roomId={} owner={} etime={}", roomId, owner, etime);
            Criteria criteria = Criteria.where("rid").is(roomId);
            Update update = new Update();
            update.set("owner", owner);
            update.set("etime", etime);
            mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("update owner quit room error. roomId={} owner={} etime={} {}", roomId, owner, etime, e.getMessage(), e);
        }
    }

    /**
     * 更新房间创建游戏权限
     */
    public void updateCreateGamePermissions(String roomId, int createGame) {
        try {
            logger.info("update create game permissions. roomId={} createGame={}", roomId, createGame);
            Criteria criteria = Criteria.where("rid").is(roomId);
            Update update = new Update();
            update.set("create_game", createGame);
            mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
            cacheMap.remove(roomId);
        } catch (Exception e) {
            logger.error("update create game permissions error. roomId={} createGame={} {}", roomId, createGame, e.getMessage(), e);
        }
    }

    public void updateRoomVideoSwitch(String roomId, int videoSwitch) {
        try {
            logger.info("update room video switch. videoSwitch={} roomId={}", videoSwitch, roomId);
            Criteria criteria = Criteria.where("rid").is(roomId);
            Update update = new Update();
            update.set("video_switch", videoSwitch);
            mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("update room video switch error. videoSwitch={} roomId={} {}", videoSwitch, roomId, e.getMessage(), e);
        }
    }

    public void updateRoomOnline(String roomId, int online) {
        try {
            logger.info("update room online. roomId={} online={}", roomId, online);
            Criteria criteria = Criteria.where("rid").is(roomId);
            Update update = new Update();
            update.set("online", online);
            mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("update room online. roomId={} online={} {}", roomId, online, e.getMessage(), e);
        }
    }

    public void updateRoomGreetSwitch(String roomId, int greetSwitch) {
        try {
            logger.info("update room greet switch. greetSwitch={} roomId={}", greetSwitch, roomId);
            Criteria criteria = Criteria.where("rid").is(roomId);
            Update update = new Update();
            update.set("greet_switch", greetSwitch);
            mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("update room greet switch error. greetSwitch={} roomId={} {}", greetSwitch, roomId, e.getMessage(), e);
        }
    }

    public void updateMicTheme(String roomId, int micTheme, int themeBgType) {
        try {
            logger.info("update room mic theme. roomId={} micTheme={}", roomId, micTheme);
            Criteria criteria = Criteria.where("rid").is(roomId);
            Update update = new Update();
            update.set("mic_theme", micTheme);
            update.set("theme_bg_type", themeBgType);
            mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("update room mic theme error.  roomId={} micTheme={} {}", roomId, micTheme, e.getMessage(), e);
        }
    }

    public void updateRoomTheme(String roomId, int theme, int themeBgType) {
        try {
            logger.info("update room theme. roomId={} theme={} themeBgType={}", roomId, theme, themeBgType);
            Criteria criteria = Criteria.where("rid").is(roomId);
            Update update = new Update();
            update.set("theme", theme);
            update.set("theme_bg_type", themeBgType);
            mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("update room theme error. roomId={} theme={} themeBgType={} {}", roomId, theme, themeBgType, e.getMessage(), e);
        }
    }

    public void save(MongoRoomData mongoRoomData) {
        mongoTemplate.save(mongoRoomData);
        cacheMap.cacheData(mongoRoomData.getRid(), mongoRoomData);
    }

    public List<MongoRoomData> listNewRoom(int day) {
        int start = (int) (DateHelper.ARABIAN.getDayOffset(System.currentTimeMillis(), day) / 1000);
        Criteria criteria = Criteria.where("last_create").gt(start);
        return mongoTemplate.find(new Query(criteria), MongoRoomData.class);
    }

    public MongoRoomData getByStreamId(String streamId) {
        try {
            return mongoTemplate.findOne(new Query(Criteria.where("stream_id").is(streamId)), MongoRoomData.class);
        } catch (Exception e) {
            logger.error("get room data from db error. streamId={} {}", streamId, e.getMessage(), e);
        }
        return null;
    }

    public void updateRoomTag(String roomId, int tagId) {
        try {
            logger.info("update room tag. roomId={} tagId={}", roomId, tagId);
            Criteria criteria = Criteria.where("rid").is(roomId);
            Update update = new Update();
            update.set("tag", tagId);
            mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("update room tag error. roomId={} tagId={}", roomId, tagId, e);
        }
    }

    public void updateMemnum(String roomId, int changeNum) {
        try {
            Criteria criteria = Criteria.where("rid").is(roomId);
            Update update = new Update();
            update.inc("memnum", changeNum);
            mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("update room member num error.roomId={} changeNum={} {}", roomId, changeNum, e.getMessage(), e);
        }
    }


    // 运营系统房间背景统计
    public long themeCount(int themeId) {
        Criteria criteria = new Criteria();
        criteria.and("theme").is(themeId);
        return mongoTemplate.count(new Query(criteria), TABLE_NAME);
    }

}
