package com.quhong.mongo.dao;

import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.UpdateOptions;
import com.quhong.cache.CacheMap;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.BeansTotalDevoteData;
import org.bson.BsonDocument;
import org.bson.BsonInt32;
import org.bson.BsonString;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Lazy
@Component
public class BeansTotalDevoteDao {
    private static final Logger logger = LoggerFactory.getLogger(BeansTotalDevoteDao.class);

    public static final String TABLE_NAME = "beans_total_devote";

    private static final long CACHE_TIME = 60 * 1000L;
    private CacheMap<String, BeansTotalDevoteData> cacheMap;

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public BeansTotalDevoteDao() {
        cacheMap = new CacheMap<>(CACHE_TIME);
    }

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public void incrDevote(String uid, int beans) {
        try {
            MongoCollection<Document> beansTotalDevote = mongoTemplate.getDb().getCollection(TABLE_NAME);
            BsonDocument filter = new BsonDocument("_id", new BsonString(uid));
            BsonDocument update = new BsonDocument();
            update.append("$inc", new BsonDocument("beans", new BsonInt32(beans)));
            update.append("$set", new BsonDocument("mtime", new BsonInt32(DateHelper.getNowSeconds())));
            beansTotalDevote.updateOne(filter, update, new UpdateOptions().upsert(true));
        } catch (Exception e) {
            logger.error("incr beans total devote error. uid={} beans={} {}", uid, beans, e.getMessage(), e);
        }
    }

    public BeansTotalDevoteData getData(String uid) {
        BeansTotalDevoteData data = cacheMap.getData(getCacheKey(uid));
        if (data != null) {
            return data;
        }
        data = findData(uid);
        if (data != null) {
            cacheMap.cacheData(getCacheKey(uid), data);
        }
        return data;
    }

    public BeansTotalDevoteData findData(String uid){
        try {
            /* BeansTotalDevoteData _id为String类型，mongoTemplate通过正常方式是查找不到对应对象的
             * 因此，需要通过调用原始方法才能获取对象
             */
            MongoCollection<Document> collection = mongoTemplate.getDb().getCollection(TABLE_NAME);
            Document find = new Document("_id", uid);
            MongoCursor<Document> iter = collection.find(find).limit(1).iterator();
            while (iter.hasNext()) {
                Document doc = iter.next();
                BeansTotalDevoteData data = new BeansTotalDevoteData();
                data.set_id(doc.getString("_id"));
                Long beans = null;
                try {
                    Integer temp = doc.getInteger("beans");
                    if(temp != null){
                        beans = temp.longValue();
                    }
                }catch (Exception e){
                    beans = doc.getLong("beans");
                }
                data.setBeans(beans == null ? 0 : beans);
                Long mtime = null;
                try {
                    Integer temp = doc.getInteger("mtime");
                    if(temp != null){
                        mtime = temp.longValue();
                    }
                }catch (Exception e){
                    mtime = doc.getLong("mtime");
                }
                data.setMtime(mtime == null ? 0 : mtime.intValue());
                return data;
            }
        }catch (Exception e){
            logger.error(" uid={} {}", uid, e.getMessage() , e);
        }
        return null;
    }

    private String getCacheKey(String uid){
        return "beansTotalDevote:" + uid;
    }
}
