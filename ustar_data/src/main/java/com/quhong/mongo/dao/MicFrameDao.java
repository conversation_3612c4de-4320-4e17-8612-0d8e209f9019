package com.quhong.mongo.dao;

import com.quhong.core.utils.DateHelper;
import com.quhong.data.ResourceGroupData;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.MicFrameData;
import com.quhong.mongo.data.MicFrameSourceData;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Component
public class MicFrameDao {
    private static final Logger logger = LoggerFactory.getLogger(MicFrameDao.class);

    public static final String TABLE_NAME = "mic_frame";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public MicFrameDao() {

    }

    public MicFrameData findData(String uid, int micFrameId) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("mic_id").is(micFrameId);
            return mongoTemplate.findOne(new Query(criteria), MicFrameData.class);
        } catch (Exception e) {
            logger.error("mic frame dao. find data error.micFrameId={} uid={} {}", micFrameId, uid, e.getMessage(), e);
        }
        return null;
    }

    public List<MicFrameData> findList(String uid,int start,int size) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.DESC, "_id");
            query.with(sort);
//            query.skip(start).limit(size);
            return mongoTemplate.find(query, MicFrameData.class);
        } catch (Exception e) {
            logger.error("find  data error. uid={} {}", uid, e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public MicFrameData findDataByStatus(String uid, int status) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("status").is(status);
            return mongoTemplate.findOne(new Query(criteria), MicFrameData.class);
        } catch (Exception e) {
            logger.error("mic frame dao. find data by status error.uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public MicFrameData insert(String uid, int micFrameId) {
        try {
            MicFrameData micFrameData = new MicFrameData();
            micFrameData.setUid(uid);
            micFrameData.setMic_id(micFrameId);
            micFrameData.setC_time(DateHelper.getNowSeconds());
            micFrameData.setStatus(0);
            mongoTemplate.insert(micFrameData);
        } catch (Exception e) {
            logger.error("mic frame dao. find data error.micFrameId={} uid={} {}", micFrameId, uid, e.getMessage(), e);
        }
        return null;
    }


    public void updateMicFrameStatusDb(MicFrameData micFrameData, int status) {
        updateMicFrameStatusDb(micFrameData.get_id(), -1, status, "");
    }


    public void updateMicFrameStatusDb(ObjectId _id, int leftTimes, int status, String checkDate) {
        try {
            Criteria criteria = Criteria.where("_id").is(_id);
            Update update = new Update();
            update.set("status", status);
            if (leftTimes >= 0) {
                update.set("left_times", leftTimes);
            }
            if (!StringUtils.isEmpty(checkDate)) {
                update.set("check_date", checkDate);
            }
            mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("update MicFrameData status error.  status={} _id={}  msg={} {}", status, _id, e.getMessage(), e);
        }
    }


    public MicFrameData insertDb(String uid, int micFrameId, int leftTimes, boolean putOn) {
        try {
            MicFrameData micFrameData = new MicFrameData();
            micFrameData.setUid(uid);
            micFrameData.setMic_id(micFrameId);
            micFrameData.setC_time(DateHelper.getNowSeconds());
            micFrameData.setStatus(putOn ? 1 : 0);
            micFrameData.setLeft_times(leftTimes);
            mongoTemplate.insert(micFrameData);
            return micFrameData;
        } catch (Exception e) {
            logger.error("mic frame dao. find data error.micFrameId={} uid={} {}", micFrameId, uid, e.getMessage(), e);
        }
        return null;
    }

    public void removeMicFrameFromDb(MicFrameData micFrameData) {
        try {
            Criteria criteria = Criteria.where("_id").is(micFrameData.get_id());
            mongoTemplate.remove(new Query(criteria), TABLE_NAME);
        } catch (Exception e) {
            logger.error("removeMicFrameFromDb from db error. micId={} uid={} {}", micFrameData.getMic_id(), micFrameData.getUid(), e.getMessage(), e);
        }
    }

    public List<ResourceGroupData> findResourceGroupList(List<Integer> resourceList) {
        try {
            Criteria criteria = new Criteria();
            criteria.and("mic_id").in(resourceList);
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.project("mic_id"),
                    Aggregation.group("mic_id").first("mic_id").as("resourceId").count().as("count")
            );

            return mongoTemplate.aggregate(aggregation, TABLE_NAME, ResourceGroupData.class).getMappedResults();

        } catch (Exception e) {
            logger.error("findResourceGroupList resourceList={} {}", resourceList, e.getMessage(), e);
        }
        return Collections.emptyList();
    }

}
