package com.quhong.mongo.dao;

import com.mongodb.client.result.UpdateResult;
import com.quhong.cache.CacheMap;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.ServerType;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.SysConfigData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Component
@Lazy
public class SysConfigDao {

    private static final Logger logger = LoggerFactory.getLogger(SysConfigDao.class);
    private final CacheMap<String, List<Integer>> cacheMap;
    private final CacheMap<String, Integer> minutesCacheMap;
    private static final long CACHE_TIME_MILLIS = 10 * 60 * 1000L;
    public static final String TURNTABLE_GIFT_KEY = "turntable_gift";
    public static final String TURNTABLE_GIFT_LIST_KEY = "turntable_gift_list";

    public static final String WEEK_COUNT = "week_count"; // 本周的周星活动id

    public static final String HIDE_UID_LIST = "hide_uid_list"; // 周星活动需要过滤的用户id
    public static final String WEEK_CONFIG = "week_config"; // 周星活动结束时间
    public static final String NEW_GIFT_KEY = "new_gift"; // 本周的周星活动礼物列表
    public static final String NEW_GIFT_LIST_KEY = "new_gift_list"; // 本周的周星活动礼物列表
    public static final String NEXT_WEEK_LIST = "next_week_list"; // 下周的周星活动礼物列表
    public static final String NEXT_2_WEEK_LIST = "next_2_week_list"; // 下下周的周星活动礼物列表
    public static final String LAST_WEEK_LIST = "last_week_list"; // 上周的周星活动礼物列表
    public static final String WEEK_BADGE_SEND = "week_badge_send"; // 周星活动发送者等级勋章id列表
    public static final String ROCKET_CONFIG = "rocket_config"; // 房间火箭配置

    public static final String PRANK_GIFT_KEY = "prank_gift";
    public static final String PRANK_GIFT_LIST_KEY = "prank_gift_list";
    public static final String COMP_RID_KEY = "comp_rid_list";
    public static final String COMP_RID_LIST_KEY = "list";
    public static final String TAP_AC_CFG = "tap_ac_cfg";
    public static final String CONQUER_GAME_TIME = "conquer_game_time";
    public static final String SYSTEM_DELETE_LIST = "system_delete_list";
    public static final String ENV_KEY = "env";
    public static final String SHOW_ENV_KEY = "show_env";  // 展示相关配置

    public static final String IS_SHOW_P_VIP_KEY = "is_show_p_vip"; // 是否展示主页vip
    public static final String ROOM_ROCKET_SWITCH_KEY = "room_rocket_switch"; // 房间火箭开关
    public static final String ROOM_ROCKET_SWITCH_V2_KEY = "room_rocket_switch_v2"; // 房间火箭v2开关
    public static final String ROOM_ROCKET_SWITCH_V2_LIGHT_KEY = "room_rocket_switch_v2_light"; // 斋灯是否开启 1开 0 关

    private static List<Integer> LUCK_GIFT_LIST;

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public SysConfigDao() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
        this.minutesCacheMap = new CacheMap<>();
        if (ServerConfig.isProduct()) {
            LUCK_GIFT_LIST = Arrays.asList(232, 340, 400);
        } else {
            LUCK_GIFT_LIST = Arrays.asList(230, 289, 258, 314, 463);
        }
    }

    public List<Integer> getList(String key, String listKey, boolean fromCache) {
        List<Integer> list;
        if (fromCache) {
            list = cacheMap.getData(key + "_" + listKey);
            if (!CollectionUtils.isEmpty(list)) {
                return list;
            }
        }
        Criteria criteria = Criteria.where("_id").is(key);
        SysConfigData sysConfig = mongoTemplate.findOne(new Query(criteria), SysConfigData.class);
        if (null == sysConfig) {
            return Collections.emptyList();
        }
        Map<String, Object> section = sysConfig.getSection();
        // noinspection unchecked
        List<Object> objectList = (List<Object>) section.get(listKey);
        if (!CollectionUtils.isEmpty(objectList)) {
            list = new ArrayList<>();
            for (Object o : objectList) {
                // 防止拿出奇奇怪怪的值，例如352.0
                list.add(Double.valueOf(String.valueOf(o)).intValue());
            }
            if (fromCache) {
                cacheMap.cacheData(key + "_" + listKey, list);
            }
        } else {
            return Collections.emptyList();
        }
        return list;
    }

    public List<String> getList(String key, String listKey) {
        Criteria criteria = Criteria.where("_id").is(key);
        SysConfigData sysConfig = mongoTemplate.findOne(new Query(criteria), SysConfigData.class);
        if (null == sysConfig) {
            return Collections.emptyList();
        }
        List<String> list = new ArrayList<>();
        Map<String, Object> section = sysConfig.getSection();
        // noinspection unchecked
        List<Object> objectList = (List<Object>) section.get(listKey);
        if (!CollectionUtils.isEmpty(objectList)) {
            for (Object o : objectList) {
                // 防止拿出奇奇怪怪的值，例如352.0
                list.add(String.valueOf(o));
            }
        } else {
            return Collections.emptyList();
        }
        return list;
    }

    /**
     * 通过id值获取礼物列表
     */
    public List<Integer> listGiftById(String key, String listKey) {
        return getList(key, listKey, true);
    }

    @SuppressWarnings("unchecked")
    public List<Integer> getCompRidList() {
        Criteria criteria = Criteria.where("_id").is(COMP_RID_KEY);
        SysConfigData sysConfig = mongoTemplate.findOne(new Query(criteria), SysConfigData.class);
        if (null == sysConfig) {
            return new ArrayList<>();
        }
        Map<String, Object> section = sysConfig.getSection();
        if (null == section || null == section.get(COMP_RID_LIST_KEY)) {
            return new ArrayList<>();
        }
        try {
            return (List<Integer>) section.get(COMP_RID_LIST_KEY);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    /**
     * 获取幸运礼物列表
     */
    public List<Integer> listLuckGift() {
        return LUCK_GIFT_LIST;
    }

    public int getLampGid() {
        boolean test = !ServerType.PRODUCT.equals(ServerConfig.getServerType());
        if (test) {
            return 230;
        }
        return 232;
    }

    public UpdateResult updateValue(String key, String session, Object value) {
        try {
            Update update = new Update();
            Map<String, Object> section = new HashMap<>();
            section.put(session, value);
            update.set("section", section);
            UpdateResult updateResult = mongoTemplate.upsert(new Query(Criteria.where("_id").is(key)), update, SysConfigData.class);
            logger.info("updateValue key={} session={} value={} modifiedCount={}", key, session, value, updateResult.getModifiedCount());
            return updateResult;
        } catch (Exception e) {
            logger.error("updateValue key={} session={}", key, session, e);
            return null;
        }
    }

    public UpdateResult updateMapValue(String key, String session, Object value) {
        try {
            SysConfigData sysConfig = mongoTemplate.findOne(new Query(Criteria.where("_id").is(key)), SysConfigData.class);
            Map<String, Object> section ;
            if (null != sysConfig) {
                 section = sysConfig.getSection();
            }else {
                 section = new HashMap<>();
            }
            Update update = new Update();
            section.put(session, value);
            update.set("section", section);
            UpdateResult updateResult = mongoTemplate.upsert(new Query(Criteria.where("_id").is(key)), update, SysConfigData.class);
            logger.info("updateMapValue key={} session={} value={} modifiedCount={}", key, session, value, updateResult.getModifiedCount());
            return updateResult;
        } catch (Exception e) {
            logger.error("updateMapValue key={} session={}", key, session, e);
            return null;
        }
    }

    public String getStringValue(String key, String session) {
        try {
            SysConfigData sysConfig = mongoTemplate.findOne(new Query(Criteria.where("_id").is(key)), SysConfigData.class);
            if (null == sysConfig) {
                return null;
            }
            Map<String, Object> section = sysConfig.getSection();
            return section.get(session) == null ? null : section.get(session) + "";
        } catch (Exception e) {
            logger.error("getStringValue key={} session={}", key, session, e);
            return null;
        }
    }

    public int getIntValue(String key, String session) {
        return getIntValue(key, session, true);
    }


    public int getIntValue(String key, String session, boolean fromCache) {
        String cacheKey = key + "-" + session;
        if (fromCache) {
            if (minutesCacheMap.hasData(cacheKey)) {
                return minutesCacheMap.getData(cacheKey);
            }
        }
        String stringValue = getStringValue(key, session);
        if (null == stringValue) {
            return 0;
        }
        try {
            int intValue = Double.valueOf(stringValue).intValue();
            minutesCacheMap.cacheData(cacheKey, intValue);
            return intValue;
        } catch (NumberFormatException e) {
            logger.error("NumberFormatException value={}", stringValue, e);
            return 0;
        }
    }

    /**
     * 获取折扣
     */
    public Double getPayDiscount() {
        try {
            Criteria criteria = Criteria.where("_id").is(TAP_AC_CFG);
            SysConfigData sysConfig = mongoTemplate.findOne(new Query(criteria), SysConfigData.class);
            if (null == sysConfig) {
                return null;
            }
            Map<String, Object> section = sysConfig.getSection();
            String discountStr = section.get("discount") + "";
            double discount = new BigDecimal(discountStr).doubleValue();
            if (0 == discount) {
                return null;
            }
            int start = Integer.parseInt(section.get("start") + "");
            int end = Integer.parseInt(section.get("end") + "");
            int nowSeconds = DateHelper.getNowSeconds();
            if (nowSeconds >= start && nowSeconds < end) {
                logger.info("get discount, discount={}", discount);
                return discount;
            }
            return null;
        } catch (Exception e) {
            logger.error("get pay discount error", e);
            return null;
        }
    }

    /**
     * 获取征服活动开始或结束时间
     *
     * @param type 1开始时间，2结束时间
     */
    public int getConquerGameTime(int type) {
        try {
            Criteria criteria = Criteria.where("_id").is(CONQUER_GAME_TIME);
            SysConfigData sysConfig = mongoTemplate.findOne(new Query(criteria), SysConfigData.class);
            if (null == sysConfig) {
                return 0;
            }
            Map<String, Object> section = sysConfig.getSection();
            String value = section.get(1 == type ? "start_time" : "end_time") + "";
            return Integer.parseInt(value);
        } catch (Exception e) {
            logger.error("get conquer game time error type={} {}", type, e);
            return 0;
        }
    }

    public List<String> getSystemDeleteList() {
        List<String> sysDelList = new ArrayList<>();
        try {
            Criteria criteria = Criteria.where("_id").is(SYSTEM_DELETE_LIST);
            SysConfigData sysConfig = mongoTemplate.findOne(new Query(criteria), SysConfigData.class);
            if (null == sysConfig) {
                return sysDelList;
            }
            Map<String, Object> section = sysConfig.getSection();
            if (null == section || null == section.get(SYSTEM_DELETE_LIST)) {
                return sysDelList;
            }
            sysDelList = (List<String>) section.get(SYSTEM_DELETE_LIST);
        } catch (Exception e) {
            logger.error("get system delete list error. {}", e.getMessage(), e);
        }
        return sysDelList;
    }


    public boolean getSwitchValue(String key, String session) {
        String stringValue = getStringValue(key, session);
        if (null == stringValue) {
            return true;
        }
        try {
            int a = Integer.parseInt(stringValue);
            return a > 0;
        } catch (NumberFormatException e) {
            logger.error("getSwitchValue value={}", stringValue, e);
            return false;
        }
    }

}
