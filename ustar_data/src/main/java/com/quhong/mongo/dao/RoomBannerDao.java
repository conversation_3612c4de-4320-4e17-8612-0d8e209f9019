package com.quhong.mongo.dao;

import com.quhong.cache.CacheMap;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.RoomBannerData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/2
 */
@Component
public class RoomBannerDao {

    private static final Logger logger = LoggerFactory.getLogger(RoomBannerDao.class);

    public static final String TABLE_NAME = "room_banner";

    // banner展示区域
    public static final int ZONE_ROOM_WINDOWS = 0;        // 语音房底部小窗口区
    public static final int ZONE_ROOM_SIDE = 1;           // 语音房侧边栏区
    public static final int ZONE_ROOM_GAME_INTERACT = 2;  // 语音房游戏中心互动游戏区
    public static final int ZONE_ROOM_GAME_OTHER = 3;     // 语音房游戏中心其他游戏区

    public static final int ZONE_GAME_ROOM_SUB = 5;       // 游戏房H5子banner
    public static final int ZONE_GAME_ROOM_WINDOWS = 6;   // 游戏房底部小窗口banner
    public static final int ZONE_GAME_ROOM_QUIT = 7;   // 游戏房退出游戏展示BC游戏


    private final CacheMap<String, List<RoomBannerData>> cacheMap = new CacheMap<>(60 * 1000L);


    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public List<RoomBannerData> findListFromCache() {
        if (cacheMap.hasData(TABLE_NAME)) {
            return cacheMap.getData(TABLE_NAME);
        }
        List<RoomBannerData> list = findList();
        cacheMap.cacheData(TABLE_NAME, list);
        return list;
    }

    public List<RoomBannerData> findList() {
        try {
            Criteria criteria = Criteria.where("show_sidebar").ne(2).and("valid").is(1);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.ASC, "banner_order");
            query.with(sort);
            return mongoTemplate.find(query, RoomBannerData.class);
        } catch (Exception e) {
            logger.error("find room banner data list error. {}", e.getMessage(), e);
        }
        return null;
    }

    public List<RoomBannerData> selectPage(String search, int showSidebar, int valid, int start, int size) {
        try {
            // 查询条件
            Criteria criteria = new Criteria();
            if(showSidebar != -1){
                criteria.and("show_sidebar").is(showSidebar);
            }

            if (valid != -1) {
                criteria.and("valid").is(valid);
            }

            if (StringUtils.hasLength(search)) {
                criteria.orOperator(Criteria.where("title").regex(".*?" + search + ".*?"), Criteria.where("title_ar").regex(".*?" + search + ".*?"));
            }

            Sort sort = Sort.by(Sort.Direction.ASC, "banner_order");
            Query query = new Query(criteria);
            query.with(sort).skip(start).limit(size);
            return mongoTemplate.find(query, RoomBannerData.class);
        } catch (Exception e) {
            logger.error("MongoThemeData selectPage error.{}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public long selectCount(String search, int showSidebar, int valid) {
        Criteria criteria = new Criteria();
        if(showSidebar != -1){
            criteria.and("show_sidebar").is(showSidebar);
        }

        if (valid != -1) {
            criteria.and("valid").is(valid);
        }
        if (StringUtils.hasLength(search)) {
            criteria.orOperator(Criteria.where("title").regex(".*?" + search + ".*?"), Criteria.where("title_ar").regex(".*?" + search + ".*?"));
        }
        return mongoTemplate.count(new Query(criteria), TABLE_NAME);
    }


    public RoomBannerData getDataByID(String docId) {
        try {
            return mongoTemplate.findById(docId, RoomBannerData.class);
        } catch (Exception e) {
            logger.error("RoomBannerData query error. msg = {}", e.getMessage(), e);

        }
        return null;
    }

    public void saveOne(RoomBannerData data) {
        try {
            mongoTemplate.insert(data);
        } catch (Exception e) {
            logger.error("RoomBannerData insert error. msg = {}", e.getMessage(), e);
        }
    }

    public void updateData(String docId, Update update) {
        try {
            mongoTemplate.updateFirst(new Query(Criteria.where("_id").is(docId)), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("updateData error.  id={} {}", docId, e.getMessage(), e);
        }
    }

    public List<RoomBannerData> getAllValidBannerList() {
        try {
            Criteria criteria = new  Criteria();
            criteria.and("valid").is(1);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.ASC, "banner_order");
            query.with(sort);
            return mongoTemplate.find(query, RoomBannerData.class);
        } catch (Exception e) {
            logger.error("getAllValidBannerList error. {}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }


    public List<RoomBannerData> getRoomBannerBySidebar(int sidebar) {
        try {
            Criteria criteria = new  Criteria();
            criteria.and("valid").is(1);
            criteria.and("show_sidebar").is(sidebar);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.ASC, "banner_order");
            query.with(sort);
            return mongoTemplate.find(query, RoomBannerData.class);
        } catch (Exception e) {
            logger.error("getRoomBannerBySidebar error. {}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }
}
