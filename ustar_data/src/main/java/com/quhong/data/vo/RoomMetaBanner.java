package com.quhong.data.vo;

import com.quhong.data.RidData;

import java.util.List;

public class RoomMetaBanner {

    private int webType;
    private int type;
    private String title;
    private String event;
    private String game_type;
    private int width;
    private int height;
    private String icon;
    private String roomIcon;
    private String url;
    private int bannerType;
    private String bannerId;
    private int isNew; // 上新标识 0否 1是
    private int hot;   // 热门
    private int showUserInfo; // 展示用户信息 0否 1是
    private List<BannerUserInfo> userInfoList; // 用户信息


    public int getWebType() {
        return webType;
    }

    public void setWebType(int webType) {
        this.webType = webType;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public String getGame_type() {
        return game_type;
    }

    public void setGame_type(String game_type) {
        this.game_type = game_type;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getRoomIcon() {
        return roomIcon;
    }

    public void setRoomIcon(String roomIcon) {
        this.roomIcon = roomIcon;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getBannerType() {
        return bannerType;
    }

    public void setBannerType(int bannerType) {
        this.bannerType = bannerType;
    }

    public String getBannerId() {
        return bannerId;
    }

    public void setBannerId(String bannerId) {
        this.bannerId = bannerId;
    }

    public int getIsNew() {
        return isNew;
    }

    public void setIsNew(int isNew) {
        this.isNew = isNew;
    }

    public int getHot() {
        return hot;
    }

    public void setHot(int hot) {
        this.hot = hot;
    }

    public int getShowUserInfo() {
        return showUserInfo;
    }

    public void setShowUserInfo(int showUserInfo) {
        this.showUserInfo = showUserInfo;
    }


    public List<BannerUserInfo> getUserInfoList() {
        return userInfoList;
    }

    public void setUserInfoList(List<BannerUserInfo> userInfoList) {
        this.userInfoList = userInfoList;
    }
}

