package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_achievement_badge")
public class AchievementBadgeData {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private int badgeType;
    private long countNum;
    private long lastCountNum;
    private int badgeId;
    private Integer mtime;
    private Integer ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public int getBadgeType() {
        return badgeType;
    }

    public void setBadgeType(int badgeType) {
        this.badgeType = badgeType;
    }

    public long getCountNum() {
        return countNum;
    }

    public void setCountNum(long countNum) {
        this.countNum = countNum;
    }

    public long getLastCountNum() {
        return lastCountNum;
    }

    public void setLastCountNum(long lastCountNum) {
        this.lastCountNum = lastCountNum;
    }

    public int getBadgeId() {
        return badgeId;
    }

    public void setBadgeId(int badgeId) {
        this.badgeId = badgeId;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "AchievementBadgeData{" +
                "id=" + id +
                ", badgeType=" + badgeType +
                ", countNum=" + countNum +
                ", lastCountNum=" + lastCountNum +
                ", badgeId=" + badgeId +
                ", mtime=" + mtime +
                ", ctime=" + ctime +
                '}';
    }
}
