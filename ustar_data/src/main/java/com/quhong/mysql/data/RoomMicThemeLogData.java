package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_room_mic_theme_log")
public class RoomMicThemeLogData {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     *房间ID
     */
    @TableField("room_id")
    private String roomId;

    /**
     * 操作用户ID
     */
    @TableField("op_uid")
    private String opUid;

    /**
     * 操作之前主题ID
     */
    @TableField("before_theme")
    private Integer beforeTheme;

    /**
     * 操作之后的主题ID
     */
    @TableField("after_theme")
    private Integer afterTheme;

    /**
     * 创建时间
     */
    private Integer ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getOpUid() {
        return opUid;
    }

    public void setOpUid(String opUid) {
        this.opUid = opUid;
    }

    public Integer getBeforeTheme() {
        return beforeTheme;
    }

    public void setBeforeTheme(Integer beforeTheme) {
        this.beforeTheme = beforeTheme;
    }

    public Integer getAfterTheme() {
        return afterTheme;
    }

    public void setAfterTheme(Integer afterTheme) {
        this.afterTheme = afterTheme;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
