package com.quhong.mysql.data;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;

import java.util.List;


@TableName(value = "t_user_recommend_notice", autoResultMap = true)
public class UserRecommendNoticeData {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 推荐用户类型
     * 0: 注册的新用户
     * 1: 流失用户
     */
    private Integer userType;
    /**
     * 天数: 注册的新用户才设置
     * 格式 1-150  中间用-分隔
     */
    private String dayLimit;
    /**
     * 推荐用户性别
     * 0: 全部、1: 男、2: 女
     */
    private Integer gender;
    /**
     * 推荐用户地区
     * 0: 全部、1:大区 2:小区
     */
    private Integer area;
    /**
     * 场景
     * 1:语音房首页 2:社交首页(游戏房) 3:动态首页
     */
    private Integer scene;
    /**
     * 触发方式
     * 0: 静默
     * 1: 点击打招呼
     * 2: 连续滑动
     */
    private Integer triggerMethod;
    /**
     * 触发条件: 根据触发方式不同而不同
     * triggerMethod = 0: 静默时长
     * triggerMethod = 1: 点击打招呼次数
     * triggerMethod = 2: 连续滑动时长
     */
    private Integer triggerCondition;
    /**
     * 全局弹出次数:
     * 该场景下允许弹窗出现的最大次数，超过则不再触发
     */
    private Integer popLimit;
    /**
     * 推荐弹窗列表
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<UserRecommendPopData> popList;
    /**
     * 状态
     * 0: 无效
     * 1: 有效
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Integer ctime;
    /**
     * 修改时间
     */
    private Integer mtime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public String getDayLimit() {
        return dayLimit;
    }

    public void setDayLimit(String dayLimit) {
        this.dayLimit = dayLimit;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Integer getArea() {
        return area;
    }

    public void setArea(Integer area) {
        this.area = area;
    }

    public Integer getScene() {
        return scene;
    }

    public void setScene(Integer scene) {
        this.scene = scene;
    }

    public Integer getTriggerMethod() {
        return triggerMethod;
    }

    public void setTriggerMethod(Integer triggerMethod) {
        this.triggerMethod = triggerMethod;
    }

    public Integer getTriggerCondition() {
        return triggerCondition;
    }

    public void setTriggerCondition(Integer triggerCondition) {
        this.triggerCondition = triggerCondition;
    }

    public Integer getPopLimit() {
        return popLimit;
    }

    public void setPopLimit(Integer popLimit) {
        this.popLimit = popLimit;
    }

    public List<UserRecommendPopData> getPopList() {
        return JSON.parseArray(JSON.toJSONString(popList), UserRecommendPopData.class);
    }

    public void setPopList(List<UserRecommendPopData> popList) {
        this.popList = popList;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }
}
