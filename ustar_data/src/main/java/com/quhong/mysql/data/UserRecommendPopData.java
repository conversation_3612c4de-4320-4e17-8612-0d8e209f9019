package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;

import java.util.List;

public class UserRecommendPopData {
    /**
     * metaId
     */
    private String metaId;
    /**
     * 弹窗类型
     * 1: 迎新房弹窗
     * 2: 游戏房弹窗
     * 3: 同地区(同国家)弹窗
     * 4: 打招呼提醒弹窗
     * 5: 打招呼匹配弹窗
     * 6: 动态推荐弹窗
     * 7: 话题推荐弹窗
     */
    private Integer popType;
    /**
     * 控频次数(popType类型弹出次数限制)
     */
    private Integer popNumber;

    /**
     * 弹窗参数（加权参数等）
     */
    private PopParams popParams;

    public static class PopParams {
        // 游戏房弹窗
        private List<Integer> recommendGameTypeList; // 推荐游戏列表
        //
        private Integer topicRid; // 指定话题rid

        public List<Integer> getRecommendGameTypeList() {
            return recommendGameTypeList;
        }

        public void setRecommendGameTypeList(List<Integer> recommendGameTypeList) {
            this.recommendGameTypeList = recommendGameTypeList;
        }

        public Integer getTopicRid() {
            return topicRid;
        }

        public void setTopicRid(Integer topicRid) {
            this.topicRid = topicRid;
        }
    }


    public String getMetaId() {
        return metaId;
    }

    public void setMetaId(String metaId) {
        this.metaId = metaId;
    }

    public Integer getPopType() {
        return popType;
    }

    public void setPopType(Integer popType) {
        this.popType = popType;
    }

    public Integer getPopNumber() {
        return popNumber;
    }

    public void setPopNumber(Integer popNumber) {
        this.popNumber = popNumber;
    }

    public PopParams getPopParams() {
        return popParams;
    }

    public void setPopParams(PopParams popParams) {
        this.popParams = popParams;
    }
}
