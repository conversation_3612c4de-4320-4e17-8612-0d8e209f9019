package com.quhong.mysql.data;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_register_login_log")
public class RegisterOrLoginLogData {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String uid;

    private Integer rid;
    /**
     * 第三方登入的uid
     */
    private String thirdUid;
    /**
     * 设备id
     */
    private String tnId;
    /**
     * 设备风险标签值
     */
    private String tnRisk;
    /**
     * 设备参数而外信息
     */
    private String tnExtraInfo;
    /**
     * 是否用图灵缓存 0 没使用 1使用
     */
    private Integer tnUseCache;

    /**
     * 0 未知 （没查询actor表，就结束的情况） 1 注册 2 登入
     */
    private Integer registerType;

    private Integer loginType;

    /**
     * 0 成功 1账号拦截 2 设备拦截
     */
    private Integer loginStatus;

    private String loginStatusDesc;

    /**
     * 设备的任一rid
     */
    private Integer deviceRid;

    /**
     * 设备的任一uid
     */
    private String deviceUid;


    private String androidId;

    private String iosKey;

    private String idfa;

    private String distinctId;

    private String taDeviceId;


    private Integer ctime;

    private String pkgName;

    private Integer vCode;

    private String vName;

    /**
     * 推广渠道
     */
    private String dynamicChannel;

    private String ip;

    private String ipCountry;

    /**
     * 手机号或vip登入的账号
     */
    private String account;

    /**
     * 注册后当前设备的账号数
     */
    private Integer nowDeviceAccountNum;

    /**
     * 注册成功时，可能使用的荣誉账号
     */
    private String deviceHonorUid;

    /**
     * 注册成功时，可能使用的等级账号
     */
    private String deviceLevelUid;

    private Integer fbGender;

    private String msg;

    /**
     * 0 安卓 1 ios
     */
    private Integer os;

    private String reqId;

    /**
     *上一次token失效的路由，注册时可为空
     */
    private String tokenExpired;

    /**
     * 数美设备id
     */
    private String shuMeiId;
    /**
     * 数美设备风险标签值
     */
    private String shuMeiRisk;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getThirdUid() {
        return thirdUid;
    }

    public void setThirdUid(String thirdUid) {
        this.thirdUid = thirdUid;
    }

    public String getTnId() {
        return tnId;
    }

    public void setTnId(String tnId) {
        this.tnId = tnId;
    }

    public String getTnRisk() {
        return tnRisk;
    }

    public void setTnRisk(String tnRisk) {
        this.tnRisk = tnRisk;
    }

    public Integer getRegisterType() {
        return registerType;
    }

    public void setRegisterType(Integer registerType) {
        this.registerType = registerType;
    }

    public Integer getLoginType() {
        return loginType;
    }

    public void setLoginType(Integer loginType) {
        this.loginType = loginType;
    }

    public Integer getLoginStatus() {
        return loginStatus;
    }

    public void setLoginStatus(Integer loginStatus) {
        this.loginStatus = loginStatus;
    }

    public String getLoginStatusDesc() {
        return loginStatusDesc;
    }

    public void setLoginStatusDesc(String loginStatusDesc) {
        this.loginStatusDesc = loginStatusDesc;
    }

    public Integer getDeviceRid() {
        return deviceRid;
    }

    public void setDeviceRid(Integer deviceRid) {
        this.deviceRid = deviceRid;
    }

    public String getDeviceUid() {
        return deviceUid;
    }

    public void setDeviceUid(String deviceUid) {
        this.deviceUid = deviceUid;
    }

    public String getAndroidId() {
        return androidId;
    }

    public void setAndroidId(String androidId) {
        this.androidId = androidId;
    }

    public String getIosKey() {
        return iosKey;
    }

    public void setIosKey(String iosKey) {
        this.iosKey = iosKey;
    }

    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        this.idfa = idfa;
    }

    public String getDistinctId() {
        return distinctId;
    }

    public void setDistinctId(String distinctId) {
        this.distinctId = distinctId;
    }

    public String getTaDeviceId() {
        return taDeviceId;
    }

    public void setTaDeviceId(String taDeviceId) {
        this.taDeviceId = taDeviceId;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public String getPkgName() {
        return pkgName;
    }

    public void setPkgName(String pkgName) {
        this.pkgName = pkgName;
    }

    public Integer getvCode() {
        return vCode;
    }

    public void setvCode(Integer vCode) {
        this.vCode = vCode;
    }

    public String getvName() {
        return vName;
    }

    public void setvName(String vName) {
        this.vName = vName;
    }

    public String getDynamicChannel() {
        return dynamicChannel;
    }

    public void setDynamicChannel(String dynamicChannel) {
        this.dynamicChannel = dynamicChannel;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getIpCountry() {
        return ipCountry;
    }

    public void setIpCountry(String ipCountry) {
        this.ipCountry = ipCountry;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public Integer getNowDeviceAccountNum() {
        return nowDeviceAccountNum;
    }

    public void setNowDeviceAccountNum(Integer nowDeviceAccountNum) {
        this.nowDeviceAccountNum = nowDeviceAccountNum;
    }

    public String getDeviceHonorUid() {
        return deviceHonorUid;
    }

    public void setDeviceHonorUid(String deviceHonorUid) {
        this.deviceHonorUid = deviceHonorUid;
    }

    public String getDeviceLevelUid() {
        return deviceLevelUid;
    }

    public void setDeviceLevelUid(String deviceLevelUid) {
        this.deviceLevelUid = deviceLevelUid;
    }

    public Integer getFbGender() {
        return fbGender;
    }

    public void setFbGender(Integer fbGender) {
        this.fbGender = fbGender;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Integer getOs() {
        return os;
    }

    public void setOs(Integer os) {
        this.os = os;
    }

    public String getTnExtraInfo() {
        return tnExtraInfo;
    }

    public void setTnExtraInfo(String tnExtraInfo) {
        this.tnExtraInfo = tnExtraInfo;
    }

    public Integer getTnUseCache() {
        return tnUseCache;
    }

    public void setTnUseCache(Integer tnUseCache) {
        this.tnUseCache = tnUseCache;
    }

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public String getTokenExpired() {
        return tokenExpired;
    }

    public void setTokenExpired(String tokenExpired) {
        this.tokenExpired = tokenExpired;
    }

    public String getShuMeiId() {
        return shuMeiId;
    }

    public void setShuMeiId(String shuMeiId) {
        this.shuMeiId = shuMeiId;
    }

    public String getShuMeiRisk() {
        return shuMeiRisk;
    }

    public void setShuMeiRisk(String shuMeiRisk) {
        this.shuMeiRisk = shuMeiRisk;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}
