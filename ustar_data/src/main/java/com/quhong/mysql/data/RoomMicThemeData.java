package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 房间麦位主题配置表
 */
@TableName("t_room_mic_theme")
public class RoomMicThemeData {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer tid;         // 背景id
    private String nameEn;
    private String nameAr;
    private String url;
    private String detailUrl;
    private String showTxtEn;
    private String showTxtAr;
    private Integer version;
    private Integer status;
    private Integer changeTheme;
    private Integer vip;
    private Integer roomStepLevel; // 主题所需要的房间等级
    /**
     * 麦位数量
     */
    private Integer micSize;
    private Integer themeType;
    private Integer orderNum;
    private Integer ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getTid() {
        return tid;
    }

    public void setTid(Integer tid) {
        this.tid = tid;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getDetailUrl() {
        return detailUrl;
    }

    public void setDetailUrl(String detailUrl) {
        this.detailUrl = detailUrl;
    }

    public String getShowTxtEn() {
        return showTxtEn;
    }

    public void setShowTxtEn(String showTxtEn) {
        this.showTxtEn = showTxtEn;
    }

    public String getShowTxtAr() {
        return showTxtAr;
    }

    public void setShowTxtAr(String showTxtAr) {
        this.showTxtAr = showTxtAr;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getChangeTheme() {
        return changeTheme;
    }

    public void setChangeTheme(Integer changeTheme) {
        this.changeTheme = changeTheme;
    }

    public Integer getVip() {
        return vip;
    }

    public void setVip(Integer vip) {
        this.vip = vip;
    }

    public Integer getRoomStepLevel() {
        return roomStepLevel;
    }

    public void setRoomStepLevel(Integer roomStepLevel) {
        this.roomStepLevel = roomStepLevel;
    }

    public Integer getMicSize() {
        return micSize;
    }

    public void setMicSize(Integer micSize) {
        this.micSize = micSize;
    }

    public Integer getThemeType() {
        return themeType;
    }

    public void setThemeType(Integer themeType) {
        this.themeType = themeType;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
