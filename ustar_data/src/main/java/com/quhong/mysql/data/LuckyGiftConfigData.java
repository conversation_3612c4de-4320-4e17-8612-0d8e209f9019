package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_lucky_gift_config")
public class LuckyGiftConfigData {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer giftId;         // 礼物id
    private String icon;            // 弹窗图标
    private String nameEn;          // 弹窗英语
    private String nameAr;          // 弹窗阿语
    private Integer bigPush;        // 大奖横幅
    private Integer prize;           // 奖池大小
    private String rewardType;      // 奖品type
    private Integer sourceId;       // 资源id
    private Integer rewardTime;     // 资源时长
    private Integer rewardNum;      // 资源数量
    private Integer ctime;          // 创建时间

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getGiftId() {
        return giftId;
    }

    public void setGiftId(Integer giftId) {
        this.giftId = giftId;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public Integer getBigPush() {
        return bigPush;
    }

    public void setBigPush(Integer bigPush) {
        this.bigPush = bigPush;
    }

    public Integer getPrize() {
        return prize;
    }

    public void setPrize(Integer prize) {
        this.prize = prize;
    }

    public String getRewardType() {
        return rewardType;
    }

    public void setRewardType(String rewardType) {
        this.rewardType = rewardType;
    }

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public Integer getRewardTime() {
        return rewardTime;
    }

    public void setRewardTime(Integer rewardTime) {
        this.rewardTime = rewardTime;
    }

    public Integer getRewardNum() {
        return rewardNum;
    }

    public void setRewardNum(Integer rewardNum) {
        this.rewardNum = rewardNum;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
