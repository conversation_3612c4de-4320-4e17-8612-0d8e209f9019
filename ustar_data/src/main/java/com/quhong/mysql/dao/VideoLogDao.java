package com.quhong.mysql.dao;

import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.VideoLogData;
import com.quhong.mysql.mapper.ustar_log.VideoLogMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class VideoLogDao extends MonthShardingDao<VideoLogMapper> {
    private static final Logger logger = LoggerFactory.getLogger(VideoLogDao.class);

    private static final long CACHE_TIME = 300 * 1000L;

    public VideoLogDao() {
        super("s_video_log");
    }

    public void insert(VideoLogData logData) {
        try {
            String suffix = DateHelper.ARABIAN.getTableSuffix(DateHelper.formatDate(logData.getCtime()));
            createTable(suffix);
            tableMapper.insert(suffix, logData);
        } catch (Exception e) {
            logger.error("insert music log data error. roomId={} {}", logData.getRoomId(), e.getMessage(), e);
        }
    }
}
