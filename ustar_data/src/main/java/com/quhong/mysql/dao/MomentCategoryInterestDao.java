package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quhong.mysql.data.MomentCategoryInterestData;
import com.quhong.mysql.mapper.ustar.MomentCategoryInterestMapper;
import com.quhong.utils.CollectionUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/5/29
 */
@Lazy
@Component
public class MomentCategoryInterestDao {

    public static String TOPIC_DETAIL_HEAD = "https://cdn3.qmovies.tv/youstar/op_1715683224_family_avatar_default.png";

    @Resource
    private MomentCategoryInterestMapper momentCategoryInterestMapper;

    public void insert(MomentCategoryInterestData data) {
        momentCategoryInterestMapper.insert(data);
    }

    public void update(MomentCategoryInterestData data) {
        momentCategoryInterestMapper.updateById(data);
    }

    public MomentCategoryInterestData selectById(Integer id) {
        return momentCategoryInterestMapper.selectById(id);
    }


    public void updateById(MomentCategoryInterestData data) {
        momentCategoryInterestMapper.updateById(data);
    }

    public void deleteById(Integer id) {
        momentCategoryInterestMapper.deleteById(id);
    }


    public Collection<Integer> getAllCategoryByInterestSet(List<Integer> interestSet) {
        if (CollectionUtils.isEmpty(interestSet)) {
            return Collections.emptyList();
        }
        QueryWrapper<MomentCategoryInterestData> query = Wrappers.query();
        query.lambda().in(MomentCategoryInterestData::getInterestId, interestSet).select(MomentCategoryInterestData::getCategoryId);
        List<MomentCategoryInterestData> familyDataList = momentCategoryInterestMapper.selectList(query);
        return CollectionUtil.listToPropertySet(familyDataList, MomentCategoryInterestData::getCategoryId);
    }

    public List<MomentCategoryInterestData> selectCategoryInterestList(Integer categoryId) {
        QueryWrapper<MomentCategoryInterestData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("category_id", categoryId);
        return momentCategoryInterestMapper.selectList(queryWrapper);
    }

    public MomentCategoryInterestData selectByCategoryAndLabel(int categoryId, int labelId) {
        QueryWrapper<MomentCategoryInterestData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("category_id", categoryId);
        queryWrapper.eq("interest_id", labelId);
        return momentCategoryInterestMapper.selectOne(queryWrapper);
    }

}
