package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.cache.CacheMap;
import com.quhong.enums.ApiResult;
import com.quhong.mysql.data.ActorPayExternalData;
import com.quhong.mysql.data.WhiteTestData;
import com.quhong.mysql.mapper.ustar.WhiteTestMapper;
import com.quhong.utils.CollectionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;

@Component
@Lazy
public class WhiteTestDao {

    private static final Logger logger = LoggerFactory.getLogger(WhiteTestDao.class);
    public static final Integer WHITE_TYPE_RID = 0;
    public static final Integer WHITE_TYPE_TN_ID = 1;
    public static final Integer WHITE_TYPE_ROOM_ID = 2;
    public static final List<Integer> WHITE_TYPE_USER = Arrays.asList(WHITE_TYPE_RID, WHITE_TYPE_TN_ID);
    @Resource
    private WhiteTestMapper whiteTestMapper;

    private final CacheMap<Integer, Set<String>> allSetCacheMap;

    public WhiteTestDao() {
        allSetCacheMap = new CacheMap<>(5 * 60 * 1000L);
    }


    public int insertOne(WhiteTestData data) {
        return whiteTestMapper.insert(data);
    }


    public int updateById(WhiteTestData data) {
        return whiteTestMapper.updateById(data);
    }

    public WhiteTestData selectById(Integer id) {
        return whiteTestMapper.selectById(id);
    }

    public WhiteTestData selectByWhiteIdType(String whiteId, Integer type) {
        QueryWrapper<WhiteTestData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("white_id", whiteId);
        queryWrapper.eq("type", type);
        queryWrapper.last("limit 1");
        return whiteTestMapper.selectOne(queryWrapper);
    }

    public WhiteTestData selectByTnId(String tnId) {
        QueryWrapper<WhiteTestData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tn_id", tnId);
        queryWrapper.eq("type", WHITE_TYPE_TN_ID);
        queryWrapper.last("limit 1");
        return whiteTestMapper.selectOne(queryWrapper);
    }

    public int deleteByTnId(String tnId, int type) {
        QueryWrapper<WhiteTestData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type", type);
        queryWrapper.eq("tn_id", tnId);
        return whiteTestMapper.delete(queryWrapper);
    }

    public int deleteByWhiteId(String whiteId, int type) {
        QueryWrapper<WhiteTestData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type", type);
        queryWrapper.eq("white_id", whiteId);
        return whiteTestMapper.delete(queryWrapper);
    }


    public IPage<WhiteTestData> selectPage(int type, String searchUid, int page, int pageSize) {
        IPage<WhiteTestData> dataPage = new Page<>(page == 0 ? 1 : page, pageSize == 0 ? 30 : pageSize);
        QueryWrapper<WhiteTestData> query = new QueryWrapper<>();
        query.in("type", type);

        if (!StringUtils.isEmpty(searchUid)) {
            query.eq("white_id", searchUid).or().eq("tn_id", searchUid);
        }
        query.orderByDesc("ctime");
        return whiteTestMapper.selectPage(dataPage, query);
    }

    public boolean isMemberByType(String whiteId, int type) {
        Set<String> allSet = getAllIdByTypeCache(type);
        return allSet.contains(whiteId);
    }

    public Set<String> getAllIdByTypeCache(int type) {
        Set<String> allSet = allSetCacheMap.getData(type);
        if (allSet == null) {
            allSet = getAllIdByType(type);
            allSetCacheMap.cacheData(type, allSet);
        }
        return allSet;
    }

    public Set<String> getAllIdByType(int type) {
        Set<String> resultSet = null;
        QueryWrapper<WhiteTestData> queryWrapper = new QueryWrapper<>();
        if (type == WHITE_TYPE_RID) {
            queryWrapper.select("white_id");
            queryWrapper.in("type", WHITE_TYPE_USER);
            resultSet = CollectionUtil.listToPropertySet(whiteTestMapper.selectList(queryWrapper), WhiteTestData::getWhiteId);
        } else if (type == WHITE_TYPE_ROOM_ID) {
            queryWrapper.select("white_id");
            queryWrapper.eq("type", WHITE_TYPE_ROOM_ID);
            resultSet = CollectionUtil.listToPropertySet(whiteTestMapper.selectList(queryWrapper), WhiteTestData::getWhiteId);
        } else if (type == WHITE_TYPE_TN_ID) {
            queryWrapper.select("tn_id");
            queryWrapper.eq("type", WHITE_TYPE_TN_ID);
            resultSet = CollectionUtil.listToPropertySet(whiteTestMapper.selectList(queryWrapper), WhiteTestData::getTnId);
        }
        return resultSet == null ? Collections.emptySet() : resultSet;
    }

}
