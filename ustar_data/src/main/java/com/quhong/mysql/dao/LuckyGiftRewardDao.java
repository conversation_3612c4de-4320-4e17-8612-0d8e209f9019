package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.LuckyGiftRewardData;
import com.quhong.mysql.mapper.ustar_log.LuckyGiftRewardMapper;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;


@Lazy
@Component
public class LuckyGiftRewardDao extends ServiceImpl<LuckyGiftRewardMapper, LuckyGiftRewardData> {

    private static final int PAGE_SIZE = 20;

    public List<LuckyGiftRewardData> findWinningUser() {
        return lambdaQuery()
                .orderByDesc(LuckyGiftRewardData::getCtime)
                .last("limit 40").list();
    }

    public List<LuckyGiftRewardData> findByUid(String uid) {
        return lambdaQuery()
                .eq(LuckyGiftRewardData::getUid, uid)
                .orderByDesc(LuckyGiftRewardData::getCtime).
                last("limit 30").list();
    }

    public void cleanUp() {
        int cleanupTime = DateHelper.getNowSeconds() - 30 * 24 * 60 * 60;
        QueryWrapper<LuckyGiftRewardData> queryWrapper = new QueryWrapper<>();
        queryWrapper.lt("ctime", cleanupTime);
        baseMapper.delete(queryWrapper);
    }

    public List<RankInfo> selectRankList() {
        int todayStartTimeSec = (int) (DateHelper.DEFAULT.getTodayStartTime() / 1000);
        return baseMapper.selectRankList(todayStartTimeSec);
    }

    public static class RankInfo {
        private String uid; // 中奖者uid
        private String name; // 中奖名字
        private String head; // 中奖头像
        private int reward; // 中奖信息

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public int getReward() {
            return reward;
        }

        public void setReward(int reward) {
            this.reward = reward;
        }
    }

}
