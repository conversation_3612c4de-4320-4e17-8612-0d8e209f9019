package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.mysql.data.UserRecommendNoticeData;
import com.quhong.mysql.mapper.ustar.UserRecommendNoticeMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

@Lazy
@Component
public class UserRecommendNoticeDao {

    private static final Logger logger = LoggerFactory.getLogger(UserRecommendNoticeDao.class);

    @Resource
    private UserRecommendNoticeMapper userRecommendNoticeMapper;

    @Cacheable(value = "userRecommendNoticeList", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<UserRecommendNoticeData> findAllConfigList() {
        QueryWrapper<UserRecommendNoticeData> queryWrapper = new QueryWrapper<>();
        return userRecommendNoticeMapper.selectList(queryWrapper);
    }

    public UserRecommendNoticeData selectOne(int id) {
        return userRecommendNoticeMapper.selectById(id);
    }

    /**
     * 分页查询，支持多条件过滤
     */
    public List<UserRecommendNoticeData> selectPage(int start, int pageSize, UserRecommendNoticeData condition) {
        IPage<UserRecommendNoticeData> page = new Page<>(start / pageSize + 1, pageSize);
        QueryWrapper<UserRecommendNoticeData> queryWrapper = new QueryWrapper<>();

        // 按条件过滤
        if (condition.getUserType() != null) {
            queryWrapper.eq("user_type", condition.getUserType());
        }
        if (condition.getGender() != null) {
            queryWrapper.eq("gender", condition.getGender());
        }
        if (condition.getArea() != null) {
            queryWrapper.eq("area", condition.getArea());
        }
        if (condition.getScene() != null) {
            queryWrapper.eq("scene", condition.getScene());
        }
        if (condition.getTriggerMethod() != null) {
            queryWrapper.eq("trigger_method", condition.getTriggerMethod());
        }
        if (condition.getPopLimit() != null) {
            queryWrapper.eq("pop_limit", condition.getPopLimit());
        }
        if (condition.getStatus() != null) {
            queryWrapper.eq("status", condition.getStatus());
        }

        // 按创建时间降序排序
        queryWrapper.orderByDesc("ctime");

        IPage<UserRecommendNoticeData> result = userRecommendNoticeMapper.selectPage(page, queryWrapper);
        return result.getRecords();
    }

    /**
     * 插入新记录
     */
    public int insert(UserRecommendNoticeData data) {
        return userRecommendNoticeMapper.insert(data);
    }

    /**
     * 更新记录
     */
    public int update(UserRecommendNoticeData data) {
        return userRecommendNoticeMapper.updateById(data);
    }

    /**
     * 删除记录
     */
    public int delete(int id) {
        return userRecommendNoticeMapper.deleteById(id);
    }

    /**
     * 检查用户群性别重叠
     */
    public List<UserRecommendNoticeData> checkGenderOverlap(Integer scene, Integer userType, String dayLimit, Integer area, Integer gender, Integer excludeId) {
        QueryWrapper<UserRecommendNoticeData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("scene", scene)
                   .eq("user_type", userType)
                   .eq("area", area)
                   .eq("status", 1); // 只检查有效记录

        if (StringUtils.hasText(dayLimit)) {
            queryWrapper.eq("day_limit", dayLimit);
        }

        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }

        // 检查性别重叠：如果已有全部(0)，不能再设置男(1)或女(2)；如果已有男或女，不能再设置全部
        if (gender == 0) {
            // 要设置为全部，检查是否已有男或女
            queryWrapper.in("gender", 1, 2);
        } else {
            // 要设置为男或女，检查是否已有全部
            queryWrapper.eq("gender", 0);
        }

        List<UserRecommendNoticeData> existingRecords = userRecommendNoticeMapper.selectList(queryWrapper);
        return existingRecords;
    }

    /**
     * 检查用户群地区重叠
     */
    public List<UserRecommendNoticeData> checkAreaOverlap(Integer scene, Integer userType, String dayLimit, Integer gender, Integer area, Integer excludeId) {
        QueryWrapper<UserRecommendNoticeData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("scene", scene)
                   .eq("user_type", userType)
                   .eq("gender", gender)
                   .eq("status", 1); // 只检查有效记录

        if (StringUtils.hasText(dayLimit)) {
            queryWrapper.eq("day_limit", dayLimit);
        }

        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }

        // 检查地区重叠：如果已有全部(0)，不能再设置大区(1)或小区(2)；如果已有大区或小区，不能再设置全部
        if (area == 0) {
            // 要设置为全部，检查是否已有大区或小区
            queryWrapper.in("area", 1, 2);
        } else {
            // 要设置为大区或小区，检查是否已有全部
            queryWrapper.eq("area", 0);
        }

        List<UserRecommendNoticeData> existingRecords = userRecommendNoticeMapper.selectList(queryWrapper);
        return existingRecords;
    }

}
