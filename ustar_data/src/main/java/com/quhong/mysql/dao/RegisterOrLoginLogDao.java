package com.quhong.mysql.dao;

import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.RegisterOrLoginLogData;
import com.quhong.mysql.mapper.ustar_log.RegisterOrLoginLogMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Lazy
@Component
public class RegisterOrLoginLogDao extends MonthShardingDao<RegisterOrLoginLogMapper> {
    private static final Logger logger = LoggerFactory.getLogger(RegisterOrLoginLogDao.class);

    public RegisterOrLoginLogDao() {
        super("t_register_login_log");
    }

    public void insert(RegisterOrLoginLogData registerOrLoginLogData) {
        String suffix = DateHelper.ARABIAN.getTableSuffix(DateHelper.formatDate(registerOrLoginLogData.getCtime()));
        createTable(suffix);
        tableMapper.insert(suffix, registerOrLoginLogData);
    }


}
