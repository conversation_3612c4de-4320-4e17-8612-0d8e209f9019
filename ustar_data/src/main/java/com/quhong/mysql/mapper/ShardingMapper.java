package com.quhong.mysql.mapper;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 分表的基类mapper
 */
public interface ShardingMapper {
    @Select("show tables like #{tableName}")
    String checkExists(@Param("tableName")String tableName);
    @Insert("create table if not exists ${tablePre}_${tableSuffix} like ${tablePre}")
    void createTable(@Param("tablePre")String tablePre, @Param("tableSuffix")String tableSuffix);
}
