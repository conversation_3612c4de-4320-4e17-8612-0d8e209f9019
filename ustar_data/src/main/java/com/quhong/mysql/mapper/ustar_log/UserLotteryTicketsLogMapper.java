package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.UserLotteryTicketsLogData;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2023/10/10
 */
public interface UserLotteryTicketsLogMapper extends ShardingMapper {

    @Insert("insert into t_user_lottery_tickets_log_${tableSuffix} (uid, changed, g_balance, r_balance, `desc`, ctime) " +
            "values (#{item.uid}, #{item.changed}, #{item.gBalance}, #{item.rBalance}, #{item.desc}, #{item.ctime})")
    void insert(@Param("tableSuffix") String suffix, @Param("item") UserLotteryTicketsLogData data);
}
