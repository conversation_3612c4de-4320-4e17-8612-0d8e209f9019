package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.CountData;
import com.quhong.mysql.data.RoomMicLogData;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface RoomMicLogMapper extends ShardingMapper {
    void insert(@Param("tableSuffix") String tableSuffix, @Param("item") RoomMicLogData msgStatData);

    List<CountData> selectUserMicTimeByHour(
            @Param("tableSuffix") String tableSuffix,
            @Param("startTime") int startTime,
            @Param("endTime") int endTime
    );
}
