package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.UserExpDetailData;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Param;

public interface UserExpDetailMapper extends ShardingMapper {
    void insert(@Param("tableSuffix") String tableSuffix, @Param("item") UserExpDetailData userExpDetailData);

    void update(@Param("tableSuffix") String tableSuffix, @Param("item") UserExpDetailData userExpDetailData);

    UserExpDetailData getByUid(@Param("tableSuffix") String tableSuffix, @Param("uid") String uid, @Param("dateStr") String dateStr);
}
