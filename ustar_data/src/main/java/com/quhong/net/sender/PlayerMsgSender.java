package com.quhong.net.sender;

import com.quhong.core.msg.ServerMsg;
import com.quhong.msg.MarsCacheMsg;
import com.quhong.msg.MarsServerMsg;
import com.quhong.net.cache.ActorMsgCache;
import com.quhong.net.cache.MsgCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

public class PlayerMsgSender extends AbstractMsgSender {
    private static final Logger logger = LoggerFactory.getLogger(PlayerMsgSender.class);
    @Autowired
    private ActorMsgCache msgCache;

    public PlayerMsgSender() {

    }

    @Override
    public boolean sendMsg(String uid, MarsServerMsg msg, boolean repeated) {
        if (msg == null) {
            logger.error("send msg error. msg is null. toUid={} repeated={}", uid, repeated);
            return false;
        }
        MarsCacheMsg cacheMsg;
        if(msg instanceof MarsCacheMsg){
            cacheMsg = (MarsCacheMsg)msg;
        }else{
            cacheMsg = MsgCache.createCacheMsg("", msg, msg.toBody(), repeated);
        }
        boolean result = doSendMsg(uid, cacheMsg, repeated);
        if(repeated) {
            msgCache.addMsg(uid, cacheMsg);
        }
        return result;
    }

    @Override
    public boolean reSendMsg(String toUid, MarsCacheMsg msg) {
        logger.info("resend msg. fromUid={} roomId={} toUid={} cmd={} msgId={}", msg.getFromUid(), msg.getRoomId(), toUid, msg.getCmd(), msg.getHeader().getMsgId());
        MarsCacheMsg cacheMsg = MsgCache.createCacheMsg(toUid, msg, msg.toBody(), true);
        return doSendMsg(toUid, cacheMsg, true);
    }
}
