package com.quhong.net.sender;

import com.quhong.core.msg.ServerMsg;
import com.quhong.msg.MarsCacheMsg;
import com.quhong.msg.MarsServerMsg;
import com.quhong.net.cache.MsgCache;
import com.quhong.net.cache.RoomMsgCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

public class RoomMsgSender extends AbstractMsgSender {
    private static final Logger logger = LoggerFactory.getLogger(RoomMsgSender.class);

    @Autowired
    private RoomMsgCache msgCache;

    public RoomMsgSender(){

    }

    @Override
    public boolean sendMsg(String toUid, MarsServerMsg msg, boolean repeated) {
        if (msg == null) {
            logger.error("send msg error. msg is null. toUid={}", toUid);
            return false;
        }
        MarsCacheMsg cacheMsg;
        if(msg instanceof MarsCacheMsg){
            cacheMsg = (MarsCacheMsg)msg;
        }else{
            cacheMsg = MsgCache.createCacheMsg("", msg, msg.toBody(), true);
        }
        boolean result = doSendMsg(toUid, cacheMsg, false);
        if(repeated) {
            msgCache.addMsg(toUid, cacheMsg);
        }
        return result;
    }

    @Override
    public boolean reSendMsg(String toUid, MarsCacheMsg msg) {
        logger.info("resend msg. fromUid={} roomId={} toUid={} cmd={} msgId={}", msg.getFromUid(), msg.getRoomId(), toUid, msg.getCmd(), msg.getHeader().getMsgId());
        MarsCacheMsg cacheMsg = MsgCache.createCacheMsg(toUid, msg, msg.toBody(), true);
        return doSendMsg(toUid, cacheMsg, false);
    }

    public void cacheMsg(String toUid, MarsCacheMsg msg){
        msgCache.addMsg(toUid, msg);
    }
}
