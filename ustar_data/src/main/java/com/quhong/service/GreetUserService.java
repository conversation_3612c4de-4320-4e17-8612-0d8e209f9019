package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.BaseInitData;
import com.quhong.data.GreetUserReplyData;
import com.quhong.data.GreetWeightData;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.mysql.dao.GreetUserDao;
import com.quhong.mysql.data.GreetUserData;
import com.quhong.mysql.data.GreetUserRecommendData;
import com.quhong.redis.NewGreetRedis;
import com.quhong.redis.NewRookieRoomRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.BitUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 打招呼推荐数据处理
 */
@Lazy
@Service
public class GreetUserService {
    private static final Logger logger = LoggerFactory.getLogger(GreetUserService.class);

    @Resource
    private GreetUserDao greetUserDao;
    @Resource
    private NewGreetRedis newGreetRedis;
    @Resource
    private BaseInitData baseInitData;
    @Resource
    private NewRookieRoomRedis newRookieRoomRedis;
    @Resource
    private ActorDao actorDao;

    /**
     * 计算资料完善度
     */
    public double calculateProgress(List<String> bannerList, List<Integer> labelList, String head, String name, String desc, String birthday, String country) {
        int process = 0;
        int bannerSize = CollectionUtils.isEmpty(bannerList) ? 0 : bannerList.size();
        int labelSize = CollectionUtils.isEmpty(labelList) ? 0 : labelList.size();
        int bannerCount = bannerSize * 5;
        int labelCount = Math.min(40, labelSize * 4);

        process += bannerCount;
        process += labelCount;
        if (!baseInitData.isSystemUrl(head)) {
            process += 5;
        }
        if (!StringUtils.isEmpty(desc)) {
            process += 12;
        }
        if (!StringUtils.isEmpty(name)) {
            process += 1;
        }
        if (!StringUtils.isEmpty(birthday)) {
            process += 1;
        }
        if (!StringUtils.isEmpty(country)) {
            process += 1;
        }
        return process * 0.01;
    }

    /**
     * 插入打招呼推荐用户基础数据
     */
    public void insertGreetUser(GreetUserData data) {
        GreetUserData greetUserData = greetUserDao.selectByUid(data.getUid());
        if (greetUserData != null) {
            return;
        }
        greetUserDao.insert(data);
    }

    /**
     * 插入打招呼推荐用户基础数据
     */
    public void insertGreetUserByUid(String uid) {
        GreetUserData greetUserData = greetUserDao.selectByUid(uid);
        if (greetUserData != null) {
            return;
        }
        ActorData registerData = actorDao.getActorDataFromCache(uid);
        int registerTime = new ObjectId(uid).getTimestamp();
        String countryCode = ActorUtils.getUpperCaseCountryCode(registerData.getCountry());
        int regionCode = ActorUtils.getRegionByCountryCode(countryCode);
        int currentTime = DateHelper.getNowSeconds();
        List<Integer> labelList = registerData.getLabelList();
        byte[] labelBit = BitUtils.tagsToBytes(labelList);
        double profileProcess = calculateProgress(new ArrayList<>(), labelList, registerData.getHead(), registerData.getName(), registerData.getDesc(), registerData.getBirthday(), registerData.getCountry());
        GreetUserData data = new GreetUserData(uid, registerData.getFb_gender(), registerTime, regionCode, countryCode,
                currentTime, JSONObject.toJSONString(labelList), labelBit, 0.0, "", "",
                profileProcess, 0, 0, currentTime, currentTime);
        greetUserDao.insert(data);
    }

    /**
     * 插入打招呼推荐用户基础数据
     */
    public void deleteGreetUserByUid(String uid) {
        GreetUserData greetUserData = greetUserDao.selectByUid(uid);
        if (greetUserData == null) {
            return;
        }
        greetUserDao.delete(greetUserData.getId());
    }

    /**
     * 更新打招呼推荐用户基础数据
     */
    public void updateGreetUser(GreetUserData data) {
        GreetUserData greetUserData = greetUserDao.selectByUid(data.getUid());
        if (greetUserData == null) {
            return;
        }
        if(data.getGender() != null){
            greetUserData.setGender(data.getGender());
        }

        if(data.getRegisterTime() != null){
            greetUserData.setRegisterTime(data.getRegisterTime());
        }
        if(data.getCountryCode() != null){
            greetUserData.setCountryCode(data.getCountryCode());
            greetUserData.setRegion(ActorUtils.getRegionByCountryCode(data.getCountryCode()));
        }
        if(data.getOnlineTime() != null){
            greetUserData.setOnlineTime(data.getOnlineTime());
        }
        if(data.getLabelInfo() != null){
            greetUserData.setLabelInfo(data.getLabelInfo());
            List<Integer> labelList = JSON.parseArray(data.getLabelInfo(), Integer.class);
            greetUserData.setLabelBit(BitUtils.tagsToBytes(labelList));
        }
        if(data.getProfileProcess() != null){
            greetUserData.setProfileProcess(data.getProfileProcess());
        }
        greetUserData.setMtime(DateHelper.getNowSeconds());
        greetUserData.setOnlineTime(DateHelper.getNowSeconds());
        greetUserDao.update(greetUserData);
    }

    /**
     * 更新打招呼推荐用户回复率
     */
    @Async("asyncTask")
    public void updateGreetReplyRate(String uid, String aid) {
        int currentTime = DateHelper.getNowSeconds();
        GreetUserData uidUserData = greetUserDao.selectByUid(uid);
        if (uidUserData != null) {
            List<GreetUserReplyData> replyList = ObjectUtils.isEmpty(uidUserData.getReplyInfo()) ? new ArrayList<>() : JSON.parseArray(uidUserData.getReplyInfo(), GreetUserReplyData.class);
            List<GreetUserReplyData> receiveGreetList = ObjectUtils.isEmpty(uidUserData.getReceiveGreetInfo()) ? new ArrayList<>() : JSON.parseArray(uidUserData.getReceiveGreetInfo(), GreetUserReplyData.class);
            // 剔除24小时以外的数据
            replyList.removeIf(item -> currentTime - item.getMtime() > 24 * 3600);
            // 设置回复列表
            boolean isReply = receiveGreetList.stream().anyMatch(item -> item.getUid().equals(aid));
            logger.info("updateGreetReplyRate uid:{} aid:{} replyList:{} receiveGreetList:{}, isReply:{}", uid, aid, JSONObject.toJSONString(replyList), JSONObject.toJSONString(receiveGreetList), isReply);
            if (isReply) {
                if (replyList.stream().anyMatch(item -> item.getUid().equals(aid))) {
                    replyList.stream().filter(item -> item.getUid().equals(aid)).findFirst().ifPresent(item -> item.setMtime(currentTime));
                } else {
                    replyList.add(new GreetUserReplyData(aid, currentTime));
                }
                int replySize = replyList.size();
                int receiveGreetSize = receiveGreetList.size();
                uidUserData.setReplyRate(receiveGreetSize == 0 ? 0 : (double) replySize / receiveGreetSize);
                uidUserData.setReplyInfo(JSON.toJSONString(replyList));
                greetUserDao.update(uidUserData);
            }
        }

        GreetUserData aidUserData = greetUserDao.selectByUid(aid);
        if (aidUserData != null){
            List<GreetUserReplyData> replyList = ObjectUtils.isEmpty(aidUserData.getReplyInfo()) ? new ArrayList<>() : JSON.parseArray(aidUserData.getReplyInfo(), GreetUserReplyData.class);
            List<GreetUserReplyData> receiveGreetList = ObjectUtils.isEmpty(aidUserData.getReceiveGreetInfo()) ? new ArrayList<>() : JSON.parseArray(aidUserData.getReceiveGreetInfo(), GreetUserReplyData.class);
            // 剔除24小时以外的数据
            receiveGreetList.removeIf(item -> currentTime - item.getMtime() > 24 * 3600);
            // 设置打招呼列表
            if (receiveGreetList.stream().anyMatch(item -> item.getUid().equals(uid))) {
                receiveGreetList.stream().filter(item -> item.getUid().equals(uid)).findFirst().ifPresent(item -> item.setMtime(currentTime));
            }else {
                receiveGreetList.add(new GreetUserReplyData(uid, currentTime));
            }
            int replySize = replyList.size();
            int receiveGreetSize = receiveGreetList.size();
            aidUserData.setReceiveGreetInfo(JSON.toJSONString(receiveGreetList));
            aidUserData.setReplyRate(receiveGreetSize == 0 ? 0 : (double) replySize / receiveGreetSize);
            greetUserDao.update(aidUserData);
        }
    }


    /**
     * 获取用户类型
     */
    private int getUserType(String uid) {
        Set<String> rookieDataSet = newRookieRoomRedis.getNewRookieUserByRedis();
        if (rookieDataSet.contains(uid)) {
            return 1;
        }
        int registerDay = ActorUtils.getRegDays(uid);
        if (registerDay <= 1) {
            return 2;
        } else if (registerDay <= 7) {
            return 3;
        } else if (registerDay <= 30) {
            return 4;
        } else {
            return 5;
        }
    }

    /**
     * 获取打招呼推荐用户权重
     */
    public GreetWeightData getGreetWeightData(String uid) {
        int userType = getUserType(uid);
        return newGreetRedis.getGreetUserWeight().stream().filter(data -> data.getUserType() == userType).findFirst().orElse(null);
    }

    public List<GreetUserRecommendData> getGreetUserRecommendList(ActorData actorData, GreetWeightData weightData) {
        if (weightData == null) {
            return Collections.emptyList();
        }
        String countryCode = ActorUtils.getUpperCaseCountryCode(actorData.getCountry());
        List<GreetUserRecommendData> recommendGreetList = newGreetRedis.getGreetUserRecommend(countryCode);
        if (recommendGreetList != null) {
            return recommendGreetList;
        }else {
            int regionCode = ActorUtils.getRegionByCountryCode(countryCode);
            byte[] labelBit = BitUtils.tagsToBytes(actorData.getLabelList());
            recommendGreetList = greetUserDao.selectRecommendList(regionCode, countryCode, labelBit, weightData.getRegisterWeight(), weightData.getRegionWeight(), weightData.getOnlineWeight(),
                    weightData.getInterestWeight(), weightData.getReplyWeight(), weightData.getProfileWeight(), 0, 2000);
            newGreetRedis.setGreetUserRecommend(countryCode, recommendGreetList);
            return recommendGreetList;
        }
    }

    public List<GreetUserRecommendData> getGreetUserRecommendListByCountry(ActorData actorData, GreetWeightData weightData, String countryCode, int page, int size) {
        if (weightData == null) {
            return Collections.emptyList();
        }
        String filterCountryCode = countryCode;
        if ("ALL".equals(countryCode)) {
            countryCode = ActorUtils.getUpperCaseCountryCode(actorData.getCountry());
        }
        int start = page > 1 ? (page - 1) * size : 0;
        int regionCode = ActorUtils.getRegionByCountryCode(countryCode);
        byte[] labelBit = BitUtils.tagsToBytes(actorData.getLabelList());
        return greetUserDao.selectRecommendListByCountryCode(regionCode, countryCode, filterCountryCode, labelBit, weightData.getRegisterWeight(),
                weightData.getRegionWeight(), weightData.getOnlineWeight(), weightData.getInterestWeight(),
                weightData.getReplyWeight(), weightData.getProfileWeight(), start, size);
    }

}
