package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.vo.BannerUserInfo;
import com.quhong.data.vo.HomeBanner;
import com.quhong.data.vo.LimitedRecharge;
import com.quhong.data.vo.RoomMetaBanner;
import com.quhong.enums.ClientOS;
import com.quhong.enums.PKGConstant;
import com.quhong.enums.SLangType;
import com.quhong.handler.HttpEnvData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorConfigDao;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.IndexBannerDao;
import com.quhong.mongo.dao.RoomBannerDao;
import com.quhong.mongo.data.IndexBannerData;
import com.quhong.mongo.data.RoomBannerData;
import com.quhong.mysql.dao.ActorPayExternalDao;
import com.quhong.redis.BackUserStateRedis;
import com.quhong.redis.GameKingRedis;
import com.quhong.redis.GameRoomRedis;
import com.quhong.utils.ActorUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * 主页banner统一方法
 */
@Lazy
@Service
public class HomeBannerService {
    private static final Logger logger = LoggerFactory.getLogger(HomeBannerService.class);
    private static final String GAME_KING_BANNER = "game_king";
    public static final int BACK_DAY = 15;
    public static final int BACK_DAY_TIME = BACK_DAY * 86400;
    private static final List<String> BC_GAME_LIST = Arrays.asList("slots", "angel_or_devil", "horse_racing", "crash", "fishing", "fast3");
    public static final int SHOW_GAME_RANK_BEAN = 30000;
    public static final int SHOW_GAME_RANK_TIME = 1743627600;
    private static final List<String> SHOW_GAME_RANK_UID_LIST = Arrays.asList("676ac00ee47f6b1b13437b25", "635e7fa60adfa9310ea6ddac", "5c88a0f166dc630038467c4e", "63a068b7f51d773645a013ac");
    public static final Set<String> GCC_SET = new HashSet<>(Arrays.asList("sa", "kw", "ae", "qa", "om", "bh", "us"));

    // 首充用户充值优惠Redis key
    private static final String LIMITED_RECHARGE_KEY = "limited_recharge_benefit";

    // banner下发过滤类型
    public static final int FILTER_TYPE_SEX = 1;            // 1: 性别过滤
    public static final int FILTER_TYPE_PAY = 2;            // 2: 付费过滤
    public static final int FILTER_TYPE_REGISTER = 3;       // 3: 注册天数过滤
    public static final int FILTER_TYPE_USER = 4;           // 4：指定用户展示
    public static final int FILTER_TYPE_LOSS = 5;           // 5：指定流失回流用户
    public static final int FILTER_TYPE_BC_GAME = 6;        // 6: bcGame开启才有
    public static final int FILTER_TYPE_COUNTRY = 7;        // 7: 指定国家过滤
    public static final int FILTER_TYPE_LIMITED_RECHARGE = 8; // 8: 限时充值福利活动次日过滤,只有首页弹窗用

    @Resource
    private IndexBannerDao indexBannerDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private GameKingRedis gameKingRedis;
    @Resource
    private ActorPayExternalDao actorPayExternalDao;
    @Resource
    private BackUserStateRedis backUserStateRedis;
    @Resource
    private RoomBannerDao roomBannerDao;
    @Resource
    private ActorConfigDao actorConfigDao;
    @Resource
    private GameRoomRedis gameRoomRedis;
    @Resource
    private CoreCacheDataService coreCacheDataService;

    // moment、主页banner、 payBanner、 任务页Banner、 个人主页 banner
    // isShowChargeOffer -> 只有payBanner使用
    @Cacheable(value = "getHomeBannerList", key = "T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<HomeBanner> getHomeBannerList(String uid, int bannerType, int slang, int versioncode, String packageName, int os, boolean isShowChargeOffer) {
        List<IndexBannerData> indexBannerList = indexBannerDao.findAllData(bannerType);
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        List<HomeBanner> list = new ArrayList<>();
        int currentTime = DateHelper.getNowSeconds();
        int area = slang == SLangType.ENGLISH ? 1 : 2;
        String lang = slang != SLangType.ENGLISH ? "ar" : "en_US";
        int bcGameSwitch = getBCGameSwitch(actorData);
        for (IndexBannerData data : indexBannerList) {
            String url = data.getUrl();
            int startTime = data.getStartTime();
            int endTime = data.getEndTime();


            if (startTime > 0 && endTime > 0 && (currentTime < startTime || currentTime > endTime)) {
                if (currentTime > endTime) {
                    Update update = new Update();
                    update.set("valid", 0);
                    indexBannerDao.updateData(data.get_id().toString(), update);
                }
                continue;
            }

            // 有效性判断
            if (!isValidUrl(url, versioncode, packageName, slang)) {
                continue;
            }

            // 过滤条件判断
            if (!checkFilterValid(actorData, data, bcGameSwitch)) {
                continue;
            }

            if (url.startsWith("https://docs.google.com/forms")) {
                String rid = "";
                if (!StringUtils.isEmpty(uid)) {
                    rid = String.valueOf(actorData.getRid());
                }
                url = url.replace("require_rid", rid);
            }

            if (bannerType != IndexBannerDao.TYPE_MOMENT) {
                url = dealWithUrl(url, uid, area, versioncode, os, lang);
            }

            if (url.contains("/RechargeReward") && !isShowChargeOffer) {
                continue;
            }

            HomeBanner banner = new HomeBanner();
            banner.setBannerId(data.get_id().toString());
            banner.setTitle(slang == SLangType.ENGLISH ? data.getTitle() : data.getTitle_ar());
            banner.setPreview(slang == SLangType.ENGLISH ? data.getPreview() : data.getPreview_ar());
            banner.setUrl(url);
            banner.setTurnCode(data.getTurn_code());
            banner.setAtype(data.getAtype());
            banner.setRoom_id(data.getActionValue());
            banner.setAid(data.getActionValue());
            if (bannerType == IndexBannerDao.TYPE_DAILY_TASK) {
                banner.setWebType(data.getWeb_type());
            } else {
                banner.setWeb_type(data.getWeb_type());
            }
            banner.setGame_type(data.getGame_type());
            banner.setDataVersion(String.valueOf(data.getDataVersion()));
            banner.setActionValue(data.getActionValue());

            if (GAME_KING_BANNER.equals(data.getGame_type())) {
                banner.setShowUserInfo(1);
                banner.setUserInfoList(getGameKingLastWeekTop3());
            }
            list.add(banner);
        }
        return list;
    }

    public boolean showGameKingBanner(String uid, int currentTime) {
        if (currentTime >= SHOW_GAME_RANK_TIME) {
            return true;
        }
        if (SHOW_GAME_RANK_UID_LIST.contains(uid)) {
            return true;
        }

        int curWeekNum = gameKingRedis.getCurWeekNum();
        int lastWeekNum = curWeekNum - 1;
        int lastWeekScore = gameKingRedis.getRankingScore(uid, lastWeekNum);
        int curWeekScore = gameKingRedis.getRankingScore(uid, curWeekNum);
        int totalScore = lastWeekScore + curWeekScore;
        return totalScore > SHOW_GAME_RANK_BEAN;
    }


    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<BannerUserInfo> getGameKingLastWeekTop3() {
        List<BannerUserInfo> list = new ArrayList<>();
        int lastWeekNum = gameKingRedis.getLastWeekNum();
        Map<String, Integer> rankingMap = gameKingRedis.getRankingMap(lastWeekNum, 3);
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            ActorData userData = actorDao.getActorDataFromCache(entry.getKey());
            if (userData == null) {
                continue;
            }
            list.add(new BannerUserInfo(userData.getName(), ImageUrlGenerator.generateRoomUserUrl(userData.getHead()), userData.getRid() + "", userData.getRidData()));
        }
        return list;
    }


    private String dealWithUrl(String url, String uid, int nlang, int versioncode, int os, String lang) {
        if (StringUtils.isEmpty(url)||!url.startsWith("http")) {
            return "";
        }

        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(url);
        urlBuilder.queryParam("uid", uid);
        urlBuilder.queryParam("nlang", nlang);
        urlBuilder.queryParam("versioncode", versioncode);
        urlBuilder.queryParam("os", os);
        urlBuilder.queryParam("lang", lang);
        return urlBuilder.build(false).encode().toUriString();
    }

    private int strToInt(String strValue) {
        if (StringUtils.isEmpty(strValue)) {
            return 0;
        }
        try {
            return Integer.parseInt(strValue);
        } catch (Exception e) {
            logger.error("string to integer error. value={}", strValue);
        }
        return 0;
    }


    private boolean isValidUrl(String url, int versioncode, String packageName, int slang) {
        Map<String, String> paramMap = urlSplit(url);
        if (!CollectionUtils.isEmpty(paramMap)) {
            if (paramMap.containsKey("android_limit")) {
                int androidLimit = strToInt(paramMap.get("android_limit"));
                if (PKGConstant.ANDROID_YOUSTAR.equals(packageName) && versioncode < androidLimit) {
                    return false;
                }
            }
            if (paramMap.containsKey("ios_limit")) {
                int iosLimit = strToInt(paramMap.get("ios_limit"));
                if (PKGConstant.IOS_MAIN.equals(packageName) && versioncode < iosLimit) {
                    return false;
                }
            }
            if (paramMap.containsKey("pro_limit")) {
                int proLimit = strToInt(paramMap.get("pro_limit"));
                if (PKGConstant.ANDROID_YOUSTAR_LITE.equals(packageName) && versioncode < proLimit) {
                    return false;
                }
            }
            if (paramMap.containsKey("startTime") && paramMap.containsKey("endTime")) {
                int nowTime = DateHelper.getNowSeconds();
                int startTime = strToInt(paramMap.get("startTime"));
                int endTime = strToInt(paramMap.get("endTime"));
                if (nowTime < startTime || nowTime > endTime) {
                    return false;
                }
            }
            if (paramMap.containsKey("showEnv")) {
                int showEnv = strToInt(paramMap.get("showEnv"));
                return slang == showEnv;
            }
        }
        return true;
    }

    private Map<String, String> urlSplit(String url) {
        Map<String, String> mapRequest = new HashMap<>();
        String strUrlParam = TruncateUrlPage(url);
        if (strUrlParam == null) {
            return mapRequest;
        }
        String[] arrSplit = strUrlParam.split("[&]");
        for (String strSplit : arrSplit) {
            String[] arrSplitEqual = strSplit.split("[=]");
            if (arrSplitEqual.length > 1) {
                mapRequest.put(arrSplitEqual[0], arrSplitEqual[1]);
            } else {
                if (!"".equals(arrSplitEqual[0])) {
                    mapRequest.put(arrSplitEqual[0], "");
                }
            }
        }
        return mapRequest;
    }

    private String TruncateUrlPage(String url) {
        String strAllParam = null;
        url = url.trim();
        String[] arrSplit = url.split("[?]");
        if (url.length() > 1) {
            if (arrSplit.length > 1) {
                for (int i = 1; i < arrSplit.length; i++) {
                    strAllParam = arrSplit[i];
                }
            }
        }
        return strAllParam;
    }


    public boolean checkFilterValid(ActorData actorData, IndexBannerData data, int bcGameSwitch) {

        try {
            switch (data.getFilterType()) {
                case HomeBannerService.FILTER_TYPE_SEX:
                    if (!StringUtils.isEmpty(data.getFilterItem()) && !data.getFilterItem().equals(String.valueOf(actorData.getFb_gender()))) {
                        return false;
                    }
                    break;
                case HomeBannerService.FILTER_TYPE_PAY:
                    double userRecharge = actorPayExternalDao.getUserRechargeMoney(actorData.getUid()).doubleValue();
                    String[] split = data.getFilterItem().split("-");
                    double minMoney = Double.parseDouble(split[0]);
                    double maxMoney = Double.parseDouble(split[1]);
                    if (userRecharge < minMoney || userRecharge > maxMoney) {
                        return false;
                    }
                    break;

                case HomeBannerService.FILTER_TYPE_REGISTER:
                    String[] splitDay = data.getFilterItem().split("-");
                    int startDay = Integer.parseInt(splitDay[0]);
                    int endDay = Integer.parseInt(splitDay[1]);
                    int regDay = ActorUtils.getRegDays(actorData.getUid());
                    if (regDay < startDay || regDay > endDay) {
                        return false;
                    }
                    break;
                case HomeBannerService.FILTER_TYPE_USER:
                    String[] uidArray = data.getFilterItem().split(",");
                    List<String> uidList = Arrays.asList(uidArray);
                    if (!uidList.contains(actorData.getUid())) {
                        return false;
                    }
                    break;
                case HomeBannerService.FILTER_TYPE_LOSS:
//                    int backDay = Integer.parseInt(data.getFilterItem().trim());
                    if (backUserStateRedis.isReg(actorData.getUid()) ||
                            backUserStateRedis.isBackUser(actorData, true) == 0) {
                        //  不是回归用户
                        return false;
                    }
                    break;
                case HomeBannerService.FILTER_TYPE_BC_GAME:
                    int filterStatus = Integer.parseInt(data.getFilterItem().trim());
                    if ((filterStatus == 0 && bcGameSwitch > 0) || (filterStatus == 1 && bcGameSwitch <= 0)) {
                        return false;
                    }
                    break;
                case HomeBannerService.FILTER_TYPE_COUNTRY:
                    if (!StringUtils.isEmpty(data.getFilterItem()) && !StringUtils.isEmpty(actorData.getCountry())) {
                        String[] countryArray = data.getFilterItem().split(",");
                        List<String> countryList = Arrays.asList(countryArray);
                        String countryCode = ActorUtils.getUpperCaseCountryCode(actorData.getCountry());
                        if (!StringUtils.isEmpty(countryCode) && !countryList.contains(countryCode)) {
                            return false;
                        }
                    }
                    break;
                case HomeBannerService.FILTER_TYPE_LIMITED_RECHARGE:
                    // 首笔充值活动用户次日打开App触发该活动首页弹窗
                    LimitedRecharge limitedRechargeInfo = coreCacheDataService.getLimitedRechargeInfoCache(LIMITED_RECHARGE_KEY, actorData.getUid());
                    if (limitedRechargeInfo.getFirstRechargeTime() <= 0) {
                        // 用户未完成首笔充值，不显示banner
                        return false;
                    }

                    int currentTime = DateHelper.getNowSeconds();
                    int firstRechargeTime = limitedRechargeInfo.getFirstRechargeTime();
                    // 计算首笔充值次日的开始时间（首笔充值时间 + 24小时）
                    int nextDayStartTime = firstRechargeTime + 24 * 3600;
                    // 计算首笔充值次日的结束时间（首笔充值时间 + 48小时）
                    int nextDayEndTime = firstRechargeTime + 48 * 3600;

                    // 只有在首笔充值次日（24-48小时内）才显示banner
                    if (currentTime < nextDayStartTime || currentTime >= nextDayEndTime) {
                        return false;
                    }
                    break;
            }
        } catch (Exception e) {
            logger.error("checkBannerValid IndexBannerData:{}, error:{}, ", JSONObject.toJSONString(data), e.getMessage(), e);
        }
        return true;
    }

    public List<RoomMetaBanner> getRoomBannerList(int sideBar, HttpEnvData dto) {

        String uid = dto.getUid();
        int os = dto.getOs();
        int versionCode = dto.getVersioncode();
        String packageName = dto.getApp_package_name();
        int slang = dto.getSlang();
        String roomId = dto.getRoomId();

        List<RoomBannerData> bannerList = roomBannerDao.getRoomBannerBySidebar(sideBar);
        if (CollectionUtils.isEmpty(bannerList)) {
            return Collections.emptyList();
        }
        List<RoomMetaBanner> list = new ArrayList<>();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        int currentTime = DateHelper.getNowSeconds();
        for (RoomBannerData data : bannerList) {
            int startTime = data.getStartTime();
            int endTime = data.getEndTime();
            if (startTime > 0 && endTime > 0 && (currentTime < startTime || currentTime > endTime)) {
                continue;
            }

            String url = data.getUrl() != null ? data.getUrl() : "";
            if (!isRoomBannerValidUrl(url, roomId, os, versionCode, packageName, slang)) {
                continue;
            }

            if (url.startsWith("https://docs.google.com/forms")) {
                String rid = "";
                if (!StringUtils.isEmpty(uid)) {
                    rid = actorData.getStrRid();
                }
                url = url.replace("require_rid", rid);
            }

            RoomMetaBanner bannerConfig = new RoomMetaBanner();
            bannerConfig.setWebType(data.getWeb_type());
            bannerConfig.setType(data.getType());
            bannerConfig.setTitle(slang == SLangType.ENGLISH ? data.getTitle() : data.getTitle_ar());
            bannerConfig.setEvent(data.getEvent());
            bannerConfig.setGame_type(data.getGame_type());
            bannerConfig.setIcon(slang == SLangType.ENGLISH ? data.getIcon() : data.getIcon_ar());
            bannerConfig.setRoomIcon(slang == SLangType.ENGLISH ? data.getRoom_icon() : data.getRoom_icon_ar());
            bannerConfig.setUrl(getBannerUrl(url, uid, slang, os, versionCode, data.getGame_type()));
            bannerConfig.setBannerType(data.getBannerType());
            bannerConfig.setBannerId(data.get_id().toString());
            bannerConfig.setIsNew(DateHelper.getNowSeconds() - data.get_id().getTimestamp() < TimeUnit.DAYS.toSeconds(3) ? 1 : 0);
            bannerConfig.setHot(data.getHot());

            if (GAME_KING_BANNER.equals(data.getGame_type())) {
                bannerConfig.setShowUserInfo(1);
                bannerConfig.setUserInfoList(getGameKingLastWeekTop3());
            }

            list.add(bannerConfig);
        }
        return list;
    }

    private String getBannerUrl(String url, String uid, int slang, int os, int versioncode, String gameType) {
        if (StringUtils.isEmpty(url)) {
            return "";
        }
        UriComponentsBuilder urlBuilder;
        try {
            urlBuilder = UriComponentsBuilder.fromHttpUrl(url.trim());
        } catch (Exception e) {
            logger.error("getBannerUrl error. url={} {}", url, e.getMessage(), e);
            return "";
        }
        urlBuilder.queryParam("uid", uid);
        urlBuilder.queryParam("nlang", slang);
        urlBuilder.queryParam("os", os);
        urlBuilder.queryParam("versioncode", versioncode);
        // urlBuilder.queryParam("token", token);
        if (BC_GAME_LIST.contains(gameType) && !url.contains("appId")) {
            urlBuilder.queryParam("appId", ServerConfig.isProduct() ? 1476635168L : 2135757682L);
        }
        return urlBuilder.build(false).encode().toUriString();
    }


    private Boolean isRoomBannerValidUrl(String url, String roomId, int os, int versioncode, String packageName, int slang) {

        Map<String, String> paramMap = urlSplit(url);
        if (!CollectionUtils.isEmpty(paramMap)) {
            if (paramMap.containsKey("android_hidden")) {
                int androidHidden = strToInt(paramMap.get("android_hidden"));
                if (PKGConstant.ANDROID_YOUSTAR.equals(packageName) && versioncode == androidHidden) {
                    return false;
                }
            }
            if (paramMap.containsKey("android_pro_hidden")) {
                int androidHidden = strToInt(paramMap.get("android_pro_hidden"));
                if (PKGConstant.ANDROID_YOUSTAR_LITE.equals(packageName) && versioncode == androidHidden) {
                    return false;
                }
            }

            if (paramMap.containsKey("ios_hidden")) {
                int iosHidden = strToInt(paramMap.get("ios_hidden"));
                if (os == ClientOS.IOS && versioncode == iosHidden) {
                    return false;
                }
            }

            if (paramMap.containsKey("android_limit")) {
                int androidLimit = strToInt(paramMap.get("android_limit"));
                if (PKGConstant.ANDROID_YOUSTAR.equals(packageName) && versioncode < androidLimit) {
                    return false;
                }
            }
            if (paramMap.containsKey("ios_limit")) {
                int iosLimit = strToInt(paramMap.get("ios_limit"));
                if (os == ClientOS.IOS && versioncode < iosLimit) {
                    return false;
                }
            }
            if (paramMap.containsKey("pro_limit")) {
                int proLimit = strToInt(paramMap.get("pro_limit"));
                if (PKGConstant.ANDROID_YOUSTAR_LITE.equals(packageName) && versioncode < proLimit) {
                    return false;
                }
            }
            if (paramMap.containsKey("android_up_limit")) {
                int androidLimit = strToInt(paramMap.get("android_up_limit"));
                if (PKGConstant.ANDROID_YOUSTAR.equals(packageName) && versioncode >= androidLimit) {
                    return false;
                }
            }
            if (paramMap.containsKey("ios_up_limit")) {
                int iosLimit = strToInt(paramMap.get("ios_up_limit"));
                if (os == ClientOS.IOS && versioncode >= iosLimit) {
                    return false;
                }
            }
            if (paramMap.containsKey("pro_up_limit")) {
                int proLimit = strToInt(paramMap.get("pro_up_limit"));
                if (PKGConstant.ANDROID_YOUSTAR_LITE.equals(packageName) && versioncode >= proLimit) {
                    return false;
                }
            }
            if (paramMap.containsKey("roomLimit") && !Objects.equals(paramMap.get("roomLimit"), roomId)) {
                return false;
            }
            if (paramMap.containsKey("startTime") && paramMap.containsKey("endTime")) {
                int nowTime = DateHelper.getNowSeconds();
                int startTime = strToInt(paramMap.get("startTime"));
                int endTime = strToInt(paramMap.get("endTime"));
                if (nowTime < startTime || nowTime > endTime) {
                    return false;
                }
            }
            if (paramMap.containsKey("showEnv")) {
                int showEnv = strToInt(paramMap.get("showEnv"));
                if (slang != showEnv) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * bc游戏总开关配置
     *
     * @param actorData: actor
     */
    public int getBCGameSwitch(ActorData actorData) {
        int defaultStatus = 1;
        try {
            String uid = actorData.getUid();
            String ipCodeCountry = actorData.getIpCodeCountry();
            if (ActorUtils.isNewRegisterActor(uid) && GCC_SET.contains(ActorUtils.getCountryCode(ipCodeCountry))) {
                defaultStatus = 0;
            }
            if (ActorUtils.isNewRegisterActor(uid, 30) && gameRoomRedis.getShowBcGameEntry(uid)) {
                defaultStatus = 0;
            }
            return actorConfigDao.getIntRoomConfig(uid, ActorConfigDao.ALL_BC_GAME_SWITCH, defaultStatus);
        } catch (Exception e) {
            logger.error("getBCGameSwitch error uid={} error={}", actorData.getUid(), e.getMessage(), e);
        }
        return defaultStatus;
    }

}
