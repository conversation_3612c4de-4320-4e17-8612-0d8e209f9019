package com.quhong.enums;

public class TruthDareV2Constant {

    // 游戏图标
    public static final String GAME_ICON = "https://cdn3.qmovies.tv/common/op_1739844587_icon-truthdare%402x.png";  // 游戏图标

    // 游戏模式
    public static final int MODE_RANDOM = 0;  // 随机
    public static final int MODE_CUSTOM = 1;  // 自定义

    // 游戏状态
    public static final int GAME_WAITING = 1;  // 等待其他人加入
    public static final int GAME_RUNNING = 2;  // 游戏进行中
    public static final int GAME_END = 0;  // 游戏已结束

    // 操作游戏消息
    public static final int GAME_CREATE_MSG = 1; // 创建游戏
    public static final int GAME_CLOSED_MSG = 2; // 关闭游戏
    public static final int GAME_TIME_OUT_MSG = 3; // 游戏超时关闭
    public static final int GAME_START_MSG = 4; // 开始游戏
    public static final int GAME_REMIND_MSG = 5;// 选择话题类型

    // 游戏最小人数
    public static final int GAME_MIN_USER = 2;

    // 房间外匹配游戏type
    public static final int GAME_MATCH_TYPE = 100;

    // 退出游戏代码
    public static final int EXIT_CODE_0 = 0;  //  游戏正常结束退出
    public static final int EXIT_CODE_1 = 1;  //  游戏超时，系统关闭
    public static final int EXIT_CODE_2 = 2;  //  创建者退出房间关闭
    public static final int EXIT_CODE_3 = 3;  //  创建者下麦关闭
    public static final int EXIT_CODE_4 = 4;  //  房主/副房主关闭
    public static final int EXIT_CODE_5 = 5;  //  创建者被踢下麦

}
