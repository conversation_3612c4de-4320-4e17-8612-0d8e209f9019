package com.quhong.enums;


public class ApiCode extends HttpCode {
    public static final HttpCode AUTH_ERROR = new HttpCode(2001, "No right to operate", "لا تستطيع لتشفيل");
    public static final HttpCode UID_EMPTY = new HttpCode(2002, "Uid is empty");

    public ApiCode(int code, String... langMsg) {
        super(code, langMsg);
    }

    public static HttpCode into(int code, String... langMsg) {
        return new HttpCode(code, langMsg);
    }

}
