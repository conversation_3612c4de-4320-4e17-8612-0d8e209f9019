package com.quhong.room.api.zego;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.timers.DelayTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.web.HttpResponseData;
import com.quhong.core.web.WebClient;
import com.quhong.datas.HttpResult;
import com.quhong.enums.ServerType;
import com.quhong.http.AsyncHttpClient;
import com.quhong.redis.RoomMicRedis;
import com.quhong.room.RoomStreamService;
import com.quhong.room.api.CheckCallback;
import com.quhong.room.api.MicMonitor;
import com.quhong.room.api.ThirdPartApi;
import com.quhong.room.api.data.MicChangeDTO;
import com.quhong.room.api.data.MicChangeVO;
import com.quhong.room.api.data.MsgDTO;
import com.quhong.room.api.zego.data.ZegoMsgDTO;
import com.quhong.room.api.zego.data.ZegoStreamDataVO;
import com.quhong.room.api.zego.data.ZegoStreamVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;
import java.util.concurrent.Semaphore;
import java.util.stream.Collectors;

@Lazy
@Service
public class ZegoApi extends MicMonitor implements ThirdPartApi {
    private static final Logger logger = LoggerFactory.getLogger(ZegoApi.class);
    private static final String BROADCAST_MSG_PATH = "https://rtc-api-bom.zego.im/?Action=SendBroadcastMessage";
    private static final String DELETE_STREAM = "https://rtc-api-bom.zego.im/?Action=DeleteStream";
    private static final String SIMPLE_STREAM_LIST = "https://rtc-api-bom.zego.im/?Action=DescribeSimpleStreamList";
    private static final String KICK_OUT = "https://rtc-api.zego.im/?Action=KickoutUser";
    private static final String KICK_OUT_V1 = "https://liveroom**********-api.zegocloud.com/cgi/kickout";
    private static final String KICK_OUT_V1_TEST = "https://test2-liveroom-api.zego.im/cgi/kickout";
    private static final String SEND_PEER_MSG = "https://zim-api.zego.im/?Action=SendPeerMessage";
    private static final String SEND_ROOM_MSG = "https://zim-api.zego.im/?Action=SendRoomMessage";
    private static final String SEND_MSG = "https://liveroom**********-api.zegocloud.com/cgi/sendmsg";
    private static final String SEND_MSG_TEST = "https://test2-liveroom-api.zego.im/cgi/sendmsg";
    private static final String GET_TOKEN = "https://liveroom**********-api.zegocloud.com/cgi/token";
    private static final String GET_TOKEN_TEST = "https://test2-liveroom-api.zego.im/cgi/token";
    private static final String GET_EFFECTS_LICENSE = "https://aieffectscgi-api.zego.im?Action=CgiDescribeEffectsLicense";

    private static String EFFECTS_LICENSE = "";
    private static String EFFECT_LICENSE_EXPIRE_DAY = "";
    private static int EFFECTS_LICENSE_RETRY_COUNT = 0;

    private static final Map<String, String> map = new HashMap<String, String>() {
        {
            put("Content-Type", "application/json;charset=utf-8");
            put("Authorization", "?");
        }
    };

    // 获取即构流数据的QPS控制，每秒100次
    private static final Semaphore semaphore = new Semaphore(100);

    @Value("${zego.app_id:**********}")
    private long appId;
    @Value("${zego.server_secret:u46e8y1g2ibw9m3kf5dnrxzlc4a7tvpq}")
    private String serverSecretTest;
    @Value("${zego.server_secret:8buw29afzicpjtkhgxravd15mse6473y}")
    private String serverSecretProd;
    @Resource
    private RoomMicRedis roomMicRedis;
    @Resource
    private AsyncHttpClient asyncHttpClient;
    @Resource
    private RoomStreamService roomStreamService;
    @Resource
    private WebClient webClient;

    private String getSecret() {
        if (ServerConfig.isNotProduct()) {
            return serverSecretTest;
        }
        return serverSecretProd;
    }

    /**
     * 向zego发送房间广播推送麦位信息
     * https://doc-zh.zego.im/article/8781
     */
    @Override
    public void micChange(MicChangeDTO dto) {
        try {
            UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(BROADCAST_MSG_PATH);
            urlBuilder.queryParam("RoomId", roomStreamService.genStreamId(dto.getRoomId()));
            // UserId必须为micChange，app通过该字段判断取值
            urlBuilder.queryParam("UserId", "micChange");
            urlBuilder.queryParam("MessageCategory", 1);
            urlBuilder.queryParam("MessageContent", JSON.toJSONString(new MicChangeVO(dto.getMicVersion())));
            generateSignature(urlBuilder);
            String url = urlBuilder.build(false).encode().toUriString();
            asyncHttpClient.get(url, null, data -> logger.info("send zego mic change roomId={} status={}", dto.getRoomId(), data.getStatus()));
        } catch (Exception e) {
            logger.error("send zego mic change error roomId={} msg={}", dto.getRoomId(), e.getMessage());
        }
    }

    /**
     * <a href="https://doc-zh.zego.im/article/8781">向zego发送房间广播推送保障信息</a>
     */
    @Override
    public void sendBroadcastMessage(String roomId, String type, String msg) {
        try {
            if (StringUtils.isEmpty(msg) || msg.length() > 888 || StringUtils.isEmpty(type)) {
                logger.info("send zego broadcast msg fail. type={} msg={}", type, msg);
                return;
            }
            UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(BROADCAST_MSG_PATH);
            urlBuilder.queryParam("RoomId", roomStreamService.genStreamId(roomId));
            urlBuilder.queryParam("UserId", type);
            urlBuilder.queryParam("MessageCategory", 1);
            urlBuilder.queryParam("MessageContent", msg);
            generateSignature(urlBuilder);
            String url = urlBuilder.build(false).encode().toUriString();
            asyncHttpClient.get(url, null, data -> logger.info("send zego broadcast msg roomId={} type={} status={}", roomId, type, data.getStatus()));
        } catch (Exception e) {
            logger.error("send zego broadcast msg error roomId={} type={} msg={}", roomId, type, e.getMessage());
        }
    }

    /**
     * 删除房间流
     * https://doc-zh.zego.im/article/8786
     */
    @Override
    public void deleteStream(String roomId, String uid, String thirdPartId) {
        try {
            UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(DELETE_STREAM);
            String streamId = roomStreamService.genStreamId(roomId);
            urlBuilder.queryParam("RoomId", streamId);
            // zego的第三方id暂时还是uid
            urlBuilder.queryParam("UserId", uid);
            urlBuilder.queryParam("StreamId", uid);
            generateSignature(urlBuilder);
            String url = urlBuilder.build(false).encode().toUriString();
            doDeleteStream(url, roomId, uid, streamId, thirdPartId, 2);
        } catch (Exception e) {
            logger.error("send zego delete stream msg error roomId={} uid={} error msg={}", roomId, uid, e.getMessage());
        }
    }

    /**
     * 删除房间流
     * https://doc-zh.zego.im/article/8786
     */
    public void doDeleteStream(String url, String roomId, String uid, String streamId, String thirdPartId, int resend) {
        try {
            asyncHttpClient.get(url, null, data -> {
                        logger.info("send zego delete stream msg roomId={} uid={} streamId={} thirdPartId={} status={}",
                                roomId, uid, streamId, thirdPartId, data.getStatus());
                        if (data.getStatus() != 200) {
                            if (resend > 0) {
                                doDeleteStream(url, roomId, uid, streamId, thirdPartId, resend - 1);
                            } else {
                                logger.info("zego delete stream error roomId={} uid={} streamId={} thirdPartId={} status={} body={}",
                                        roomId, uid, streamId, thirdPartId, data.getStatus(), data.getBody());
                                delStreamError("即构删流失败", roomId, uid, streamId, thirdPartId);
                            }
                        }
                    }
            );
        } catch (Exception e) {
            logger.error("send zego delete stream msg error roomId={} uid={} error msg={}", roomId, uid, e.getMessage());
        }
    }

    /**
     * 获取简易流列表
     * https://doc-zh.zego.im/article/11662
     */
    public void checkStreamListWithLimiting(String roomId, CheckCallback checkCallback) {
        try {
            UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(SIMPLE_STREAM_LIST);
            String streamId = roomStreamService.genStreamId(roomId);
            urlBuilder.queryParam("RoomId", streamId);
            generateSignature(urlBuilder);
            String url = urlBuilder.build(false).encode().toUriString();
            asyncHttpClient.get(url, null, data -> {
                String body = data.getBody();
                logger.info("get zego stream list roomId={} streamId={} status={} data={}", roomId, streamId, data.getStatus(), body);
                if (data.getStatus() != 200) {
                    logger.error("zego stream list error roomId={} status={} body={}", roomId, data.getStatus(), data.getBody());
                }
                if (StringUtils.isEmpty(body)) {
                    logger.error("get zego stream list error roomId={}", roomId);
                    return;
                }
                ZegoStreamVO zegoStreamVO = JSON.parseObject(body, ZegoStreamVO.class);
                if (null != zegoStreamVO && 0 == zegoStreamVO.getCode()) {
                    List<ZegoStreamDataVO> streamList = zegoStreamVO.getData().getStreamList();
                    Set<String> zegoSet = streamList.stream().map(ZegoStreamDataVO::getUserId).collect(Collectors.toSet());
                    Set<String> serverSet = roomMicRedis.getRoomMicSetRedis(roomId);
                    if (null != serverSet && zegoSet.hashCode() != serverSet.hashCode()) {
                        for (String aid : zegoSet) {
                            if (!serverSet.contains(aid)) {
                                logger.error("mic cross, roomId={} uid={} streamId={} micSet={}", roomId, aid, streamId, serverSet);
                                // zego的第三方id暂时还是uid
                                deleteStream(roomId, aid, aid);
                                // 串流报警
                                micCross("即构房间串流", roomId, aid, streamId, aid);
                                checkCallback.micCross(aid);
                            }
                        }
                    }
                }
            });
        } catch (Exception e) {
            logger.error("get zego stream list error roomId={} error msg={}", roomId, e.getMessage());
        }
    }

    /**
     * 以100QPS的方式获取即构流信息，失败不处理
     */
    @Override
    public void checkStreamList(String roomId, CheckCallback checkCallback) {
        try {
            if (semaphore.tryAcquire()) {
                checkStreamListWithLimiting(roomId, checkCallback);
                // 休眠1秒钟再释放
                TimerService.getService().addDelay(new DelayTask(1000) {
                    @Override
                    protected void execute() {
                        semaphore.release();
                    }
                });
            } else {
                logger.error("check stream try acquire fail roomId={}", roomId);
            }
        } catch (Exception e) {
            logger.error("checkStreamListWithLimiting error roomId={} {}", roomId, e.getMessage());
            semaphore.release();
        }
    }

    /**
     * 字节数组转16进制
     *
     * @param bytes 需要转换的byte数组
     * @return 转换后的Hex字符串
     */
    public String bytesToHex(byte[] bytes) {
        StringBuilder md5str = new StringBuilder();
        //把数组每一字节换成16进制连成md5字符串
        int digital;
        for (byte aByte : bytes) {
            digital = aByte;
            if (digital < 0) {
                digital += 256;
            }
            if (digital < 16) {
                md5str.append("0");
            }
            md5str.append(Integer.toHexString(digital));
        }
        return md5str.toString();
    }

    /**
     * https://doc-zh.zego.im/article/8985#2_1
     */
    public void generateSignature(UriComponentsBuilder urlBuilder) {
        String signatureNonce = UUID.randomUUID().toString().replace("-", "").substring(0, 16);
        int timestamp = DateHelper.getNowSeconds();
        String str = appId + signatureNonce + getSecret() + timestamp;
        String signature = "";
        try {
            //创建一个提供信息摘要算法的对象，初始化为md5算法对象
            MessageDigest md = MessageDigest.getInstance("MD5");
            //计算后获得字节数组
            byte[] bytes = md.digest(str.getBytes(StandardCharsets.UTF_8));
            //把数组每一字节换成16进制连成md5字符串
            signature = bytesToHex(bytes);
        } catch (Exception e) {
            logger.error("get signature error errorMsg={}", e.getMessage());
        }
        urlBuilder.queryParam("AppId", appId);
        urlBuilder.queryParam("SignatureNonce", signatureNonce);
        urlBuilder.queryParam("Timestamp", timestamp);
        urlBuilder.queryParam("Signature", signature);
        urlBuilder.queryParam("SignatureVersion", 2.0);
        urlBuilder.queryParam("IsTest", !ServerType.PRODUCT.equals(ServerConfig.getServerType()));
    }

    /**
     * <a href="https://doc-zh.zego.im/article/8784">从zego房间踢出用户</a>
     */
    @Override
    public void kickOut(String roomId, String uid) {
        try {
            UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(KICK_OUT);
            urlBuilder.queryParam("RoomId", roomStreamService.genStreamId(roomId));
            // UserId必须为micChange，app通过该字段判断取值
            urlBuilder.queryParam("UserId[]", uid);
            generateSignature(urlBuilder);
            String url = urlBuilder.build(false).encode().toUriString();
            asyncHttpClient.get(url, null, data -> logger.info("zego kick out roomId={} uid={} status={}", roomId, uid, data.getStatus()));
        } catch (Exception e) {
            logger.error("zego kick out error roomId={} uid={} msg={}", roomId, uid, e.getMessage());
        }
    }

    public String getZegoToken() {
        int expired = DateHelper.getNowSeconds() + 7200;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("ver", 1);
        jsonObject.put("hash", DigestUtils.md5DigestAsHex((appId + getSecret() + "123456787654" + expired).getBytes()));
        jsonObject.put("nonce", "123456787654");
        jsonObject.put("expired", expired);
        return Base64.getEncoder().encodeToString(jsonObject.toJSONString().getBytes(StandardCharsets.UTF_8));
    }

    public void kickOutV1(String roomId, String uid) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("access_token", getZegoToken());
            jsonObject.put("version", 1);
            jsonObject.put("seq", 1);
            jsonObject.put("room_id", roomStreamService.genStreamId(roomId));
            JSONArray jsonArray = new JSONArray();
            jsonArray.add(uid);
            jsonObject.put("kickout_user_account", jsonArray);
            asyncHttpClient.post(ServerConfig.isProduct() ? KICK_OUT_V1 : KICK_OUT_V1_TEST, jsonObject.toJSONString(), null, data -> {
                logger.info("zego v1 kick out roomId={} uid={} status={}", roomId, uid, data.getStatus());
            }, 1);
        } catch (Exception e) {
            logger.error("zego v1 kick out error roomId={} uid={}", roomId, uid, e);
        }
    }

    public void sendMsg(String roomId, String uid, JSONObject msg) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("access_token", getAccessToken());
            jsonObject.put("version", 1);
            jsonObject.put("seq", 1);
            jsonObject.put("room_id", roomStreamService.genStreamId(roomId));
            jsonObject.put("src_user_account", uid);
            jsonObject.put("msg_content", JSONObject.toJSONString(msg));
            asyncHttpClient.post(ServerConfig.isProduct() ? SEND_MSG : SEND_MSG_TEST, jsonObject.toJSONString(), null, data -> {
                logger.info("zego v1 send msg. roomId={} uid={} req={} status={}", roomId, uid, JSONObject.toJSONString(jsonObject), data.getStatus());
            }, 1);
        } catch (Exception e) {
            logger.error("zego v1 send msg error roomId={} uid={}", roomId, uid, e);
        }
    }

    public String getAccessToken() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("version", 1);
        jsonObject.put("seq", 1);
        jsonObject.put("app_id", appId);
        jsonObject.put("biz_type", 0);
        jsonObject.put("token", getZegoToken());
        String url = ServerConfig.isProduct() ? GET_TOKEN : GET_TOKEN_TEST;
        try {
            HttpResponseData<String> responseData = webClient.sendRestfulPost(url, JSON.toJSONString(jsonObject), map);
            String body = responseData.getBody();
            if (StringUtils.isEmpty(body)) {
                logger.error("get sud game list error, http status={}", responseData.getStatus());
                return "";
            }
            HttpResult httpResult = JSON.parseObject(body, HttpResult.class);
            if (httpResult.isError()) {
                logger.error("api error, http code={}", httpResult.getCode());
                return "";
            }
            JSONObject jsonData = (JSONObject) httpResult.getData();
            return jsonData.getString("access_token");
        } catch (Exception e) {
            logger.error("api error, url={} error msg={}", url, e.getMessage(), e);
            return "";
        }
    }

    /**
     * 发送IM消息
     * <a href="https://doc-zh.zego.im/article/12222">发送单聊消息</a>
     * <a href="https://doc-zh.zego.im/article/12226">发送房间消息</a>
     *
     * @param roomId  房间id，一对一发送时可为空，不为空时发送给全房间
     * @param fromUid 发送者uid
     * @param toUid   接收者uid，发送给全房间时可为空
     * @param msgBody 消息内容
     */
    @Override
    public void sendMsg(String roomId, String fromUid, Set<String> toUid, MsgDTO msgBody) {
        try {
            UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(ObjectUtils.isEmpty(roomId) ? SEND_PEER_MSG : SEND_ROOM_MSG);
            if (!ObjectUtils.isEmpty(roomId)) {
                urlBuilder.queryParam("RoomId", roomStreamService.genStreamId(roomId));
            }
            generateSignature(urlBuilder);
            String url = urlBuilder.build(false).encode().toUriString();
            ZegoMsgDTO zegoMsgDTO = new ZegoMsgDTO(fromUid, toUid, msgBody);
            asyncHttpClient.post(url, JSON.toJSONString(zegoMsgDTO), null, data -> {
                logger.info("zego send msg roomId={} fromUid={} toUid={} status={}", roomId, fromUid, toUid, data.getBody());
            }, 1);
        } catch (Exception e) {
            logger.error("zego send msg error roomId={} fromUid={} toUid={}", roomId, fromUid, toUid, e);
        }
    }

    @Override
    public String generateActorStreamId(Integer ownerRid, Integer rid, String aid) {
        if (ServerConfig.isProduct()) {
            if (ownerRid == 2996881 || ownerRid == 5777530) {
                return ownerRid + "_" + rid;
            }
        } else {
            if (ownerRid == 2 || ownerRid == 555555 || ownerRid == 1005784 || ownerRid == 1006125 || ownerRid == 100014 || ownerRid == 1003683) {
                return ownerRid + "_" + rid;
            }
        }
        return aid;
//        return ownerRid + "_" + rid;
    }

    /**
     * <a href="https://doc-zh.zego.im/article/11987">向 ZegoEffects 服务端请求鉴权文件</a>
     */
    public String getEffectsLicense() {
        try {
            UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(GET_EFFECTS_LICENSE);
            generateSignature(urlBuilder);
            String url = urlBuilder.build(false).encode().toUriString();
            HttpResponseData<String> responseData = webClient.sendGet(url, null);
            JSONObject body = JSON.parseObject(responseData.getBody());
            JSONObject data = null == body ? null : body.getJSONObject("Data");
            return null == data ? null : data.getString("License");
        } catch (Exception e) {
            logger.error("zego zego getEffectsLicense error msg={}", e.getMessage());
            return null;
        }
    }

    public synchronized String getEffectsLicenseFromCache() {
        String today = DateHelper.DEFAULT.formatDateInDay();
        if (!today.equals(EFFECT_LICENSE_EXPIRE_DAY)) {
            EFFECTS_LICENSE = getEffectsLicense();
            if (ObjectUtils.isEmpty(EFFECTS_LICENSE)) {
                EFFECTS_LICENSE_RETRY_COUNT++;
            } else {
                EFFECT_LICENSE_EXPIRE_DAY = today;
            }
            if (EFFECTS_LICENSE_RETRY_COUNT > 10) {
                EFFECTS_LICENSE_RETRY_COUNT = 0;
                EFFECT_LICENSE_EXPIRE_DAY = today;
            }
        }
        return EFFECTS_LICENSE;
    }
}
