package com.quhong.room;

import com.quhong.core.config.ServerConfig;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 灰度测试房间
 */
@Lazy
@Service
public class TestRoomService {

    private final Set<String> TEST_ROOM_SET = new HashSet<>();
    private String TEST_ROOM = null;

    @PostConstruct
    public void postInit() {
        if (ServerConfig.isProduct()) {
            // 2996881、2988124
            TEST_ROOM_SET.addAll(Arrays.asList("r:5cc2797c66dc630025bf17c2", "r:5cb7df8366dc63002628e272"));
            TEST_ROOM = "r:5cb7df8366dc63002628e272";
        } else {
            // 1003924、2、1016309
            TEST_ROOM_SET.addAll(Arrays.asList("r:5cd0f553644f8e0030454ef7", "r:5cd16cc2644f8e0030454ef8", "r:6343c35bbf29592bcbb01ff0"));
            TEST_ROOM = "r:5cd16cc2644f8e0030454ef8";
        }
    }

    public boolean isTestRoom(String roomId) {
        return TEST_ROOM_SET.contains(roomId) || TEST_ROOM.equals(roomId);
    }

    public Set<String> getTestRoomSet() {
        return TEST_ROOM_SET;
    }

    public String getTestRoom() {
        return TEST_ROOM;
    }
}
