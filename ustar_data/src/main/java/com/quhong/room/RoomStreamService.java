package com.quhong.room;

import com.quhong.redis.RoomPwdRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Service
@Lazy
public class RoomStreamService {
    private static final Logger logger = LoggerFactory.getLogger(RoomStreamService.class);

    @Resource
    private RoomPwdRedis roomPwdRedis;

    /**
     * 生成流id
     *
     * @param roomId 房间id
     */
    public String genStreamId(String roomId) {
        String pwd = roomPwdRedis.getRoomPwd(roomId);
        if (StringUtils.isEmpty(pwd)) {
            pwd = "you_star";
        }
        try {
            return DigestUtils.md5DigestAsHex((roomId + pwd).getBytes());
        } catch (Exception e) {
            logger.error("get streamId error roomId={} pwd={}", roomId, pwd);
            return null;
        }
    }

}
