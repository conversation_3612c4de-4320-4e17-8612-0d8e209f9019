package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.http.AsyncHttpClient;
import com.quhong.monitor.MonitorSender;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
@Lazy
public class PyBroadcastMsgApi {
    private static final Logger logger = LoggerFactory.getLogger(PyBroadcastMsgApi.class);

    @Resource
    private AsyncHttpClient asyncHttpClient;
    @Value("${pyBroadcastUrl}")
    private String pyBroadcastUrl;
    @Resource
    private MonitorSender monitorSender;
    private long warningTime;
    private static final long WARNING_DURATION = 10 * 1000;

    private static final Map<String, String> map = new HashMap<String, String>() {
        {
            put("Content-Type", "application/json");
        }
    };

    /**
     * 发送Python关播消息
     */
    public void sendPyBroadcastMsg(String channel, String msgBody, String msgType) {
        try {
            JSONObject body = new JSONObject();
            body.put("channel", channel);
            body.put("msg_body", msgBody);
            body.put("msg_type", msgType);
            asyncHttpClient.post(pyBroadcastUrl, body.toJSONString(), map, data -> {
                logger.info("sendPyBroadcastMsg channel={} msgBody={} status={} response={}", channel, msgBody, data.getStatus(), data.getBody());
                warnCheck(channel, data.getStatus(), data.getBody());
            }, 2);
        } catch (Exception e) {
            logger.error("send py broadcast msg error. errorMsg={}", e.getMessage());
        }
    }

    private void warnCheck(String channel, int httpCode, String body) {
        try {
            if ("user_charge".equals(channel)) {
                if (httpCode != HttpStatus.OK.value()) {
                    logger.error("send py broadcast msg error, http invoke error httpCode={}", httpCode);
                    sendWarn("中台支付Py广播调用异常", "code=" + httpCode + "body=" + body);
                } else {
                    JSONObject jsonObject = JSON.parseObject(body);
                    if (null != jsonObject && jsonObject.getIntValue("code") != 0) {
                        logger.error("send py broadcast msg error, py handle error body={}", body);
                        sendWarn("中台支付Py广播处理异常", "body=" + body);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("monitor error. {}", e.getMessage(), e);
        }
    }

    private void sendWarn(String desc, String detail) {
        long curTime = System.currentTimeMillis();
        if (curTime - warningTime < WARNING_DURATION) {
            return;
        }
        warningTime = curTime;
        monitorSender.info("ustar_java_exception", desc, detail);
    }
}
