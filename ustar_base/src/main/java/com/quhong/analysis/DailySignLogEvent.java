package com.quhong.analysis;

/**
 * 每日签到记录
 *
 * <AUTHOR>
 * @date 2023/3/14
 */
public class DailySignLogEvent extends UserEvent{

    /**
     * 日期
     */
    private int date;

    /**
     * 每日签到行为类型
     * 0为签到页面曝光 1为点击签到按钮 2为签到成功页面曝光 3为成功签到（服务端上报）
     */
    private int daily_sign_action;

    /**
     * 签到页进入来源 1为首页弹出进入 2为在每日任务点击进入
     */
    private int daily_sign_enter_source;

    /**
     * 加载耗时（毫秒）
     */
    private long load_time;

    /**
     * 连续签到天数
     */
    private int sign_days;

    /**
     * 数据创建时间
     */
    private int ctime;

    @Override
    public String getEventName() {
        return "daily_sign_log";
    }

    @Override
    public int getEventTime() {
        return ctime;
    }

    public int getDate() {
        return date;
    }

    public void setDate(int date) {
        this.date = date;
    }

    public int getDaily_sign_action() {
        return daily_sign_action;
    }

    public void setDaily_sign_action(int daily_sign_action) {
        this.daily_sign_action = daily_sign_action;
    }

    public int getDaily_sign_enter_source() {
        return daily_sign_enter_source;
    }

    public void setDaily_sign_enter_source(int daily_sign_enter_source) {
        this.daily_sign_enter_source = daily_sign_enter_source;
    }

    public long getLoad_time() {
        return load_time;
    }

    public void setLoad_time(long load_time) {
        this.load_time = load_time;
    }

    public int getSign_days() {
        return sign_days;
    }

    public void setSign_days(int sign_days) {
        this.sign_days = sign_days;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
