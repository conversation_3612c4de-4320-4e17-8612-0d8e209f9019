package com.quhong.analysis;

/**
 * 麦位互动
 */
public class NewMicOperateEvent extends UserEvent {

    public String room_id; // 房间id
    public int mic_up_type; // 上麦操作类型 1为主动上麦，2为房主邀请上麦，3为管理员邀请上麦 4申请上麦
    public int mic_down_type; // 下麦操作类型 1为主动下麦，2为房主踢掉下麦，3为管理员踢掉下麦，4为离开房间下麦，5切换live模式时被踢下麦
    public int mic_up_time; // 上麦时间 同时将其作为数数的event_time
    public int mic_down_time; // 下麦时间
    public int mic_time; // 上麦时长
    public int room_type; // 1为语聊房，2为直播房

    @Override
    public String getEventName() {
        return "new_mic_operate";
    }

    @Override
    public int getEventTime() {
        return mic_up_time;
    }

    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public int getMic_up_type() {
        return mic_up_type;
    }

    public void setMic_up_type(int mic_up_type) {
        this.mic_up_type = mic_up_type;
    }

    public int getMic_down_type() {
        return mic_down_type;
    }

    public void setMic_down_type(int mic_down_type) {
        this.mic_down_type = mic_down_type;
    }

    public int getMic_up_time() {
        return mic_up_time;
    }

    public void setMic_up_time(int mic_up_time) {
        this.mic_up_time = mic_up_time;
    }

    public int getMic_down_time() {
        return mic_down_time;
    }

    public void setMic_down_time(int mic_down_time) {
        this.mic_down_time = mic_down_time;
    }

    public int getMic_time() {
        return mic_time;
    }

    public void setMic_time(int mic_time) {
        this.mic_time = mic_time;
    }

    public int getRoom_type() {
        return room_type;
    }

    public void setRoom_type(int room_type) {
        this.room_type = room_type;
    }
}
