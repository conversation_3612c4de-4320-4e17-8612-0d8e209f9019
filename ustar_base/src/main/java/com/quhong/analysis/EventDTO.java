package com.quhong.analysis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.utils.DateHelper;
import org.springframework.lang.NonNull;

import java.util.HashMap;
import java.util.Map;

public class EventDTO {
    private String uid; // 用户uid
    private String distinctId; // 访客id
    private String eventName; // 事件名称
    private String eventId; // 事件id，唯一。track_update时使用
    private Map<String, Object> properties = new HashMap<>();  // 事件属性
    private int method; // 0track 1track_update 2user_set 3user_add 4user_append

    public EventDTO() {
    }

    public EventDTO(String uid, String eventName) {
        this.uid = uid;
        this.eventName = eventName;
    }

    public EventDTO(String uid, String distinctId, String eventName) {
        this.uid = uid;
        this.distinctId = distinctId;
        this.eventName = eventName;
    }

    public EventDTO(String uid, String eventName, @NonNull Map<String, Object> properties) {
        this.uid = uid;
        this.eventName = eventName;
        this.properties = properties;
    }

    public EventDTO(String uid, String distinctId, String eventName, @NonNull Map<String, Object> properties) {
        this.uid = uid;
        this.distinctId = distinctId;
        this.eventName = eventName;
        this.properties = properties;
    }

    /**
     * 使用fastjson序列化将object的所有属性换为properties
     */
    public EventDTO(String uid, String eventName, @NonNull Object object) {
        this.uid = uid;
        this.eventName = eventName;
        properties.putAll((JSONObject) JSON.toJSON(object));
    }

    public EventDTO(@NonNull UserEvent userEvent) {
        this.uid = userEvent.getUid();
        this.eventName = userEvent.getEventName();
        if (0 != userEvent.getEventTime()) {
            properties.put("#time", DateHelper.UTC.formatDateByThinkingData(userEvent.getEventTime()));
        }
        properties.putAll((JSONObject) JSON.toJSON(userEvent));
    }

    /**
     * 统计事件处理
     *
     * @param statDate 统计日期，设置为UTC时间 12:00
     *                 eg: statDate 2022-02-28 to #time 2022-02-28 12:00:00.000
     */
    public EventDTO(@NonNull UserEvent userEvent, String statDate) {
        this.uid = userEvent.getUid();
        this.eventName = userEvent.getEventName();
        properties.put("#time", statDate + " 12:00:00.000");
        properties.putAll((JSONObject) JSON.toJSON(userEvent));
    }

    /**
     * 统计事件处理
     *
     * @param statDate 统计日期，设置为UTC时间 12:00
     *                 eg: statDate 2022-02-28 to #time 2022-02-28 12:00:00.000
     */
    public EventDTO(String uid, String eventName, String statDate, @NonNull Object object) {
        this.uid = uid;
        this.eventName = eventName;
        properties.putAll((JSONObject) JSON.toJSON(object));
        properties.put("#time", statDate + " 12:00:00.000");
    }

    /**
     * 将jsonObject的所有属性换为properties
     */
    public EventDTO(String uid, String eventName, @NonNull JSONObject jsonObject) {
        this.uid = uid;
        this.eventName = eventName;
        properties.putAll(jsonObject);
    }

    /**
     * builder模式
     */
    public EventDTO addProperties(String propertyName, Object value) {
        properties.put(propertyName, value);
        return this;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getDistinctId() {
        return distinctId;
    }

    public void setDistinctId(String distinctId) {
        this.distinctId = distinctId;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public Map<String, Object> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, Object> properties) {
        this.properties = properties;
    }

    /**
     * 获取所有properties，追加UTC时间
     */
    public Map<String, Object> getPropertiesFillTime() {
        if (!this.properties.containsKey("#time")) {
            this.properties.put("#time", DateHelper.UTC.formatDateByThinkingData());
        }
        this.properties.put("#zone_offset", 0);
        return properties;
    }

    public int getMethod() {
        return method;
    }

    public void setMethod(int method) {
        this.method = method;
    }
}
