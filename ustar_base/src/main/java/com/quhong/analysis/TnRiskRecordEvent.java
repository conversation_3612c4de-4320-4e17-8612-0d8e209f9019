package com.quhong.analysis;

/**
 * 图灵顿风险标签记录
 *
 */
public class TnRiskRecordEvent extends UserEvent {

    public String tn_id; // 房间id
    public String tn_risk; // 游戏id
    public int is_cache; //0 不是 1是
    private int ctime;

    @Override
    public String getEventName() {
        return "tn_risk_record";
    }

    @Override
    public int getEventTime() {
        return ctime;
    }

    public String getTn_id() {
        return tn_id;
    }

    public void setTn_id(String tn_id) {
        this.tn_id = tn_id;
    }

    public String getTn_risk() {
        return tn_risk;
    }

    public void setTn_risk(String tn_risk) {
        this.tn_risk = tn_risk;
    }

    public int getIs_cache() {
        return is_cache;
    }

    public void setIs_cache(int is_cache) {
        this.is_cache = is_cache;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
