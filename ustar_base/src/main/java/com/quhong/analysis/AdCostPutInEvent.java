package com.quhong.analysis;


public class AdCostPutInEvent extends UserEvent {
    private int ctime;
    private String pkg_os;  //场景
    private String medium;  //场景描述
    private String campaign;
    private float ad_cost_money;
    private String date;
    private String operator;
    private String time_zone;
    private String other_info;

    @Override
    public String getEventName() {
        return "ad_cost";
    }

    @Override
    public int getEventTime() {
        return ctime;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public String getPkg_os() {
        return pkg_os;
    }

    public void setPkg_os(String pkg_os) {
        this.pkg_os = pkg_os;
    }

    public String getMedium() {
        return medium;
    }

    public void setMedium(String medium) {
        this.medium = medium;
    }

    public String getCampaign() {
        return campaign;
    }

    public void setCampaign(String campaign) {
        this.campaign = campaign;
    }

    public float getAd_cost_money() {
        return ad_cost_money;
    }

    public void setAd_cost_money(float ad_cost_money) {
        this.ad_cost_money = ad_cost_money;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getTime_zone() {
        return time_zone;
    }

    public void setTime_zone(String time_zone) {
        this.time_zone = time_zone;
    }

    public String getOther_info() {
        return other_info;
    }

    public void setOther_info(String other_info) {
        this.other_info = other_info;
    }
}
