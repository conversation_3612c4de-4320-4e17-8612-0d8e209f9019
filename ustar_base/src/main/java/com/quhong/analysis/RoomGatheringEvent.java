package com.quhong.analysis;

/**
 *  发送召集广播记录
 *
 * <AUTHOR>
 * @date 2023/2/27
 */
public class RoomGatheringEvent extends UserEvent{

    /**
     * 房间id
     */
    private String room_id;

    /**
     * 召集广播类型 0召集全球用户 1召集房间会员 2召集房间粉丝 3召集我的粉丝
     */
    private int gathering_broadcast_type;

    /**
     * 召集广播消耗钻石数
     */
    private int gathering_broadcast_cost;

    /**
     * 数据创建时间
     */
    private int ctime;

    @Override
    public String getEventName() {
        return "room_gathering_broadcast";
    }

    @Override
    public int getEventTime() {
        return ctime;
    }

    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public int getGathering_broadcast_type() {
        return gathering_broadcast_type;
    }

    public void setGathering_broadcast_type(int gathering_broadcast_type) {
        this.gathering_broadcast_type = gathering_broadcast_type;
    }

    public int getGathering_broadcast_cost() {
        return gathering_broadcast_cost;
    }

    public void setGathering_broadcast_cost(int gathering_broadcast_cost) {
        this.gathering_broadcast_cost = gathering_broadcast_cost;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
