package com.quhong.analysis;

/**
 *  水果机流水记录
 *
 * <AUTHOR>
 * @date 2023/2/27
 */
public class FruitPartyEvent extends UserEvent{


    private String day;

    private int fruit_type;

    private int consume;

    /**
     * 数据创建时间
     */
    private int ctime;

    private int loop;

    private int win_beans;

    private int hit_type;

    private int hit_odds;


    @Override
    public String getEventName() {
        return "serv_fruit_party";
    }

    @Override
    public int getEventTime() {
        return ctime;
    }


    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public int getFruit_type() {
        return fruit_type;
    }

    public void setFruit_type(int fruit_type) {
        this.fruit_type = fruit_type;
    }

    public int getConsume() {
        return consume;
    }

    public void setConsume(int consume) {
        this.consume = consume;
    }

    public int getLoop() {
        return loop;
    }

    public void setLoop(int loop) {
        this.loop = loop;
    }

    public int getWin_beans() {
        return win_beans;
    }

    public void setWin_beans(int win_beans) {
        this.win_beans = win_beans;
    }

    public int getHit_type() {
        return hit_type;
    }

    public void setHit_type(int hit_type) {
        this.hit_type = hit_type;
    }

    public int getHit_odds() {
        return hit_odds;
    }

    public void setHit_odds(int hit_odds) {
        this.hit_odds = hit_odds;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
