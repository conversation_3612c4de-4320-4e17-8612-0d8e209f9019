package com.quhong.msg;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.proto.YoustarProtoCommon;

@Message(cmd = Cmd.ERROR_NOTIFICATION)
public class ErrorNotificationPushMsg extends MarsServerMsg {
    private int reason; // 1 文字不能发送
    private int show_type; // 1 提示文字 2 弹窗
    private String title; // 标题
    private String content; // 内容


    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoCommon.ErrorNotification msg = YoustarProtoCommon.ErrorNotification.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.reason = msg.getReason();
        this.show_type = msg.getShowType();
        this.title = msg.getTitle();
        this.content = msg.getContent();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoCommon.ErrorNotification.Builder builder =  YoustarProtoCommon.ErrorNotification.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setReason(reason);
        builder.setShowType(show_type);
        builder.setTitle(title == null ? "" : title);
        builder.setContent(content == null ? "" : content);
        return builder.build().toByteArray();
    }

    public int getReason() {
        return reason;
    }

    public void setReason(int reason) {
        this.reason = reason;
    }

    public int getShow_type() {
        return show_type;
    }

    public void setShow_type(int show_type) {
        this.show_type = show_type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
