package com.quhong.msg.obj;

import com.quhong.core.msg.IProto;
import com.quhong.proto.YoustarProtoRoom;

/**
 * <AUTHOR>
 * @date 2023/3/6
 */
public class UserRoomRankInfoObject implements IProto<YoustarProtoRoom.UserRoomRankInfo> {


    /**
     *
     */
    private String uid;

    /**
     *
     */
    private int rank;

    /**
     * 1日榜 2周榜
     */
    private int rankType;


    @Override
    public void doFromBody(YoustarProtoRoom.UserRoomRankInfo proto) {
        this.uid = proto.getUid();
        this.rank = proto.getRank();
        this.rankType = proto.getRankType();
    }

    @Override
    public YoustarProtoRoom.UserRoomRankInfo.Builder doToBody() {
        YoustarProtoRoom.UserRoomRankInfo.Builder builder = YoustarProtoRoom.UserRoomRankInfo.newBuilder();
        builder.setUid(this.uid == null ? "" : this.uid);
        builder.setRank(this.rank);
        builder.setRankType(this.rankType);
        return builder;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getRank() {
        return rank;
    }

    public void setRank(int rank) {
        this.rank = rank;
    }

    public int getRankType() {
        return rankType;
    }

    public void setRankType(int rankType) {
        this.rankType = rankType;
    }

    @Override
    public String toString() {
        return "UserRoomRankInfoObject{" +
                "uid='" + uid + '\'' +
                ", rank=" + rank +
                ", rankType=" + rankType +
                '}';
    }
}
