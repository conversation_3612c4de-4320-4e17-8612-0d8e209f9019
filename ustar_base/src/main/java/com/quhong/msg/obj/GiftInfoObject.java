package com.quhong.msg.obj;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.protobuf.GeneratedMessageV3;
import com.quhong.core.msg.IProto;
import com.quhong.proto.YoustarProtoGift;

public class GiftInfoObject implements IProto<YoustarProtoGift.GiftInfo> {
    @JSONField(name = "gift_id")
    private int giftId;  //礼物ID
    @JSONField(name = "gift_type")
    private int giftType; //动画类型：1:普通 2:交互 3:全屏webp 4:全屏入场webp 5:全屏Aiya
    private int cartonLoadType; //3: webp  7 svga
    @JSONField(name = "gift_number")
    private int giftNumber; //礼物数量
    @JSONField(name = "gift_icon")
    private String giftIcon = ""; //礼物图标
    @JSONField(name = "gift_time")
    private long giftTime; //礼物时间
    @JSONField(name = "gift_price")
    private int giftPrice;
    @JSONField(name = "gift_random")
    private int giftRandom; // 是否是盲盒礼物 0否 1是
    @JSONField(name = "gift_random_icon")
    private String giftRandomIcon = ""; // 盲盒礼物图标
    @JSONField(name = "gift_fusion")
    private int giftFusion; // 是否融合动画礼物 0否 1是
    @JSONField(name = "fusion_id")
    private String fusionId = ""; //  融合用户id
    @JSONField(name = "fusion_name")
    private String fusionName = ""; //  融合用户名称
    @JSONField(name = "fusion_head")
    private String fusionHead = ""; // 融合用户头像

    public void fillFrom(JSONObject src){
        if(src == null){
            return;
        }
        this.giftId = src.getIntValue("gift_id");
        this.giftType = src.getIntValue("gift_type");
        this.giftNumber = src.getIntValue("gift_number");
        this.giftIcon = src.getString("gift_icon");
        this.giftTime = src.getLongValue("gift_time");
        this.cartonLoadType = 1;
        this.giftPrice = src.getIntValue("gift_price");
    }

    @Override
    public void doFromBody(YoustarProtoGift.GiftInfo proto) {
        this.giftId = proto.getGiftId();
        this.giftType = proto.getGiftType();
        this.cartonLoadType = proto.getCartonLoadType();
        this.giftIcon = proto.getGiftIcon();
        this.giftTime = proto.getGiftTime();
        this.giftNumber = proto.getGiftNumber();
        this.giftPrice = proto.getGiftPrice();
        this.giftRandom = proto.getGiftRandom();
        this.giftRandomIcon = proto.getGiftRandomIcon();
        this.giftFusion = proto.getGiftFusion();
        this.fusionId = proto.getFusionId();
        this.fusionName = proto.getFusionName();
        this.fusionHead = proto.getFusionHead();
    }

    @Override
    public YoustarProtoGift.GiftInfo.Builder doToBody() {
        YoustarProtoGift.GiftInfo.Builder builder = YoustarProtoGift.GiftInfo.newBuilder();
        builder.setGiftId(giftId);
        builder.setGiftType(giftType);
        builder.setCartonLoadType(cartonLoadType);
        builder.setGiftIcon(giftIcon == null ? "": giftIcon);
        builder.setGiftTime(giftTime);
        builder.setGiftNumber(giftNumber);
        builder.setGiftPrice(giftPrice);
        builder.setGiftRandom(giftRandom);
        builder.setGiftRandomIcon(giftRandomIcon == null ? "": giftRandomIcon);
        builder.setGiftFusion(giftFusion);
        builder.setFusionId(fusionId == null ? "": fusionId);
        builder.setFusionName(fusionName == null ? "": fusionName);
        builder.setFusionHead(fusionHead == null ? "": fusionHead);
        return builder;
    }

    public int getGiftId() {
        return giftId;
    }

    public void setGiftId(int giftId) {
        this.giftId = giftId;
    }

    public int getGiftType() {
        return giftType;
    }

    public void setGiftType(int giftType) {
        this.giftType = giftType;
    }

    public int getCartonLoadType() {
        return cartonLoadType;
    }

    public void setCartonLoadType(int cartonLoadType) {
        this.cartonLoadType = cartonLoadType;
    }

    public int getGiftNumber() {
        return giftNumber;
    }

    public void setGiftNumber(int giftNumber) {
        this.giftNumber = giftNumber;
    }

    public String getGiftIcon() {
        return giftIcon;
    }

    public void setGiftIcon(String giftIcon) {
        this.giftIcon = giftIcon;
    }

    public long getGiftTime() {
        return giftTime;
    }

    public void setGiftTime(long giftTime) {
        this.giftTime = giftTime;
    }

    public int getGiftPrice() {
        return giftPrice;
    }

    public void setGiftPrice(int giftPrice) {
        this.giftPrice = giftPrice;
    }

    public int getGiftRandom() {
        return giftRandom;
    }

    public void setGiftRandom(int giftRandom) {
        this.giftRandom = giftRandom;
    }

    public String getGiftRandomIcon() {
        return giftRandomIcon;
    }

    public void setGiftRandomIcon(String giftRandomIcon) {
        this.giftRandomIcon = giftRandomIcon;
    }

    public int getGiftFusion() {
        return giftFusion;
    }

    public void setGiftFusion(int giftFusion) {
        this.giftFusion = giftFusion;
    }

    public String getFusionId() {
        return fusionId;
    }

    public void setFusionId(String fusionId) {
        this.fusionId = fusionId;
    }

    public String getFusionName() {
        return fusionName;
    }

    public void setFusionName(String fusionName) {
        this.fusionName = fusionName;
    }

    public String getFusionHead() {
        return fusionHead;
    }

    public void setFusionHead(String fusionHead) {
        this.fusionHead = fusionHead;
    }
}
