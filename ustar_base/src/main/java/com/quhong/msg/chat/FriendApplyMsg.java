package com.quhong.msg.chat;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.proto.YoustarProtoChat;

@Message(cmd = Cmd.FRIEND_APPLY_MSG)
public class FriendApplyMsg extends MarsServerMsg {

    private String fromUid; //申请者uid
    private String name; //申请者名称
    private String head; //申请者头像
    private String body;

    @Override
    public void fillFrom(JSONObject object) {
        this.fromUid = object.getString("from_uid") == null ? "" : object.getString("from_uid");
        this.name = object.getString("name") == null ? "" : object.getString("name");
        this.head = object.getString("head") == null ? "" : object.getString("head");
        this.body = object.getString("body") == null ? "" : object.getString("body");
    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoChat.FriendApplyMessage msg = YoustarProtoChat.FriendApplyMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.fromUid = msg.getFromUid();
        this.name = msg.getName();
        this.head = msg.getHead();
        this.body = msg.getBody();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoChat.FriendApplyMessage.Builder builder = YoustarProtoChat.FriendApplyMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setFromUid(this.fromUid == null ? "" : this.fromUid);
        builder.setName(this.name == null ? "" : this.name);
        builder.setHead(this.head == null ? "" : this.head);
        builder.setBody(this.body == null ? "" : this.body);
        return builder.build().toByteArray();
    }

    @Override
    public String getFromUid() {
        return fromUid;
    }

    @Override
    public void setFromUid(String fromUid) {
        this.fromUid = fromUid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }
}
