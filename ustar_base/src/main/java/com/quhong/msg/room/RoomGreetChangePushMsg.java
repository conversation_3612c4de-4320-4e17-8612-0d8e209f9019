package com.quhong.msg.room;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.proto.YoustarProtoRoom;

@Message(cmd = Cmd.ROOM_GREET_SWITCH_CHANGE)
public class RoomGreetChangePushMsg extends MarsServerMsg {

    private int greet_switch; //欢迎语开关 1打开 2关闭

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoRoom.RoomGreetSwitchChangedMessage msg = YoustarProtoRoom.RoomGreetSwitchChangedMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.greet_switch = msg.getGreetSwitch();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoRoom.RoomGreetSwitchChangedMessage.Builder builder = YoustarProtoRoom.RoomGreetSwitchChangedMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setGreetSwitch(greet_switch);
        return builder.build().toByteArray();
    }

    public int getGreet_switch() {
        return greet_switch;
    }

    public void setGreet_switch(int greet_switch) {
        this.greet_switch = greet_switch;
    }
}
