package com.quhong.msg.room;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.proto.YoustarProtoRoom;

@Message(cmd = Cmd.LEAVE_ROOM_REQ)
public class LeaveRoomMsg extends MarsServerMsg {
    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoRoom.LeaveRoomMessage msg = YoustarProtoRoom.LeaveRoomMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoRoom.LeaveRoomMessage.Builder builder = YoustarProtoRoom.LeaveRoomMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        return builder.build().toByteArray();
    }
}
