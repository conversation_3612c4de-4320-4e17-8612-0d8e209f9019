package com.quhong.msg.room;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.proto.YoustarProtoHighlightText;
import com.quhong.proto.YoustarProtoRoom;
import com.quhong.proto.YoustarProtoUser;

import java.util.ArrayList;
import java.util.List;

/**
 * actionType
 * 0:跳转 h5, 1: 统一格式，保留不使用[弹窗], 2: 跳转等级页面, 3: 商店Ride坐骑列表, 4: 商店mic列表, 5: 商店bubble列表, 6: 商店voice声波, 7: Unique ID,
 * 8: 女王  9: tycoons  10: userPage个人主页  11: 商店飘屏列表  12: 勋章列表  13: 我的坐骑列表 14: 我的麦位框列表  15: 我的气泡列表  16: 我的声波列表
 * 17: 我的飘屏列表 18: 点击游戏全屏 19: 点击游戏半屏 20: 充值页面  21: 商城主页  22: 商店入场通知列表  23: 我的入场通知列表 24: 跳转家族主页 25: 跳转日常任务
 * 30: 指定momentId
 * 99: 指定房间  998 不跳转
 */
@Message(cmd = Cmd.USER_COMMON_POP_UP_MSG)
public class UserCommonPopupMessage extends MarsServerMsg {
    private String uid;
    private String icon;
    private String titleEn;
    private String titleAr;
    private String textEn;
    private String textAr;
    private int actionType;
    private String actionValue;
    private int showView = 1;
    private int showDuration;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoUser.UserCommonPopupMessage msg = YoustarProtoUser.UserCommonPopupMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.uid = msg.getUid();
        this.icon = msg.getIcon();
        this.titleEn = msg.getTitleEn();
        this.titleAr = msg.getTitleAr();
        this.textEn = msg.getTextEn();
        this.textAr = msg.getTextAr();
        this.actionType = msg.getActionType();
        this.actionValue = msg.getActionValue();
        this.showView = msg.getShowView();
        this.showDuration = msg.getShowDuration();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoUser.UserCommonPopupMessage.Builder builder = YoustarProtoUser.UserCommonPopupMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setIcon(this.icon == null ? "" : this.icon);
        builder.setTitleEn(this.titleEn == null ? "" : this.titleEn);
        builder.setTitleAr(this.titleAr == null ? "" : this.titleAr);
        builder.setTextEn(this.textEn == null ? "" : this.textEn);
        builder.setTextAr(this.textAr == null ? "" : this.textAr);
        builder.setActionType(actionType);
        builder.setActionValue(this.actionValue == null ? "" : this.actionValue);
        builder.setShowView(showView);
        builder.setShowDuration(showDuration);
        return builder.build().toByteArray();
    }

    @Override
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getTitleEn() {
        return titleEn;
    }

    public void setTitleEn(String titleEn) {
        this.titleEn = titleEn;
    }

    public String getTitleAr() {
        return titleAr;
    }

    public void setTitleAr(String titleAr) {
        this.titleAr = titleAr;
    }

    public String getTextEn() {
        return textEn;
    }

    public void setTextEn(String textEn) {
        this.textEn = textEn;
    }

    public String getTextAr() {
        return textAr;
    }

    public void setTextAr(String textAr) {
        this.textAr = textAr;
    }

    public int getActionType() {
        return actionType;
    }

    public void setActionType(int actionType) {
        this.actionType = actionType;
    }

    public String getActionValue() {
        return actionValue;
    }

    public void setActionValue(String actionValue) {
        this.actionValue = actionValue;
    }

    public int getShowView() {
        return showView;
    }

    public void setShowView(int showView) {
        this.showView = showView;
    }

    public int getShowDuration() {
        return showDuration;
    }

    public void setShowDuration(int showDuration) {
        this.showDuration = showDuration;
    }
}
