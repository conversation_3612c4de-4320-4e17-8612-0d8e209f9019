package com.quhong.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.config.ServerConfig;
import com.quhong.enums.HttpCode;
import com.quhong.enums.ServerType;
import com.quhong.exception.CommonException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;

/**
 * create by maoyule on 2019/4/15
 */
public class RequestUtils {
    private static final Logger logger = LoggerFactory.getLogger(RequestUtils.class);

    public static String CACHE_PARAM = "cache_param";
    public static String REQUEST_ID = "request_id";

    public static byte[] getBody(HttpServletRequest request) {
        try {
            if (request.getInputStream().isFinished()) {
                return null;
            }
            long contentLength = request.getContentLength();
            ByteArrayOutputStream bos =
                    new ByteArrayOutputStream(contentLength >= 0 ? (int) contentLength : StreamUtils.BUFFER_SIZE);
            StreamUtils.copy(request.getInputStream(), bos);
            return bos.toByteArray();
        } catch (Exception e) {
            String remoteIp = RequestUtils.getIpAddress(request);
            String message = e.getMessage();
            if (message.contains("EOFException")) {
                logger.info("read body failed. path={} ip={} {}", request.getRequestURI(), remoteIp, message);
            } else {
                logger.info("read body failed. path={} ip={} {}", request.getRequestURI(), remoteIp, message, e);
            }
        }
        return null;
    }

    @Deprecated
    public static JSONObject getSendData(HttpServletRequest request) {
        return (JSONObject) request.getAttribute(CACHE_PARAM);
    }

    public static JSONObject getSendDataFromCache(HttpServletRequest request) {
        return (JSONObject) request.getAttribute(CACHE_PARAM);
    }

    public static JSONObject parseSendData(HttpServletRequest request) {
        // 先从缓存中获取
        JSONObject jsonObject = getSendDataFromCache(request);
        if (jsonObject != null) {
            return jsonObject;
        }
        long startParseTime = System.currentTimeMillis();
        byte[] bodyBytes = getBody(request);
        if (bodyBytes == null) {
            logger.info("body is null. path={}", request.getRequestURI());
            return null;
        }
        if (System.currentTimeMillis() - startParseTime > 1000L) {
            logger.info("getBody cost too long! path={} cost={}", request.getRequestURI(), System.currentTimeMillis() - startParseTime);
        }
        boolean debug = RequestUtils.getParamInt(request, "debug") > 0;
        boolean test = !ServerType.PRODUCT.equals(ServerConfig.getServerType());
        boolean encrypt = debug && test;
        int newVersioncode = RequestUtils.getParamInt(request, "new_versioncode");
        try {
            String body = new String(bodyBytes, StandardCharsets.UTF_8);
            jsonObject = getSendData(body, encrypt, newVersioncode);
            if (jsonObject == null) {
                logger.info("json is null. body={} path={}", body, request.getRequestURI());
                return null;
            }
            jsonObject.put("debug", encrypt ? 1 : 0);
            // 存入缓存中
            cacheSendData(request, jsonObject, newVersioncode);
            if (newVersioncode < 5) {
                throw new CommonException(HttpCode.UPDATE_APP);
            }
            return jsonObject;
        } catch (CommonException e) {
            throw e;
        } catch (Exception e) {
            logger.error("parse json error. path={} bodyBytes.length={} {}", request.getRequestURI(), bodyBytes.length, e.getMessage(), e);
            return null;
        }
    }

    public static <T> T getSendDataFromNoCache(HttpServletRequest request, Class<T> clazz) {
        JSONObject jsonObject = parseSendData(request);
        if (jsonObject != null) {
            return jsonObject.toJavaObject(clazz);
        }
        return null;
    }

    public static <T> T getSendData(HttpServletRequest request, Class<T> clazz) {
        JSONObject jsonObject = (JSONObject) request.getAttribute(CACHE_PARAM);
        return jsonObject.toJavaObject(clazz);
    }

    public static void cacheSendData(HttpServletRequest request, JSONObject sendData, int newVersioncode) {
        sendData.put("new_versioncode", newVersioncode);
        request.setAttribute(CACHE_PARAM, sendData);
    }

    public static JSONObject getSendData(String body, int newVersioncode) {
        return getSendData(body, false, newVersioncode);
    }

    /**
     * 获取发送数据
     *
     * @param body           请求的body数据
     * @param debug          是否测试环境
     * @param newVersioncode 加密版本信息
     */
    public static JSONObject getSendData(String body, boolean debug, int newVersioncode) {
        if (StringUtils.isEmpty(body)) {
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(body);
        if (debug) {
            // debug模式，直接是send_data中的内容，不加密，方便postman调试
            return jsonObject;
        }
        String value = jsonObject.getString(3 == newVersioncode ? "to_data" : "send_data");
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        String decodeStr = AESUtils.decryptServerData(value, newVersioncode);
        return JSON.parseObject(decodeStr);
    }

    public static String getParam(HttpServletRequest request, String key) {
        String value = request.getParameter(key);
        if (value != null) {
            return value.trim();
        }
        return null;
    }

    public static int getParamInt(HttpServletRequest request, String key) {
        String value = request.getParameter(key);
        if (value != null) {
            return Integer.parseInt(value.trim());
        }
        return -1;
    }

    public static long getParamLong(HttpServletRequest request, String key) {
        String value = request.getParameter(key);
        if (value != null) {
            return Long.parseLong(value.trim());
        }
        return -1;
    }

    public static boolean getParamBoolean(HttpServletRequest request, String key) {
        String value = request.getParameter(key);
        if (value != null) {
            return Boolean.valueOf(value);
        }
        return false;
    }

    public static String getIpAddress(HttpServletRequest request) {
        String ipAddress = filterHeader(request, "x-forwarded-for");
        if (ipAddress != null) {
            int index = ipAddress.indexOf(",");
            if (index > -1) {
                ipAddress = ipAddress.substring(0, index);
            }
            return ipAddress;
        }
        ipAddress = filterHeader(request, "Proxy-Client-IP");
        if (ipAddress != null) {
            return ipAddress;
        }
        ipAddress = filterHeader(request, "WL-Proxy-Client-IP");
        if (ipAddress != null) {
            return ipAddress;
        }
        ipAddress = filterHeader(request, "XFF");
        if (ipAddress != null) {
            return ipAddress;
        }
        ipAddress = request.getRemoteAddr();
        return ipAddress;
    }

    private static String filterHeader(HttpServletRequest request, String headerName) {
        String ret = request.getHeader(headerName);
        if (ret == null || ret.length() == 0 || "unknown".equalsIgnoreCase(ret)) {
            return null;
        }
        return ret;
    }
}
