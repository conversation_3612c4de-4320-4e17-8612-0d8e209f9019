package com.quhong.executors.http;

import com.quhong.data.HttpReqData;
import com.quhong.http.HttpResult;
import com.quhong.robot.Robot;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
public class CreateRoomHandler extends LeaveRoomHandler {
    private static final Logger logger = LoggerFactory.getLogger(CreateRoomHandler.class);
    public static final String CREATE_ROOM_URL = "room_service/create";
    public static final String UP_MIC_URL = "room_service/room_mic_up";


    public void reqCreateRoom(Robot robot, String roomId) {
        CreateRoomReq reqData = new CreateRoomReq();
        reqData.copyFrom(robot);
        robot.getRoomData().setEntering(true);
        requestV2(CREATE_ROOM_URL, reqData, data -> {
            HttpResult result = data.getBody();
            if (result == null) {
                logger.error("create room error. result is null. body={} status={} uid={}", data.getBody(), data.getStatus(), robot.getUid());
                robot.afterLeaveRoom();
                return;
            }
            if (result.isError()) {
                logger.info("create room error. code={} msg={}. uid={}", result.getCode(), result.getMsg(), robot.getUid());
                robot.getRoomData().getErrorRoomSet().add(roomId);
                robot.afterLeaveRoom();
                return;
            }
            robot.afterEnterRoom(roomId);
            reqUpMicMsg(robot);
        });
    }

    public void reqUpMicMsg(Robot robot) {
        String roomId = robot.getRoomId();
        if (StringUtils.isEmpty(roomId)) {
            return;
        }
        RoomMicReq reqData = new RoomMicReq();
        reqData.copyFrom(robot);
        reqData.setRoom_id(roomId);
        requestV2(UP_MIC_URL, reqData, data -> {
            HttpResult result = data.getBody();
            if (result == null) {
                logger.error("up mic error. result is null. body={} status={} uid={}", data.getBody(), data.getStatus(), robot.getUid());
                return;
            }
            if (result.isError()) {
                logger.error("up mic error. code={}. uid={}", result.getCode(), robot.getUid());
                return;
            }
        });
    }

    public static class CreateRoomReq extends HttpReqData {
        private int roomMode = 1; // 房间模式，1Voice、2Live

        public int getRoomMode() {
            return roomMode;
        }

        public void setRoomMode(int roomMode) {
            this.roomMode = roomMode;
        }
    }

    public static class RoomMicReq extends HttpReqData {
        private String room_id;
        private int opType = 1;

        public String getRoom_id() {
            return room_id;
        }

        public void setRoom_id(String room_id) {
            this.room_id = room_id;
        }

        public int getOpType() {
            return opType;
        }

        public void setOpType(int opType) {
            this.opType = opType;
        }
    }
}
