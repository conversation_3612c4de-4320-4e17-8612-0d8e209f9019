spring:
  application:
    name: ustar-biz-robot
server:
  port: 8080
robot:
  v1Domain: https://api.qmovies.tv:8081
  v2Domain: https://apiv2.qmovies.tv
  tcpHost: ************
  tcpPort: 9001
dubbo:
  application:
    name: ${spring.application.name}
    qos-enable: false
    metadata-service-port: 20885
  registry:
    address: kubernetes://DEFAULT_MASTER_HOST?registry-type=service&duplicate=false&trustCerts=true&namespace=devops
  protocol:
    name: dubbo
    port: 20880
  metadata-report:
    report-metadata: false
  service:
    shutdown:
      wait: 5000
