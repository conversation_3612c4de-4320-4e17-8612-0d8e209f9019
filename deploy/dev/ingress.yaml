apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  namespace: devops
  name: ustar-java-user
  annotations:
    alb.ingress.kubernetes.io/group.name: ustar
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/target-group-attributes: stickiness.enabled=true,stickiness.lb_cookie.duration_seconds=600
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}, {"HTTP": 80}, {"HTTP": 8080}]'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:ap-south-1:239620982073:certificate/de56ea51-ba15-4818-8145-67be9a2e104a
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/backend-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-path: /system/health_check
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '5'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '2'  #健康检查超时时间设置为2s
spec:
  rules:
    - host: testv2.qmovies.tv
      http:
        paths:
          - path: /user/
            pathType: Prefix
            backend:
              service:
                name: ustar-java-user
                port:
                  number: 8080

