package com.quhong.config;

import com.quhong.core.annotation.MessageGroup;
import com.quhong.core.annotation.MsgExecutorGroup;
import com.quhong.core.clusters.servers.ClusterServerConnector;
import com.quhong.core.clusters.servers.ClusterSocketServer;
import com.quhong.core.net.server.adapter.IConnectorFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

public class TCPServerConfig {
    private static final Logger logger = LoggerFactory.getLogger(TCPServerConfig.class);

    @Autowired
    private MessageGroup messageGroup;
    @Autowired
    private MsgExecutorGroup msgExecutorGroup;

    private ClusterSocketServer socketServer;

    @PostConstruct
    public void postInit(){
        socketServer = new ClusterSocketServer();
        socketServer.initParams(true, 0, 60, 8);
        socketServer.start();
    }

    @PreDestroy
    public void preDestroy(){
        if(socketServer != null){
            socketServer.stop();
        }
    }

    @Bean
    public IConnectorFactory getConnectorFactory() {
        return () -> new ClusterServerConnector(messageGroup, msgExecutorGroup);
    }

    @Bean
    public ClusterSocketServer getSocketServer(){
        return socketServer;
    }
}
