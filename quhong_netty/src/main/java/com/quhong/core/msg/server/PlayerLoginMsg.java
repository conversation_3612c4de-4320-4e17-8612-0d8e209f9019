package com.quhong.core.msg.server;

import com.quhong.core.annotation.Message;
import com.quhong.core.msg.ServerMsg;
import com.quhong.enums.BaseServerCmd;
import com.quhong.server.protobuf.ServerProto;

@Message(cmd = BaseServerCmd.PLAYER_LOGIN, isServer = true)
public class PlayerLoginMsg extends ServerMsg {
    private String playerUid = "";

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        ServerProto.PlayerLogin msg = ServerProto.PlayerLogin.parseFrom(bytes);
        this.playerUid = msg.getUid();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        ServerProto.PlayerLogin.Builder builder = ServerProto.PlayerLogin.newBuilder();
        builder.setUid(this.playerUid);
        return builder.build().toByteArray();
    }

    public String getPlayerUid() {
        return playerUid;
    }

    public void setPlayerUid(String playerUid) {
        this.playerUid = playerUid;
    }
}
