package com.quhong.core.msg.server;

import com.google.protobuf.ByteString;
import com.quhong.core.annotation.Message;
import com.quhong.core.msg.ServerMsg;
import com.quhong.enums.BaseServerCmd;
import com.quhong.server.protobuf.ServerProto;

@Message(cmd = BaseServerCmd.GLOBAL_MSG, isServer = true)
public class GlobalMsg extends ServerMsg {
    private int msgCmd;
    private byte[] body;
    private String fromUid;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        ServerProto.GlobalMessage msg = ServerProto.GlobalMessage.parseFrom(bytes);
        this.msgCmd = msg.getMsgCmd();
        this.body = msg.getBody().toByteArray();
        this.fromUid = msg.getFromUid();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        ServerProto.GlobalMessage.Builder builder = ServerProto.GlobalMessage.newBuilder();
        builder.setMsgCmd(msgCmd);
        builder.setBody(ByteString.copyFrom(body));
        builder.setFromUid(fromUid == null ? "" : fromUid);
        return builder.build().toByteArray();
    }

    public int getMsgCmd() {
        return msgCmd;
    }

    public void setMsgCmd(int msgCmd) {
        this.msgCmd = msgCmd;
    }

    public byte[] getBody() {
        return body;
    }

    public void setBody(byte[] body) {
        this.body = body;
    }

    @Override
    public String getFromUid() {
        return fromUid;
    }

    public void setFromUid(String fromUid) {
        this.fromUid = fromUid;
    }
}
