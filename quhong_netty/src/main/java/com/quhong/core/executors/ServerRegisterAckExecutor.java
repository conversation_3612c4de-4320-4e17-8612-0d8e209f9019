package com.quhong.core.executors;

import com.quhong.core.annotation.MsgExecutor;
import com.quhong.core.clusters.clients.ClusterClientConnector;
import com.quhong.core.msg.Msg;
import com.quhong.core.net.connect.IConnector;
import com.quhong.enums.BaseServerCmd;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@MsgExecutor
public class ServerRegisterAckExecutor extends AbstractMsgExecutor {
    private static final Logger logger = LoggerFactory.getLogger(ServerRegisterAckExecutor.class);

    public ServerRegisterAckExecutor() {
        super(BaseServerCmd.SERVER_REGISTER_ACK);
    }

    @Override
    public void execute(IConnector connector, Msg msg) {
        if(connector instanceof ClusterClientConnector){
            ((ClusterClientConnector)connector).receiveRegisterAck();
            logger.info("receive register ack. sessionId={}", connector.getSessionId());
        }else{
            logger.error("receive register ack. connector is not ClusterClientConnector sessionId={}", connector.getSessionId());
        }
    }
}
