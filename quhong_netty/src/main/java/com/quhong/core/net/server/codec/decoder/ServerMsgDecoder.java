package com.quhong.core.net.server.codec.decoder;

import com.quhong.core.annotation.MessageGroup;
import com.quhong.core.clusters.ClusterGroup;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.errors.CodecException;
import com.quhong.core.msg.*;
import com.quhong.enums.BaseClusterEnums;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ServerMsgDecoder extends AbstractMsgDecoder {
    private static final Logger logger = LoggerFactory.getLogger(ServerMsgDecoder.class);

    protected MessageGroup msgGroup;
    protected boolean isGate;

    public ServerMsgDecoder(MessageGroup msgGroup) {
        this.msgGroup = msgGroup;
        this.isGate = ClusterGroup.fetchClusterId(ServerConfig.getServerID()) == BaseClusterEnums.GATE;
    }

    @Override
    public Msg decode(ChannelHandlerContext ctx, ByteBuf buf) {
        return decode(buf);
    }

    public Msg decode(ByteBuf buf){
        if (buf == null) {
            return null;
        }
        int size = buf.readUnsignedShort();
        // 用于标记头开始标记
        int headerIndex = buf.readerIndex();
        ServerHeader header = createMsgHeader();
        try {
            header.fromHeader(buf);
        }catch (CodecException e){
            logger.error("decode error size={}", size);
            return null;
        }
        size = size - 2; //去掉长度字长
        Msg msg = getMsg(header.getCmd());
        if(msg == null){
            logger.error("can not find msg. cmd={}", header.getCmd());
            return null;
        }
        msg.setHeader(header);
        int bodySize = size - header.getHeaderLength();
        if(bodySize < 0){
            logger.error("decode error cmd={} size={} header.length={}", header.getCmd(), size, header.getHeaderLength());
            return null;
        }
        int bodyIndex = headerIndex + header.getHeaderLength();
        try {
            // 重新设置 bodySize
            buf.readerIndex(bodyIndex);
        }catch (Exception e){
            logger.error("decode error.  cmd={} size={} header.length={}", header.getCmd(), size, header.getHeaderLength());
        }
        byte[] bytes = new byte[bodySize];
        if(bodySize > 0) {
            buf.readBytes(bytes);
        }
        msg.fromBody(bytes);
        return msg;
    }

    protected Msg getMsg(int cmd){
        Class<Msg> msgClazz;
        if(isGate){
            msgClazz = msgGroup.getGateServer(cmd);
        }else {
            msgClazz = msgGroup.get(cmd);
        }
        if(msgClazz == null){
            return new GateServerMsg();
        }
        try {
            return msgClazz.newInstance();
        }catch (Exception e){
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    protected Msg getGateMsg(int cmd){
        Class<Msg> msgClazz = msgGroup.getGate(cmd);
        if(msgClazz == null){
            return null;
        }
        try {
            return msgClazz.newInstance();
        }catch (Exception e){
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    protected ServerHeader createMsgHeader(){
        return new ServerHeader();
    }
}
