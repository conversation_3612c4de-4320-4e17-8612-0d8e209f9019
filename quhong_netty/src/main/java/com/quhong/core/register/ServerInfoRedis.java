package com.quhong.core.register;

import com.alibaba.fastjson.JSON;
import com.quhong.core.utils.DateHelper;
import com.quhong.datas.ServerData;
import com.quhong.monitor.MonitorSender;
import com.quhong.redis.RedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Component
@Lazy
public class ServerInfoRedis {
    private static final Logger logger = LoggerFactory.getLogger(ServerInfoRedis.class);
    private static final int EXPIRE_SECOND = 16; //redis过期时间为16s

    @Resource(name = RedisBean.IM)
    private StringRedisTemplate redisTemplate;
    @Autowired
    private MonitorSender monitorSender;

    public ServerInfoRedis() {

    }

    public void updateServer(ServerData serverData) {
        if (Boolean.FALSE.equals(redisTemplate.expire(getServerKey(serverData.getServerId()), EXPIRE_SECOND, TimeUnit.SECONDS))) {
            addServerData(serverData);
        }
        addToHash(serverData.getServerId());
    }

    public void addServerData(ServerData serverData) {
        redisTemplate.opsForValue().set(getServerKey(serverData.getServerId()), JSON.toJSONString(serverData), EXPIRE_SECOND, TimeUnit.SECONDS);
        addToHash(serverData.getServerId());
    }

    public void removeServerData(int serverId) {
        redisTemplate.delete(getServerKey(serverId));
        removeFromHash(serverId);
    }

    public ServerData getServerData(int serverId) {
        String json = redisTemplate.opsForValue().get(getServerKey(serverId));
        if (StringUtils.isEmpty(json)) {
            logger.info("can not find sererData. serverId={}", serverId);
            return null;
        }
        return JSON.parseObject(json, ServerData.class);
    }

    private String getServerKey(int serverId) {
        return "server_" + serverId;
    }

    public void addToHash(int serverId) {
        redisTemplate.opsForHash().put(getHashKey(), serverId + "", DateHelper.getNowSeconds() + "");
    }

    private void removeFromHash(int serverId) {
        redisTemplate.opsForHash().delete(getHashKey(), serverId + "");
    }

    public List<ServerData> getServerList() {
        List<ServerData> serverDataList = new ArrayList<>();
        Set<Object> keySet = redisTemplate.opsForHash().keys(getHashKey());
        for (Object key : keySet) {
            Integer serverId = null;
            try {
                serverId = Integer.valueOf((String) key);
            } catch (Exception e) {
                monitorSender.info("im", "parse server error", "serverId=" + serverId);
                logger.error("can not parse serverId. serverId={} {}", key, e.getMessage(), e);
                redisTemplate.opsForHash().delete(getHashKey(), key);
            }
            if (serverId == null || serverId == 0) {
                logger.error("server id is null or zero. serverId={}", key);
                monitorSender.info("im", "parse server error", "serverId=" + serverId);
                redisTemplate.opsForHash().delete(getHashKey(), key);
                continue;
            }
            ServerData serverData = getServerData(serverId);
            if (serverData == null) {
                logger.error("serverData is null. serverId={}", key);
                redisTemplate.opsForHash().delete(getHashKey(), key);
                continue;
            }
            serverDataList.add(serverData);
        }
        return serverDataList;
    }

    private String getHashKey() {
        return "java_server_hash";
    }
}
