package com.quhong.core.annotation;

import com.quhong.core.msg.Msg;
import com.quhong.core.utils.ClassUtil;
import com.quhong.core.utils.SpringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2018/10/11
 * @copyright core
 */
@Component
public class MessageGroup {
    private static final Logger logger = LoggerFactory.getLogger(MessageGroup.class);

    private static MessageGroup instance;

    public static MessageGroup getInstance(){
        if(instance == null){
            instance = SpringUtils.getBean(MessageGroup.class);
        }
        return instance;
    }

    private Map<Integer, Class<Msg>> map = new ConcurrentHashMap<>();
    private Map<Integer, Class<Msg>> gateMap = new ConcurrentHashMap<>();
    private Map<Integer, Class<Msg>> serverMap = new ConcurrentHashMap<>();
    private Map<Class, Integer> map2 = new ConcurrentHashMap<>();

    public MessageGroup() {
        instance = this;
        addPackage("com.quhong");
    }

    public void removeClassesFromSuperClass(Class<?> superClass, String packageName){
        Iterator<Class<Msg>> iter = map.values().iterator();
        while(iter.hasNext()){
            Class<Msg> clazz = iter.next();
            if(clazz.getPackage().getName().equals(packageName)){
                if(superClass.isAssignableFrom(clazz)){
                    iter.remove();
                    map2.remove(clazz);
                    logger.info("remove class.{}",clazz.getCanonicalName());
                }
            }
        }
    }

    public void addMsg(int cmd, Class<Msg> msgBaseClass){
        map.put(cmd, msgBaseClass);
    }

    public void addPackage(String packageName){
        Set<Class<?>> classes = ClassUtil.getClasses(packageName);
        for (Class<?> clazz : classes) {
            Message anon = clazz.getAnnotation(Message.class);
            if (anon != null) {
                map.put(anon.cmd(), (Class<Msg>)clazz);
                map2.put(clazz, anon.cmd());
                if(anon.isServer()){
                    serverMap.put(anon.cmd(), (Class<Msg>)clazz);
                }
                if(anon.isGate()){
                    gateMap.put(anon.cmd(), (Class<Msg>)clazz);
                }
            }
        }
    }

    public Map<Integer, Class<Msg>> getAll() {
        return map;
    }

    public Class<Msg> get(int key) {
        return this.map.get(key);
    }

    public Class<Msg> getGate(int key) {
        return this.gateMap.get(key);
    }

    public Class<Msg> getGateServer(int key){
        return this.serverMap.get(key);
    }

    public void remove(int cmd){
        this.map.remove(cmd);
    }

    public int getCmd(Class clazz){
        Integer cmd = this.map2.get(clazz);
        if(cmd == null){
            return 0;
        }
        return cmd;
    }
}
