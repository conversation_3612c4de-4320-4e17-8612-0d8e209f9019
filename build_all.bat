echo off
set PROJECT=ustar_gate
set MODULE=gate
set OUT_DIR=..\out\gate
echo "==== %PROJECT% build all start ========"
echo "===== mvn ========="
call mvn clean compile package

echo "==== out dir clear ========"
del /f /s /q %OUT_DIR%\*.*
echo "==== copy ========"
rd /s /q %OUT_DIR%\
md %OUT_DIR%\
copy .\target\%MODULE%.jar %OUT_DIR%\
xcopy .\target\lib\* %OUT_DIR%\lib\ /s/e/y
echo "==== zip ========"
"7z.exe" a -r %OUT_DIR%\%PROJECT%.zip %OUT_DIR%\*.*
echo "==== %PROJECT% complete ========"
echo "===== upload to server====="
cd %OUT_DIR%\
"up.exe" %PROJECT%.zip
echo "===== upload to server done====="
pause
