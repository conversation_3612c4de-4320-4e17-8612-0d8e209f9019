package com.quhong.data;

import java.io.Serializable;
import java.util.Date;

public class YoutubeVideo implements Serializable {
    private String videoId;

    private String keywords;

    private String mainBody;

    private String title;

    private String thumbnail;

    private long releaseTime;

    private long crawlerTime;

    public YoutubeVideo(){

    }

    public String getVideoId() {
        return videoId;
    }

    public void setVideoId(String videoId) {
        this.videoId = videoId;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getMainBody() {
        return mainBody;
    }

    public void setMainBody(String mainBody) {
        this.mainBody = mainBody;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(String thumbnail) {
        this.thumbnail = thumbnail;
    }

    public long getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(long releaseTime) {
        this.releaseTime = releaseTime;
    }

    public long getCrawlerTime() {
        return crawlerTime;
    }

    public void setCrawlerTime(long crawlerTime) {
        this.crawlerTime = crawlerTime;
    }
}
