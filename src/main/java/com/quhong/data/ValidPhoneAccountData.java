package com.quhong.data;

import com.alibaba.fastjson.JSON;

/**
 * <AUTHOR>
 * @date 2022/9/16
 */
public class ValidPhoneAccountData {

    private String oldAccount; // 老接口账号，可能带0
    private String newAccount; // 手机账号不带0
    private String newAccountByZero; // 手机账号带0
    private boolean isValidAccount = true; //  账号是否匹配正则

    public String getOldAccount() {
        return oldAccount;
    }

    public void setOldAccount(String oldAccount) {
        this.oldAccount = oldAccount;
    }

    public String getNewAccount() {
        return newAccount;
    }

    public void setNewAccount(String newAccount) {
        this.newAccount = newAccount;
    }

    public String getNewAccountByZero() {
        return newAccountByZero;
    }

    public void setNewAccountByZero(String newAccountByZero) {
        this.newAccountByZero = newAccountByZero;
    }

    public boolean isValidAccount() {
        return isValidAccount;
    }

    public void setValidAccount(boolean validAccount) {
        isValidAccount = validAccount;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
