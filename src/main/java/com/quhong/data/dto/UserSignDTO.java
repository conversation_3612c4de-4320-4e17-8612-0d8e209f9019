package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

/**
 * <AUTHOR>
 * @date 2022/9/20
 */
public class UserSignDTO extends HttpEnvData {

    /**
     * 1 打开 2 关闭
     */
    private int switch_notice;
    private Boolean isRookieBag;
    /**
     * 0首页弹出，1日常任务页
     */
    private int access;

    /**
     * 8.43版本是否新用户第一次首页请求，0不是 1是（ps：注册接口下发isnew为1时）
     */
    private int firstReq;

    private int fakeDay;

    private String ip;

    public int getSwitch_notice() {
        return switch_notice;
    }

    public void setSwitch_notice(int switch_notice) {
        this.switch_notice = switch_notice;
    }

    public Boolean getRookieBag() {
        return isRookieBag;
    }

    public void setRookieBag(Boolean rookieBag) {
        isRookieBag = rookieBag;
    }

    public int getAccess() {
        return access;
    }

    public void setAccess(int access) {
        this.access = access;
    }

    public int getFirstReq() {
        return firstReq;
    }

    public void setFirstReq(int firstReq) {
        this.firstReq = firstReq;
    }

    public int getFakeDay() {
        return fakeDay;
    }

    public void setFakeDay(int fakeDay) {
        this.fakeDay = fakeDay;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }
}
