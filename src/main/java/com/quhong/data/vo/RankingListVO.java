package com.quhong.data.vo;

import com.quhong.data.RidData;

import java.util.List;

public class RankingListVO {
    private String uid;
    private String roomId;
    private String name;
    private String head;
    private String associateHead; // 关联用户的头像
    private String associateUid;  // 关联用户的uid
    private int score;
    private int conquerRoom;  // 征服房间数
    private RidData ridData;
    private List<String> badgeList;
    private Integer vipLevel;
    private String countryFlag; // 国旗
    private List<OtherSupportUserVO> supportUserList; // 房间榜单的支持者

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getAssociateHead() {
        return associateHead;
    }

    public void setAssociateHead(String associateHead) {
        this.associateHead = associateHead;
    }

    public String getAssociateUid() {
        return associateUid;
    }

    public void setAssociateUid(String associateUid) {
        this.associateUid = associateUid;
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }

    public int getConquerRoom() {
        return conquerRoom;
    }

    public void setConquerRoom(int conquerRoom) {
        this.conquerRoom = conquerRoom;
    }

    public RidData getRidData() {
        return ridData;
    }

    public void setRidData(RidData ridData) {
        this.ridData = ridData;
    }


    public List<String> getBadgeList() {
        return badgeList;
    }

    public void setBadgeList(List<String> badgeList) {
        this.badgeList = badgeList;
    }

    public Integer getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(Integer vipLevel) {
        this.vipLevel = vipLevel;
    }

    public String getCountryFlag() {
        return countryFlag;
    }

    public void setCountryFlag(String countryFlag) {
        this.countryFlag = countryFlag;
    }

    public List<OtherSupportUserVO> getSupportUserList() {
        return supportUserList;
    }

    public void setSupportUserList(List<OtherSupportUserVO> supportUserList) {
        this.supportUserList = supportUserList;
    }
}
