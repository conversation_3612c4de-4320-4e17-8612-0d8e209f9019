package com.quhong.data.vo;

import com.quhong.mongo.data.CelebrityActivity;

import java.util.List;

public class CelebrityActivityVO {
    private String acNameEn; // 英文活动名
    private String acNameAr; // 阿语活动名
    private Integer startTime; // 活动开始时间
    private Integer endTime; // 活动结束时间
    private CelebrityActivity.ActivityConfig config; // 活动配置
    private int freeLikeCount; // 剩余的点赞数量
    private List<CelebrityRankingVO> rankingList; // 排行榜

    public String getAcNameEn() {
        return acNameEn;
    }

    public void setAcNameEn(String acNameEn) {
        this.acNameEn = acNameEn;
    }

    public String getAcNameAr() {
        return acNameAr;
    }

    public void setAcNameAr(String acNameAr) {
        this.acNameAr = acNameAr;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public CelebrityActivity.ActivityConfig getConfig() {
        return config;
    }

    public void setConfig(CelebrityActivity.ActivityConfig config) {
        this.config = config;
    }

    public int getFreeLikeCount() {
        return freeLikeCount;
    }

    public void setFreeLikeCount(int freeLikeCount) {
        this.freeLikeCount = freeLikeCount;
    }

    public List<CelebrityRankingVO> getRankingList() {
        return rankingList;
    }

    public void setRankingList(List<CelebrityRankingVO> rankingList) {
        this.rankingList = rankingList;
    }
}
