package com.quhong.data.vo;


import java.util.List;

public class MomentTopicPageVO {
    private List<MomentTopicVO> topicList;
    private List<MomentTopicVO> recommendedList;
    private String nextUrl;
    private MyMomentTopicVO myMomentTopic;

    public List<MomentTopicVO> getTopicList() {
        return topicList;
    }

    public void setTopicList(List<MomentTopicVO> topicList) {
        this.topicList = topicList;
    }

    public List<MomentTopicVO> getRecommendedList() {
        return recommendedList;
    }

    public void setRecommendedList(List<MomentTopicVO> recommendedList) {
        this.recommendedList = recommendedList;
    }

    public String getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(String nextUrl) {
        this.nextUrl = nextUrl;
    }

    public MyMomentTopicVO getMyMomentTopic() {
        return myMomentTopic;
    }

    public void setMyMomentTopic(MyMomentTopicVO myMomentTopic) {
        this.myMomentTopic = myMomentTopic;
    }

    public static class MyMomentTopicVO {
        private Integer myTopicRid; // 我的话题rid，0为没创建
        private Integer myTopicStatus; // 我的话题状态  -1没创建 0待审核 1审核通过

        public Integer getMyTopicRid() {
            return myTopicRid;
        }

        public void setMyTopicRid(Integer myTopicRid) {
            this.myTopicRid = myTopicRid;
        }

        public Integer getMyTopicStatus() {
            return myTopicStatus;
        }

        public void setMyTopicStatus(Integer myTopicStatus) {
            this.myTopicStatus = myTopicStatus;
        }
    }

    public static class AddATopicVO extends MomentTopicPageVO {
        private List<MomentTopicVO> recentlyUseList;
        private List<MomentTopicVO> recommendedList;

        public List<MomentTopicVO> getRecentlyUseList() {
            return recentlyUseList;
        }

        public void setRecentlyUseList(List<MomentTopicVO> recentlyUseList) {
            this.recentlyUseList = recentlyUseList;
        }

        public List<MomentTopicVO> getRecommendedList() {
            return recommendedList;
        }

        public void setRecommendedList(List<MomentTopicVO> recommendedList) {
            this.recommendedList = recommendedList;
        }
    }


    public static class MomentTopicMembersPageVO extends MomentTopicPageVO {
        private int role; // 访问者话题身份 1 话题创建者 2 话题管理员 3 话题关注用户 0非话题成员
        private MomentTopicVO momentTopic;

        private List<MomentTopicVO.MomentTopicMembersVO> membersList;

        public List<MomentTopicVO.MomentTopicMembersVO> getMembersList() {
            return membersList;
        }

        public void setMembersList(List<MomentTopicVO.MomentTopicMembersVO> membersList) {
            this.membersList = membersList;
        }

        public int getRole() {
            return role;
        }

        public void setRole(int role) {
            this.role = role;
        }

        public MomentTopicVO getMomentTopic() {
            return momentTopic;
        }

        public void setMomentTopic(MomentTopicVO momentTopic) {
            this.momentTopic = momentTopic;
        }
    }

    public static class BlackMembersPageVO extends MomentTopicPageVO {
        private int role; // 访问者话题身份 1 话题创建者 2 话题管理员 3 话题关注用户 0非话题成员

        public int getRole() {
            return role;
        }

        public void setRole(int role) {
            this.role = role;
        }

        private List<MomentTopicVO.MomentTopicMembersVO> blackMembersList;

        public List<MomentTopicVO.MomentTopicMembersVO> getBlackMembersList() {
            return blackMembersList;
        }

        public void setBlackMembersList(List<MomentTopicVO.MomentTopicMembersVO> blackMembersList) {
            this.blackMembersList = blackMembersList;
        }
    }
}
