package com.quhong.data.vo;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * <AUTHOR>
 * @date 2023/4/3
 */
public class ShareActivityVO {

    /**
     * 活动id
     */
    @JSONField(name = "activity_id")
    private String activityId;

    /**
     * 活动名称英语
     */
    @JSONField(name = "activity_name")
    private String name;

    /**
     * 活动介绍英语
     */
    @JSONField(name = "activity_desc")
    private String description;

    /**
     * 活动icon英语
     */
    @JSONField(name = "activity_icon")
    private String icon;

    /**
     * 活动banner英语
     */
    @JSONField(name = "activity_banner")
    private String banner;

    /**
     * 活动地址
     */
    @JSONField(name = "activity_url")
    private String url;

    /**
     * 分享WhatsApp地址
     */
    @JSONField(name = "action_url")
    private String actionUrl;

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getBanner() {
        return banner;
    }

    public void setBanner(String banner) {
        this.banner = banner;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getActionUrl() {
        return actionUrl;
    }

    public void setActionUrl(String actionUrl) {
        this.actionUrl = actionUrl;
    }
}
