package com.quhong.data.vo;


public class TimeCapsuleV2VO extends OtherRankConfigVO{
    private String activityId;
    private String uid;
    private String userName;
    private Integer userLevel;
    private String wishDownDate;
    private int wishStatus;
    private String wishInfo;
    private Integer wishDownDay;
    private Integer shareWish;
    private int wishStatusV2;
    private String wishInfoV2;
    private Integer shareWishV2;

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getUserLevel() {
        return userLevel;
    }

    public void setUserLevel(Integer userLevel) {
        this.userLevel = userLevel;
    }

    public String getWishDownDate() {
        return wishDownDate;
    }

    public void setWishDownDate(String wishDownDate) {
        this.wishDownDate = wishDownDate;
    }

    public int getWishStatus() {
        return wishStatus;
    }

    public void setWishStatus(int wishStatus) {
        this.wishStatus = wishStatus;
    }

    public String getWishInfo() {
        return wishInfo;
    }

    public void setWishInfo(String wishInfo) {
        this.wishInfo = wishInfo;
    }

    public Integer getWishDownDay() {
        return wishDownDay;
    }

    public void setWishDownDay(Integer wishDownDay) {
        this.wishDownDay = wishDownDay;
    }

    public Integer getShareWish() {
        return shareWish;
    }

    public void setShareWish(Integer shareWish) {
        this.shareWish = shareWish;
    }

    public int getWishStatusV2() {
        return wishStatusV2;
    }

    public void setWishStatusV2(int wishStatusV2) {
        this.wishStatusV2 = wishStatusV2;
    }

    public String getWishInfoV2() {
        return wishInfoV2;
    }

    public void setWishInfoV2(String wishInfoV2) {
        this.wishInfoV2 = wishInfoV2;
    }

    public Integer getShareWishV2() {
        return shareWishV2;
    }

    public void setShareWishV2(Integer shareWishV2) {
        this.shareWishV2 = shareWishV2;
    }
}
