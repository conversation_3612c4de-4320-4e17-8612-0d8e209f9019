package com.quhong.data.vo;


import java.util.List;

public class PlantsWarVO extends OtherRankConfigVO{
    private int status;         // 状态 0: 正常  1: 死亡
    private int plantsNum;      // 剩余豌豆数量
    private int initNum;        // 初始化豌豆数量
    private int eatBrainNum;    // 吃掉脑子数量
    private int deadBrainNum;   // 被吃掉脑子数量
    private int attackNum;      // 攻击礼物数量
    private List<AttackInfo> attackList;

    private List<BrainRankingListVO> brainRankingList;
    private BrainRankingListVO myBrainRank;

    private List<OtherRankingListVO> peaRankingList;
    private OtherRankingListVO myPeaRank;

    // 等级勋章数量
    private int zombieBadgeSend;
    private int peaBadgeSend;

    public static class AttackInfo{
        private int attackTime;
        private int rid;
        private String head;

        public int getAttackTime() {
            return attackTime;
        }

        public void setAttackTime(int attackTime) {
            this.attackTime = attackTime;
        }

        public int getRid() {
            return rid;
        }

        public void setRid(int rid) {
            this.rid = rid;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getPlantsNum() {
        return plantsNum;
    }

    public void setPlantsNum(int plantsNum) {
        this.plantsNum = plantsNum;
    }

    public int getInitNum() {
        return initNum;
    }

    public void setInitNum(int initNum) {
        this.initNum = initNum;
    }

    public int getEatBrainNum() {
        return eatBrainNum;
    }

    public void setEatBrainNum(int eatBrainNum) {
        this.eatBrainNum = eatBrainNum;
    }

    public int getDeadBrainNum() {
        return deadBrainNum;
    }

    public void setDeadBrainNum(int deadBrainNum) {
        this.deadBrainNum = deadBrainNum;
    }

    public int getAttackNum() {
        return attackNum;
    }

    public void setAttackNum(int attackNum) {
        this.attackNum = attackNum;
    }

    public List<AttackInfo> getAttackList() {
        return attackList;
    }

    public void setAttackList(List<AttackInfo> attackList) {
        this.attackList = attackList;
    }

    public List<BrainRankingListVO> getBrainRankingList() {
        return brainRankingList;
    }

    public void setBrainRankingList(List<BrainRankingListVO> brainRankingList) {
        this.brainRankingList = brainRankingList;
    }

    public BrainRankingListVO getMyBrainRank() {
        return myBrainRank;
    }

    public void setMyBrainRank(BrainRankingListVO myBrainRank) {
        this.myBrainRank = myBrainRank;
    }

    public int getZombieBadgeSend() {
        return zombieBadgeSend;
    }

    public void setZombieBadgeSend(int zombieBadgeSend) {
        this.zombieBadgeSend = zombieBadgeSend;
    }

    public int getPeaBadgeSend() {
        return peaBadgeSend;
    }

    public void setPeaBadgeSend(int peaBadgeSend) {
        this.peaBadgeSend = peaBadgeSend;
    }

    public List<OtherRankingListVO> getPeaRankingList() {
        return peaRankingList;
    }

    public void setPeaRankingList(List<OtherRankingListVO> peaRankingList) {
        this.peaRankingList = peaRankingList;
    }

    public OtherRankingListVO getMyPeaRank() {
        return myPeaRank;
    }

    public void setMyPeaRank(OtherRankingListVO myPeaRank) {
        this.myPeaRank = myPeaRank;
    }
}
