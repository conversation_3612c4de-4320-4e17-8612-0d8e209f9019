package com.quhong.data.vo;


import java.util.List;

public class TreasureRecordVO {

    private List<DrawRecord> drawRecordList;
    private Integer nextUrl;

    public static class DrawRecord {
        private String titleEn;
        private String titleAr;
        private String iconEn;
        private String iconAr;
        private Integer ctime;

        public String getTitleEn() {
            return titleEn;
        }

        public void setTitleEn(String titleEn) {
            this.titleEn = titleEn;
        }

        public String getTitleAr() {
            return titleAr;
        }

        public void setTitleAr(String titleAr) {
            this.titleAr = titleAr;
        }

        public String getIconEn() {
            return iconEn;
        }

        public void setIconEn(String iconEn) {
            this.iconEn = iconEn;
        }

        public String getIconAr() {
            return iconAr;
        }

        public void setIconAr(String iconAr) {
            this.iconAr = iconAr;
        }

        public Integer getCtime() {
            return ctime;
        }

        public void setCtime(Integer ctime) {
            this.ctime = ctime;
        }
    }


    public List<DrawRecord> getDrawRecordList() {
        return drawRecordList;
    }

    public void setDrawRecordList(List<DrawRecord> drawRecordList) {
        this.drawRecordList = drawRecordList;
    }

    public Integer getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(Integer nextUrl) {
        this.nextUrl = nextUrl;
    }
}
