package com.quhong.data.vo;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/14
 */
public class StoreRippleVO {

    private List<RippleVO> list;

    private String nextUrl;

    public static class RippleVO {

        @JSONField(name = "voice_icon")
        private String voiceIcon;

        @JSONField(name = "voice_id")
        private Integer voiceId;

        @JSONField(name = "source_url")
        private String sourceUrl;

        private int buy_type;

        private int days;

        private int beans;

        private String voice_name;

        public String getVoiceIcon() {
            return voiceIcon;
        }

        public void setVoiceIcon(String voiceIcon) {
            this.voiceIcon = voiceIcon;
        }

        public Integer getVoiceId() {
            return voiceId;
        }

        public void setVoiceId(Integer voiceId) {
            this.voiceId = voiceId;
        }

        public String getSourceUrl() {
            return sourceUrl;
        }

        public void setSourceUrl(String sourceUrl) {
            this.sourceUrl = sourceUrl;
        }

        public int getBuy_type() {
            return buy_type;
        }

        public void setBuy_type(int buy_type) {
            this.buy_type = buy_type;
        }

        public int getDays() {
            return days;
        }

        public void setDays(int days) {
            this.days = days;
        }

        public int getBeans() {
            return beans;
        }

        public void setBeans(int beans) {
            this.beans = beans;
        }

        public String getVoice_name() {
            return voice_name;
        }

        public void setVoice_name(String voice_name) {
            this.voice_name = voice_name;
        }
    }

    public List<RippleVO> getList() {
        return list;
    }

    public void setList(List<RippleVO> list) {
        this.list = list;
    }

    public String getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(String nextUrl) {
        this.nextUrl = nextUrl;
    }
}
