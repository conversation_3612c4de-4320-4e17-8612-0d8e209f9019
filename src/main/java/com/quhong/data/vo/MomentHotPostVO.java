package com.quhong.data.vo;


import com.quhong.mongo.data.MomentActivityData;

import java.util.List;

public class MomentHotPostVO extends OtherRankConfigVO {

    private List<HotPostRank> likeLastWeeklyRank;
    private List<HotPostRank> likeThisWeeklyRank;

    private List<HotPostRank> giftLastWeeklyRank;
    private List<HotPostRank> giftThisWeeklyRank;
    private List<TaskConfigVO> dailyTaskList;
    private List<TaskConfigVO> finishDayList;

    public static class HotPostRank{
        private String uid;
        private String head;
        private String momentId;
        private String text; // 内容
        private MomentActivityData.Quote quote; // 引用对象，链接、转发及分享等
        private List<MomentActivityData.Image> imgs;  // 图片信息
        private long score;  // 点赞/礼物接收总价值
        private int c_time;  // 发布时间

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public String getMomentId() {
            return momentId;
        }

        public void setMomentId(String momentId) {
            this.momentId = momentId;
        }

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public MomentActivityData.Quote getQuote() {
            return quote;
        }

        public void setQuote(MomentActivityData.Quote quote) {
            this.quote = quote;
        }

        public List<MomentActivityData.Image> getImgs() {
            return imgs;
        }

        public void setImgs(List<MomentActivityData.Image> imgs) {
            this.imgs = imgs;
        }

        public long getScore() {
            return score;
        }

        public void setScore(long score) {
            this.score = score;
        }

        public int getC_time() {
            return c_time;
        }

        public void setC_time(int c_time) {
            this.c_time = c_time;
        }
    }

    public List<HotPostRank> getLikeLastWeeklyRank() {
        return likeLastWeeklyRank;
    }

    public void setLikeLastWeeklyRank(List<HotPostRank> likeLastWeeklyRank) {
        this.likeLastWeeklyRank = likeLastWeeklyRank;
    }

    public List<HotPostRank> getLikeThisWeeklyRank() {
        return likeThisWeeklyRank;
    }

    public void setLikeThisWeeklyRank(List<HotPostRank> likeThisWeeklyRank) {
        this.likeThisWeeklyRank = likeThisWeeklyRank;
    }

    public List<HotPostRank> getGiftLastWeeklyRank() {
        return giftLastWeeklyRank;
    }

    public void setGiftLastWeeklyRank(List<HotPostRank> giftLastWeeklyRank) {
        this.giftLastWeeklyRank = giftLastWeeklyRank;
    }

    public List<HotPostRank> getGiftThisWeeklyRank() {
        return giftThisWeeklyRank;
    }

    public void setGiftThisWeeklyRank(List<HotPostRank> giftThisWeeklyRank) {
        this.giftThisWeeklyRank = giftThisWeeklyRank;
    }

    public List<TaskConfigVO> getDailyTaskList() {
        return dailyTaskList;
    }

    public void setDailyTaskList(List<TaskConfigVO> dailyTaskList) {
        this.dailyTaskList = dailyTaskList;
    }

    public List<TaskConfigVO> getFinishDayList() {
        return finishDayList;
    }

    public void setFinishDayList(List<TaskConfigVO> finishDayList) {
        this.finishDayList = finishDayList;
    }
}
