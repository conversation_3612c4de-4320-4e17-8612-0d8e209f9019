package com.quhong.data.vo;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * <AUTHOR>
 * @date 2022/8/25
 */
public class BalanceVO {

    private Integer beans;
    private Integer gold;
    private Integer paypal;
    private Integer heart;
    private Integer coins;
    @JSONField(name = "service_phone")
    private String servicePhone;
    @JSONField(name = "third_pay")
    private Integer thirdPay;
    private Integer screenUpTime;

    public Integer getBeans() {
        return beans;
    }

    public void setBeans(Integer beans) {
        this.beans = beans;
    }

    public Integer getGold() {
        return gold;
    }

    public void setGold(Integer gold) {
        this.gold = gold;
    }

    public Integer getPaypal() {
        return paypal;
    }

    public void setPaypal(Integer paypal) {
        this.paypal = paypal;
    }

    public Integer getHeart() {
        return heart;
    }

    public void setHeart(Integer heart) {
        this.heart = heart;
    }

    public Integer getCoins() {
        return coins;
    }

    public void setCoins(Integer coins) {
        this.coins = coins;
    }

    public String getServicePhone() {
        return servicePhone;
    }

    public void setServicePhone(String servicePhone) {
        this.servicePhone = servicePhone;
    }

    public Integer getThirdPay() {
        return thirdPay;
    }

    public void setThirdPay(Integer thirdPay) {
        this.thirdPay = thirdPay;
    }

    public Integer getScreenUpTime() {
        return screenUpTime;
    }

    public void setScreenUpTime(Integer screenUpTime) {
        this.screenUpTime = screenUpTime;
    }

    @Override
    public String toString() {
        return "BalanceVO{" +
                "beans=" + beans +
                ", gold=" + gold +
                ", paypal=" + paypal +
                ", heart=" + heart +
                ", servicePhone=" + servicePhone +
                ", thirdPay=" + thirdPay +
                ", screenUpTime=" + screenUpTime +
                '}';
    }
}
