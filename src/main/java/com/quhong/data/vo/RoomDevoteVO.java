package com.quhong.data.vo;

import java.util.List;

public class RoomDevoteVO {
    private List<UserInfoItemVO> list;
    private List<String> myBadge;
    private int myGender;
    private String myHead;
    private String myMicFrame;
    private String myName;
    private String myTotal;
    private String myUid;
    private int myAge;
    private int ulvl;
    private int myVipLevel;
    private String myVipMedal;
    private String total;
    private String myRank;

    @Deprecated
    private String nextUrl;

    @Deprecated
    private Integer uptime;

    public List<UserInfoItemVO> getList() {
        return list;
    }

    public void setList(List<UserInfoItemVO> list) {
        this.list = list;
    }

    public List<String> getMyBadge() {
        return myBadge;
    }

    public void setMyBadge(List<String> myBadge) {
        this.myBadge = myBadge;
    }

    public int getMyGender() {
        return myGender;
    }

    public void setMyGender(int myGender) {
        this.myGender = myGender;
    }

    public String getMyHead() {
        return myHead;
    }

    public void setMyHead(String myHead) {
        this.myHead = myHead;
    }

    public String getMyMicFrame() {
        return myMicFrame;
    }

    public void setMyMicFrame(String myMicFrame) {
        this.myMicFrame = myMicFrame;
    }

    public String getMyName() {
        return myName;
    }

    public void setMyName(String myName) {
        this.myName = myName;
    }

    public String getMyTotal() {
        return myTotal;
    }

    public void setMyTotal(String myTotal) {
        this.myTotal = myTotal;
    }

    public String getMyUid() {
        return myUid;
    }

    public void setMyUid(String myUid) {
        this.myUid = myUid;
    }

    public int getUlvl() {
        return ulvl;
    }

    public void setUlvl(int ulvl) {
        this.ulvl = ulvl;
    }

    public int getMyVipLevel() {
        return myVipLevel;
    }

    public void setMyVipLevel(int myVipLevel) {
        this.myVipLevel = myVipLevel;
    }

    public String getMyVipMedal() {
        return myVipMedal;
    }

    public void setMyVipMedal(String myVipMedal) {
        this.myVipMedal = myVipMedal;
    }

    public String getTotal() {
        return total;
    }

    public void setTotal(String total) {
        this.total = total;
    }

    public String getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(String nextUrl) {
        this.nextUrl = nextUrl;
    }

    public Integer getUptime() {
        return uptime;
    }

    public void setUptime(Integer uptime) {
        this.uptime = uptime;
    }

    public int getMyAge() {
        return myAge;
    }

    public void setMyAge(int myAge) {
        this.myAge = myAge;
    }

    public String getMyRank() {
        return myRank;
    }

    public void setMyRank(String myRank) {
        this.myRank = myRank;
    }
}
