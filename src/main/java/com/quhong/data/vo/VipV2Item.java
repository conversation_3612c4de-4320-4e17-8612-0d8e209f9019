package com.quhong.data.vo;

import java.util.List;

public class VipV2Item {
    /**
     * vip等级
     */
    private int vipLevel;
    /**
     * vip购买价格
     */
    private int price;
    /**
     * vip有效天数
     */
    private int validDay;
    /**
     * vip勋章
     */
    private String vipMedal;
    /**
     * vip是否可购买
     * 0:不可购买 1:可购买
     */
    private int canBuy;
    /**
     * 不可购买提示
     */
    private String tips;
    /**
     * vip特权列表
     */
    private List<VipV2Privilege> privilegeList;

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public int getPrice() {
        return price;
    }

    public void setPrice(int price) {
        this.price = price;
    }

    public int getValidDay() {
        return validDay;
    }

    public void setValidDay(int validDay) {
        this.validDay = validDay;
    }

    public String getVipMedal() {
        return vipMedal;
    }

    public void setVipMedal(String vipMedal) {
        this.vipMedal = vipMedal;
    }

    public int getCanBuy() {
        return canBuy;
    }

    public void setCanBuy(int canBuy) {
        this.canBuy = canBuy;
    }

    public String getTips() {
        return tips;
    }

    public void setTips(String tips) {
        this.tips = tips;
    }

    public List<VipV2Privilege> getPrivilegeList() {
        return privilegeList;
    }

    public void setPrivilegeList(List<VipV2Privilege> privilegeList) {
        this.privilegeList = privilegeList;
    }
}
