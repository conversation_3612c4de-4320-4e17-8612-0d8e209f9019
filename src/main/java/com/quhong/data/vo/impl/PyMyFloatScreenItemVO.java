package com.quhong.data.vo.impl;

import com.alibaba.fastjson.annotation.JSONField;
import com.quhong.data.vo.GoodsListHomeVO;

public class PyMyFloatScreenItemVO extends GoodsListHomeVO.GoodsItemVO {
    /**
     * 2 麦位框 3 坐骑 6 聊天气泡 7 声波纹 8 浮屏
     */
    private Integer res_type;

    @JSONField(name = "screen_id")
    private Integer res_id;

    private String source_icon;

    /**
     * 坐骑类型才有效 1为svga
     */
    private Integer source_type;

    @JSONField(name = "screen_name")
    private String source_name;

    private String source_url;

    /**
     * 坐骑类型才有效
     */
    private String source_md5;

    /**
     * 动效时长,坐骑类型才有效
     */
    private Integer time_long;

    private Integer beans;

    private Integer days;

    /**
     * 0为钻石，1为金币
     */
    private Integer buy_type;

    /**
     * 老版本7天售卖钻石
     */
    private Integer diamonds_7_days;

    /**
     * 老版本15天售卖钻石
     */
    private Integer diamonds_15_days;

    /**
     * 老版本浮萍标签名字
     */
    private String label_name;


    // 以下是我的装扮相关字段
    private Integer states;

    private Integer end_days;

    /**
     * 0: 默认相关  1: 奖励相关  2: 荣誉相关 3: vip相关  4: 用户等级相关 5 商店购买  6: 签到相关  7: 女王相关
     */
    private Integer item_type;

    /**
     * 老版本我的装扮浮萍字段
     */
    private Integer get_way;

    /**
     * 老版本我的装扮气泡字段
     */
    private Integer is_activity;

    /**
     * 老版本我的装扮气泡字段
     */
    private String buddle_color;

    /**
     * 老版本我的装扮气泡字段
     */
    private String buddle_tl;

    /**
     * 老版本我的装扮气泡字段
     */
    private String buddle_tr;

    /**
     * 老版本我的装扮气泡字段
     */
    private String buddle_bl;

    /**
     * 老版本我的装扮气泡字段
     */
    private String buddle_br;

    /**
     * 老版本我的装扮气泡字段
     */
    private Integer top;

    /**
     * 老版本我的装扮气泡字段
     */
    private Integer bottom;

    /**
     * 老版本我的装扮气泡字段
     */
    private Integer left;

    /**
     * 老版本我的装扮气泡字段
     */
    private Integer right;

    /**
     * 新版本我的装扮麦位排序字段
     */
    @JSONField(serialize = false)
    private long c_time;


    public Integer getRes_type() {
        return res_type;
    }

    public void setRes_type(Integer res_type) {
        this.res_type = res_type;
    }

    public Integer getRes_id() {
        return res_id;
    }

    public void setRes_id(Integer res_id) {
        this.res_id = res_id;
    }

    public String getSource_icon() {
        return source_icon;
    }

    public void setSource_icon(String source_icon) {
        this.source_icon = source_icon;
    }

    public Integer getSource_type() {
        return source_type;
    }

    public void setSource_type(Integer source_type) {
        this.source_type = source_type;
    }

    public String getSource_name() {
        return source_name;
    }

    public void setSource_name(String source_name) {
        this.source_name = source_name;
    }

    public String getSource_url() {
        return source_url;
    }

    public void setSource_url(String source_url) {
        this.source_url = source_url;
    }

    public Integer getTime_long() {
        return time_long;
    }

    public void setTime_long(Integer time_long) {
        this.time_long = time_long;
    }

    public Integer getBeans() {
        return beans;
    }

    public void setBeans(Integer beans) {
        this.beans = beans;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public Integer getBuy_type() {
        return buy_type;
    }

    public void setBuy_type(Integer buy_type) {
        this.buy_type = buy_type;
    }

    public String getSource_md5() {
        return source_md5;
    }

    public void setSource_md5(String source_md5) {
        this.source_md5 = source_md5;
    }

    public Integer getDiamonds_7_days() {
        return diamonds_7_days;
    }

    public void setDiamonds_7_days(Integer diamonds_7_days) {
        this.diamonds_7_days = diamonds_7_days;
    }

    public Integer getDiamonds_15_days() {
        return diamonds_15_days;
    }

    public void setDiamonds_15_days(Integer diamonds_15_days) {
        this.diamonds_15_days = diamonds_15_days;
    }

    public String getLabel_name() {
        return label_name;
    }

    public void setLabel_name(String label_name) {
        this.label_name = label_name;
    }

    public Integer getStates() {
        return states;
    }

    public void setStates(Integer states) {
        this.states = states;
    }

    public Integer getEnd_days() {
        return end_days;
    }

    public void setEnd_days(Integer end_days) {
        this.end_days = end_days;
    }

    public Integer getItem_type() {
        return item_type;
    }

    public void setItem_type(Integer item_type) {
        this.item_type = item_type;
    }

    public Integer getGet_way() {
        return get_way;
    }

    public void setGet_way(Integer get_way) {
        this.get_way = get_way;
    }

    public Integer getIs_activity() {
        return is_activity;
    }

    public void setIs_activity(Integer is_activity) {
        this.is_activity = is_activity;
    }

    public String getBuddle_color() {
        return buddle_color;
    }

    public void setBuddle_color(String buddle_color) {
        this.buddle_color = buddle_color;
    }

    public String getBuddle_tl() {
        return buddle_tl;
    }

    public void setBuddle_tl(String buddle_tl) {
        this.buddle_tl = buddle_tl;
    }

    public String getBuddle_tr() {
        return buddle_tr;
    }

    public void setBuddle_tr(String buddle_tr) {
        this.buddle_tr = buddle_tr;
    }

    public String getBuddle_bl() {
        return buddle_bl;
    }

    public void setBuddle_bl(String buddle_bl) {
        this.buddle_bl = buddle_bl;
    }

    public String getBuddle_br() {
        return buddle_br;
    }

    public void setBuddle_br(String buddle_br) {
        this.buddle_br = buddle_br;
    }

    public Integer getTop() {
        return top;
    }

    public void setTop(Integer top) {
        this.top = top;
    }

    public Integer getBottom() {
        return bottom;
    }

    public void setBottom(Integer bottom) {
        this.bottom = bottom;
    }

    public Integer getLeft() {
        return left;
    }

    public void setLeft(Integer left) {
        this.left = left;
    }

    public Integer getRight() {
        return right;
    }

    public void setRight(Integer right) {
        this.right = right;
    }

    public long getC_time() {
        return c_time;
    }

    public void setC_time(long c_time) {
        this.c_time = c_time;
    }
}
