package com.quhong.data.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/14
 */
public class MiningVO {

    private Integer type; // 类型: 0金子 1宝石 2宝箱
    private Integer rewardType; // -2钻石 4礼物
    private String sourceIcon; // 来源图标
    private String rewardIcon; // 奖励图标
    private Integer rewardNum; // 奖励数量
    private Integer rewardPrice; // 奖励价值
    private String tag; // 数量或天数文案
    private Integer hoeNum; // 锄头数量
    private Integer goldNum; // 金子数
    private List<MiningGridVO> gridList; // 网格
    private Integer firstMiningGold; // 首次挖金子 0否 1是

    public MiningVO() {
    }

    public MiningVO(Integer type, Integer rewardType, String sourceIcon, String rewardIcon, Integer rewardNum, Integer rewardPrice, String tag) {
        this.type = type;
        this.rewardType = rewardType;
        this.sourceIcon = sourceIcon;
        this.rewardIcon = rewardIcon;
        this.rewardNum = rewardNum;
        this.rewardPrice = rewardPrice;
        this.tag = tag;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getSourceIcon() {
        return sourceIcon;
    }

    public void setSourceIcon(String sourceIcon) {
        this.sourceIcon = sourceIcon;
    }

    public String getRewardIcon() {
        return rewardIcon;
    }

    public void setRewardIcon(String rewardIcon) {
        this.rewardIcon = rewardIcon;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Integer getHoeNum() {
        return hoeNum;
    }

    public void setHoeNum(Integer hoeNum) {
        this.hoeNum = hoeNum;
    }

    public Integer getGoldNum() {
        return goldNum;
    }

    public void setGoldNum(Integer goldNum) {
        this.goldNum = goldNum;
    }

    public List<MiningGridVO> getGridList() {
        return gridList;
    }

    public void setGridList(List<MiningGridVO> gridList) {
        this.gridList = gridList;
    }

    public Integer getRewardNum() {
        return rewardNum;
    }

    public void setRewardNum(Integer rewardNum) {
        this.rewardNum = rewardNum;
    }

    public Integer getFirstMiningGold() {
        return firstMiningGold;
    }

    public void setFirstMiningGold(Integer firstMiningGold) {
        this.firstMiningGold = firstMiningGold;
    }

    public Integer getRewardType() {
        return rewardType;
    }

    public void setRewardType(Integer rewardType) {
        this.rewardType = rewardType;
    }

    public Integer getRewardPrice() {
        return rewardPrice;
    }

    public void setRewardPrice(Integer rewardPrice) {
        this.rewardPrice = rewardPrice;
    }
}
