package com.quhong.data;


public class ResourceConfigData {
    private String name; // 资源名称
    private String nameAr; // 阿语资源名称
    private String desc; // 资源描述
    private String type; // 资源类型 gift、mic、buddle、ride、ripple、diamond、badge
    private int level; // 对应等级
    private int diamond; // 钻石
    private int sourceId; // 资源id
    private int sourceIdTest; // 资源id（测试服）
    private int day; // 资源有效期天数
    private String icon; // 资源url
    private String link; // 升级弹窗资源url
    private String startColor;
    private String endColor;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public int getSourceId() {
        return sourceId;
    }

    public int getSourceIdTest() {
        return sourceIdTest;
    }

    public void setSourceIdTest(int sourceIdTest) {
        this.sourceIdTest = sourceIdTest;
    }

    public void setSourceId(int sourceId) {
        this.sourceId = sourceId;
    }

    public int getDay() {
        return day;
    }

    public void setDay(int day) {
        this.day = day;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getStartColor() {
        return startColor;
    }

    public void setStartColor(String startColor) {
        this.startColor = startColor;
    }

    public String getEndColor() {
        return endColor;
    }

    public void setEndColor(String endColor) {
        this.endColor = endColor;
    }

    public int getDiamond() {
        return diamond;
    }

    public void setDiamond(int diamond) {
        this.diamond = diamond;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }
}
