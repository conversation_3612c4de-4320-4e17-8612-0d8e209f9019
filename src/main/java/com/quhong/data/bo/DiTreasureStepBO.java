package com.quhong.data.bo;

public class DiTreasureStepBO {
    /**
     * 频次
     */
    private Integer step;
    /**
     * 该频次需要的消耗
     */
    private Integer cost;
    /**
     * 剩余所有频次的总消耗
     */
    private Integer allCost;

    public DiTreasureStepBO() {
    }

    public DiTreasureStepBO(Integer step, Integer cost, Integer allCost) {
        this.step = step;
        this.cost = cost;
        this.allCost = allCost;
    }

    public Integer getStep() {
        return step;
    }

    public void setStep(Integer step) {
        this.step = step;
    }

    public Integer getCost() {
        return cost;
    }

    public void setCost(Integer cost) {
        this.cost = cost;
    }

    public Integer getAllCost() {
        return allCost;
    }

    public void setAllCost(Integer allCost) {
        this.allCost = allCost;
    }
}
