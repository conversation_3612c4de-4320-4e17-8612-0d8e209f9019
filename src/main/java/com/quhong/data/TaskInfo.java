package com.quhong.data;

import com.quhong.core.config.ServerConfig;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/20
 */
public class TaskInfo {

    private String key;
    private int type;
    private String name;
    private String nameAr;
    private String icon;
    private String webIcon;
    private int limit;
    private int rewardType;
    private int rewardNum;
    private int isStageTask;
    private String jumpCall;
    private String webJumpCall;
    private String stageTaskKey;
    private List<Reward> rewards;
    private String resourceKey;
    private String resourceKeyOther;

    public static class Reward {
        private String name;
        private String nameAr;
        private String icon;
        private int resId;
        private int resIdPro;
        private int resType;
        private int num;
        private int userType; // 1一个账号的新用户 2多个账号的新用户 3一个账号的老用户 4多个账号的老用户

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getNameAr() {
            return nameAr;
        }

        public void setNameAr(String nameAr) {
            this.nameAr = nameAr;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public int getResId() {
            return ServerConfig.isProduct() ? resIdPro : resId;
        }

        public void setResId(int resId) {
            this.resId = resId;
        }

        public int getResIdPro() {
            return resIdPro;
        }

        public void setResIdPro(int resIdPro) {
            this.resIdPro = resIdPro;
        }

        public int getResType() {
            return resType;
        }

        public void setResType(int resType) {
            this.resType = resType;
        }

        public int getNum() {
            return num;
        }

        public void setNum(int num) {
            this.num = num;
        }

        public int getUserType() {
            return userType;
        }

        public void setUserType(int userType) {
            this.userType = userType;
        }
    }


    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getWebIcon() {
        return webIcon;
    }

    public void setWebIcon(String webIcon) {
        this.webIcon = webIcon;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getRewardType() {
        return rewardType;
    }

    public void setRewardType(int rewardType) {
        this.rewardType = rewardType;
    }

    public int getRewardNum() {
        return rewardNum;
    }

    public void setRewardNum(int rewardNum) {
        this.rewardNum = rewardNum;
    }

    public int getIsStageTask() {
        return isStageTask;
    }

    public void setIsStageTask(int isStageTask) {
        this.isStageTask = isStageTask;
    }

    public String getStageTaskKey() {
        return stageTaskKey;
    }

    public void setStageTaskKey(String stageTaskKey) {
        this.stageTaskKey = stageTaskKey;
    }

    public List<Reward> getRewards() {
        return rewards;
    }

    public void setRewards(List<Reward> rewards) {
        this.rewards = rewards;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getJumpCall() {
        return jumpCall;
    }

    public void setJumpCall(String jumpCall) {
        this.jumpCall = jumpCall;
    }

    public String getWebJumpCall() {
        return webJumpCall;
    }

    public void setWebJumpCall(String webJumpCall) {
        this.webJumpCall = webJumpCall;
    }

    public String getResourceKey() {
        return resourceKey;
    }

    public void setResourceKey(String resourceKey) {
        this.resourceKey = resourceKey;
    }

    public String getResourceKeyOther() {
        return resourceKeyOther;
    }

    public void setResourceKeyOther(String resourceKeyOther) {
        this.resourceKeyOther = resourceKeyOther;
    }
}
