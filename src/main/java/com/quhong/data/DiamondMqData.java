package com.quhong.data;

/**
 * 钻石资源
 */
public class DiamondMqData extends ResourceMqData {
    private int diamond;// 钻石数
    private int act_type; // 如果是钻石的要传
    private String title;// 如果是钻石的要传
    private String act_desc;// 如果是钻石的要传

    public DiamondMqData() {
    }

    public DiamondMqData(String uid, int diamond, String desc) {
        this.uid = uid;
        this.stype = "diamond";
        this.diamond = diamond;
        this.act_type = 913;
        this.title = "Level Reward";
        this.act_desc = desc;
    }

    public int getDiamond() {
        return diamond;
    }

    public void setDiamond(int diamond) {
        this.diamond = diamond;
    }

    public int getAct_type() {
        return act_type;
    }

    public void setAct_type(int act_type) {
        this.act_type = act_type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAct_desc() {
        return act_desc;
    }

    public void setAct_desc(String act_desc) {
        this.act_desc = act_desc;
    }

    @Override
    public String toString() {
        return "DiamondMqData{" +
                "diamond=" + diamond +
                ", act_type=" + act_type +
                ", title='" + title + '\'' +
                ", act_desc='" + act_desc + '\'' +
                ", uid='" + uid + '\'' +
                ", stype='" + stype + '\'' +
                '}';
    }
}
