package com.quhong.data;


import java.util.List;

public class SuperQueenEntryConfigData {

    private int entryConfig = 1; // 1为账号性别为女性的用户；2为全量用户；3为隐藏入口; 4为白名单用户展示入口。默认配置为1
    private int startTime; // 0 关闭
    private int endTime; // 0 关闭
    private List<Integer> badgeList; // 勋章列表 依次为 热情，魅力，慷慨
    private String superQueenUrl ;

    public int getEntryConfig() {
        return entryConfig;
    }

    public void setEntryConfig(int entryConfig) {
        this.entryConfig = entryConfig;
    }

    public int getStartTime() {
        return startTime;
    }

    public void setStartTime(int startTime) {
        this.startTime = startTime;
    }

    public int getEndTime() {
        return endTime;
    }

    public void setEndTime(int endTime) {
        this.endTime = endTime;
    }

    public List<Integer> getBadgeList() {
        return badgeList;
    }

    public void setBadgeList(List<Integer> badgeList) {
        this.badgeList = badgeList;
    }

    public String getSuperQueenUrl() {
        return superQueenUrl;
    }

    public void setSuperQueenUrl(String superQueenUrl) {
        this.superQueenUrl = superQueenUrl;
    }
}
