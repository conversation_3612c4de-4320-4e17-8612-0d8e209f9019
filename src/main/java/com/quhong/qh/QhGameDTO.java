package com.quhong.qh;

public class QhGameDTO {

    public static class ChangeBalanceDTO extends BaseDTO {
        private int change; // 大于0为增加余额，小于零为减少余额
        private int logType; // -1下注，1返奖，2游戏费用退还
        private String roundId;

        public int getChange() {
            return change;
        }

        public void setChange(int change) {
            this.change = change;
        }

        public int getLogType() {
            return logType;
        }

        public void setLogType(int logType) {
            this.logType = logType;
        }

        public String getRoundId() {
            return roundId;
        }

        public void setRoundId(String roundId) {
            this.roundId = roundId;
        }
    }

    public static class RobotDTO extends BaseDTO {
        private int page;
        private int pageSize;

        public int getPage() {
            return page;
        }

        public void setPage(int page) {
            this.page = page;
        }

        public int getPageSize() {
            return pageSize;
        }

        public void setPageSize(int pageSize) {
            this.pageSize = pageSize;
        }
    }
}
