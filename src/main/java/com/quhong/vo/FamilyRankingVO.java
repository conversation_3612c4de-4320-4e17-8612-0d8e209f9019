package com.quhong.vo;


import java.util.ArrayList;
import java.util.List;

public class FamilyRankingVO {
    private List<RankingVO> rankingList = new ArrayList<>(30); // 排行榜
    private RankingVO myRanking; // 我的家族排行信息，未加入家族时为空

    public List<RankingVO> getRankingList() {
        return rankingList;
    }

    public void setRankingList(List<RankingVO> rankingList) {
        this.rankingList = rankingList;
    }

    public RankingVO getMyRanking() {
        return myRanking;
    }

    public void setMyRanking(RankingVO myRanking) {
        this.myRanking = myRanking;
    }

    public static class RankingVO {
        protected int rid; // 家族id
        protected String head;
        protected String name;
        protected String announce;
        protected String memberNum;
        protected String devote; // 贡献值
        protected String rank; // 排名

        public int getRid() {
            return rid;
        }

        public void setRid(int rid) {
            this.rid = rid;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getAnnounce() {
            return announce;
        }

        public void setAnnounce(String announce) {
            this.announce = announce;
        }

        public String getMemberNum() {
            return memberNum;
        }

        public void setMemberNum(String memberNum) {
            this.memberNum = memberNum;
        }

        public String getDevote() {
            return devote;
        }

        public void setDevote(String devote) {
            this.devote = devote;
        }

        public String getRank() {
            return rank;
        }

        public void setRank(String rank) {
            this.rank = rank;
        }
    }
}
