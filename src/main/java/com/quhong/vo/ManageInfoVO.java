package com.quhong.vo;


public class ManageInfoVO {
    private Integer rid; // 公会id
    private String head; // 公会封面
    private String name; // 公会名称
    private String announce; // 公会公告
    private Integer members; // 公会人数
    private Integer maxMember; // 公会会员最大限额
    private int role; // 家族身份 1公会长 2公会管理员
    private Integer createTime;
    private Integer createDays;
    private Integer dismissFamilyH;// 多少小时后家族解散，公会长可见
    private Integer familyStatus; // 家族状态 0 可解散 1 可撤销解散 公会长可见

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAnnounce() {
        return announce;
    }

    public void setAnnounce(String announce) {
        this.announce = announce;
    }

    public Integer getMembers() {
        return members;
    }

    public void setMembers(Integer members) {
        this.members = members;
    }

    public int getRole() {
        return role;
    }

    public void setRole(int role) {
        this.role = role;
    }

    public Integer getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Integer createTime) {
        this.createTime = createTime;
    }

    public Integer getCreateDays() {
        return createDays;
    }

    public void setCreateDays(Integer createDays) {
        this.createDays = createDays;
    }

    public Integer getDismissFamilyH() {
        return dismissFamilyH;
    }

    public void setDismissFamilyH(Integer dismissFamilyH) {
        this.dismissFamilyH = dismissFamilyH;
    }

    public Integer getMaxMember() {
        return maxMember;
    }

    public void setMaxMember(Integer maxMember) {
        this.maxMember = maxMember;
    }

    public Integer getFamilyStatus() {
        return familyStatus;
    }

    public void setFamilyStatus(Integer familyStatus) {
        this.familyStatus = familyStatus;
    }
}
