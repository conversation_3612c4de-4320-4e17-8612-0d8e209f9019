package com.quhong.vo;

import java.util.List;

public class FamilyRequestVO {

    private int reqId; // 申请id
    private String aid;
    private String name;
    private String head;
    private String micFrame;
    private int vipLevel;
    private int gender;
    private int age;
    private List<String> badgeList;
    private String content; // 申请内容
    private Integer status; // 0已查看待处理 1待处理 2已通过 3已拒绝
    private Integer ctime;
    private int uLevel;

    public int getReqId() {
        return reqId;
    }

    public void setReqId(int reqId) {
        this.reqId = reqId;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getMicFrame() {
        return micFrame;
    }

    public void setMicFrame(String micFrame) {
        this.micFrame = micFrame;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public List<String> getBadgeList() {
        return badgeList;
    }

    public void setBadgeList(List<String> badgeList) {
        this.badgeList = badgeList;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public int getuLevel() {
        return uLevel;
    }

    public void setuLevel(int uLevel) {
        this.uLevel = uLevel;
    }
}
