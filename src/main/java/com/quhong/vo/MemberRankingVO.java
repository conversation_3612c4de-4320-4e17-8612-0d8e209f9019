package com.quhong.vo;


import com.quhong.data.RidData;

import java.util.ArrayList;
import java.util.List;

public class MemberRankingVO {
    private List<RankingVO> rankingList = new ArrayList<>(30); // 排行榜
    private RankingVO myRanking; // 我的排行信息

    public List<RankingVO> getRankingList() {
        return rankingList;
    }

    public void setRankingList(List<RankingVO> rankingList) {
        this.rankingList = rankingList;
    }

    public RankingVO getMyRanking() {
        return myRanking;
    }

    public void setMyRanking(RankingVO myRanking) {
        this.myRanking = myRanking;
    }

    public static class RankingVO {
        protected RidData ridData;
        protected String aid;
        protected String head;
        protected String name;
        protected int gender;
        protected int age;
        protected int role; // 家族身份 1公会长 2公会管理员 3普通成员
        protected int vipLevel; // vip等级
        protected int level;  // 用户等级等级
        protected String devote; // 贡献值
        protected String rank; // 排名
        protected String previousDiffer; // 与上一名相差

        public RidData getRidData() {
            return ridData;
        }

        public void setRidData(RidData ridData) {
            this.ridData = ridData;
        }

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getGender() {
            return gender;
        }

        public void setGender(int gender) {
            this.gender = gender;
        }

        public int getAge() {
            return age;
        }

        public void setAge(int age) {
            this.age = age;
        }

        public int getRole() {
            return role;
        }

        public void setRole(int role) {
            this.role = role;
        }

        public int getVipLevel() {
            return vipLevel;
        }

        public void setVipLevel(int vipLevel) {
            this.vipLevel = vipLevel;
        }

        public int getLevel() {
            return level;
        }

        public void setLevel(int level) {
            this.level = level;
        }

        public String getDevote() {
            return devote;
        }

        public void setDevote(String devote) {
            this.devote = devote;
        }

        public String getRank() {
            return rank;
        }

        public void setRank(String rank) {
            this.rank = rank;
        }

        public String getPreviousDiffer() {
            return previousDiffer;
        }

        public void setPreviousDiffer(String previousDiffer) {
            this.previousDiffer = previousDiffer;
        }
    }
}
