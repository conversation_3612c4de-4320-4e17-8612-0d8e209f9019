package com.quhong.vo;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

public class FamilyTaskInfoVO implements Serializable {
    @JSONField(serialize = false)
    private String id; // 任务id
    private String icon; // 任务图标
    private String name; // 任务名字
    private String nameAr; // 任务名字
    private String desc; // 任务描述
    private String descAr; // 任务描述
    private String jumpCall; // 跳转行为
    private int completed; // 是否完成
    @JSONField(serialize = false)
    private int total; // 总次数

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDescAr() {
        return descAr;
    }

    public void setDescAr(String descAr) {
        this.descAr = descAr;
    }

    public String getJumpCall() {
        return jumpCall;
    }

    public void setJumpCall(String jumpCall) {
        this.jumpCall = jumpCall;
    }

    public int getCompleted() {
        return completed;
    }

    public void setCompleted(int completed) {
        this.completed = completed;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }
}
