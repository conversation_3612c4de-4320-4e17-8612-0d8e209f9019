package com.quhong.vo;

import com.quhong.config.TaskTurntableConfig;

import java.util.List;

public class TaskTurntableVO {

    private List<TaskTurntableConfig.CommonAwardConfig> primaryTurntableList;
    private List<TaskTurntableConfig.CommonAwardConfig> advancedTurntableList;
    private List<RollRecord> primaryRollList;
    private List<RollRecord> advancedRollList;
    private int todayFreeDraw;   // 今日是否已免费抽奖
    private int coinBalance;
    private int couponBalance;


    public static class RollRecord{
        private String name;
        private String head;
        private String nameEn;
        private String nameAr;
        private String numDayEn;
        private String numDayAr;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public String getNameEn() {
            return nameEn;
        }

        public void setNameEn(String nameEn) {
            this.nameEn = nameEn;
        }

        public String getNameAr() {
            return nameAr;
        }

        public void setNameAr(String nameAr) {
            this.nameAr = nameAr;
        }

        public String getNumDayEn() {
            return numDayEn;
        }

        public void setNumDayEn(String numDayEn) {
            this.numDayEn = numDayEn;
        }

        public String getNumDayAr() {
            return numDayAr;
        }

        public void setNumDayAr(String numDayAr) {
            this.numDayAr = numDayAr;
        }
    }

    public List<TaskTurntableConfig.CommonAwardConfig> getPrimaryTurntableList() {
        return primaryTurntableList;
    }

    public void setPrimaryTurntableList(List<TaskTurntableConfig.CommonAwardConfig> primaryTurntableList) {
        this.primaryTurntableList = primaryTurntableList;
    }

    public List<TaskTurntableConfig.CommonAwardConfig> getAdvancedTurntableList() {
        return advancedTurntableList;
    }

    public void setAdvancedTurntableList(List<TaskTurntableConfig.CommonAwardConfig> advancedTurntableList) {
        this.advancedTurntableList = advancedTurntableList;
    }

    public List<RollRecord> getPrimaryRollList() {
        return primaryRollList;
    }

    public void setPrimaryRollList(List<RollRecord> primaryRollList) {
        this.primaryRollList = primaryRollList;
    }

    public List<RollRecord> getAdvancedRollList() {
        return advancedRollList;
    }

    public void setAdvancedRollList(List<RollRecord> advancedRollList) {
        this.advancedRollList = advancedRollList;
    }

    public int getTodayFreeDraw() {
        return todayFreeDraw;
    }

    public void setTodayFreeDraw(int todayFreeDraw) {
        this.todayFreeDraw = todayFreeDraw;
    }

    public int getCoinBalance() {
        return coinBalance;
    }

    public void setCoinBalance(int coinBalance) {
        this.coinBalance = coinBalance;
    }

    public int getCouponBalance() {
        return couponBalance;
    }

    public void setCouponBalance(int couponBalance) {
        this.couponBalance = couponBalance;
    }
}
