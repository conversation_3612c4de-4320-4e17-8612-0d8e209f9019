package com.quhong.vo;

import com.quhong.mongo.dao.FamilyDevoteDao;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class FamilyUniVO {
    public static class FamilyHomeVO {
        private Integer rid; // 家族id
        private String head; // 家族封面
        private String name; // 家族名称
        private String announce; // 家族公告

        private int maxMember; // 家族会员最大限额
        private int curMember; // 当前家族会员数量

        private int rank; // 家族排名 TOP30家族显示，低于TOP30家族不显示(0)。
        private int rankType; // 1Daily 2Weekly 3Monthly

        private int familyLevel; // 家族等级，[1,6]
        private int star; // 星级，[1,5]
        private long monthlyScore; // 本月战力值
        private long relegationScore; // 保级所需战力值
        private long curLevelScore; // 家族当前战力值
        private long nextLevelScore; // 家族下一等级战力值
        private long monthEnd; // 本月结束时间(秒)

        private int isAdmin; // 是否家族管理员
        private int isOwner; // 是否家族长

        private int memberStatus; // -2已加入当前家族 -1已加入其他家族 0未申请 大于0为正在申请中，值为申请的家族rid

        public Integer getRid() {
            return rid;
        }

        public void setRid(Integer rid) {
            this.rid = rid;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getAnnounce() {
            return announce;
        }

        public void setAnnounce(String announce) {
            this.announce = announce;
        }

        public int getMaxMember() {
            return maxMember;
        }

        public void setMaxMember(int maxMember) {
            this.maxMember = maxMember;
        }

        public int getCurMember() {
            return curMember;
        }

        public void setCurMember(int curMember) {
            this.curMember = curMember;
        }

        public int getRank() {
            return rank;
        }

        public void setRank(int rank) {
            this.rank = rank;
        }

        public int getRankType() {
            return rankType;
        }

        public void setRankType(int rankType) {
            this.rankType = rankType;
        }

        public int getFamilyLevel() {
            return familyLevel;
        }

        public void setFamilyLevel(int familyLevel) {
            this.familyLevel = familyLevel;
        }

        public int getStar() {
            return star;
        }

        public void setStar(int star) {
            this.star = star;
        }

        public long getMonthlyScore() {
            return monthlyScore;
        }

        public void setMonthlyScore(long monthlyScore) {
            this.monthlyScore = monthlyScore;
        }

        public long getRelegationScore() {
            return relegationScore;
        }

        public void setRelegationScore(long relegationScore) {
            this.relegationScore = relegationScore;
        }

        public long getCurLevelScore() {
            return curLevelScore;
        }

        public void setCurLevelScore(long curLevelScore) {
            this.curLevelScore = curLevelScore;
        }

        public long getNextLevelScore() {
            return nextLevelScore;
        }

        public void setNextLevelScore(long nextLevelScore) {
            this.nextLevelScore = nextLevelScore;
        }

        public long getMonthEnd() {
            return monthEnd;
        }

        public void setMonthEnd(long monthEnd) {
            this.monthEnd = monthEnd;
        }

        public int getIsAdmin() {
            return isAdmin;
        }

        public void setIsAdmin(int isAdmin) {
            this.isAdmin = isAdmin;
        }

        public int getIsOwner() {
            return isOwner;
        }

        public void setIsOwner(int isOwner) {
            this.isOwner = isOwner;
        }

        public int getMemberStatus() {
            return memberStatus;
        }

        public void setMemberStatus(int memberStatus) {
            this.memberStatus = memberStatus;
        }
    }

    public static class FamilyTaskVO {
        private long earnToday;
        private long accumulation;
        private List<FamilyTaskInfoVO> taskList = new ArrayList<>();

        public long getEarnToday() {
            return earnToday;
        }

        public void setEarnToday(long earnToday) {
            this.earnToday = earnToday;
        }

        public long getAccumulation() {
            return accumulation;
        }

        public void setAccumulation(long accumulation) {
            this.accumulation = accumulation;
        }

        public List<FamilyTaskInfoVO> getTaskList() {
            return taskList;
        }

        public void setTaskList(List<FamilyTaskInfoVO> taskList) {
            this.taskList = taskList;
        }
    }

    public static class FamilyProfileVO {
        private List<FamilyDevoteDao.FamilyMemberTop1Ranking> ranking = Collections.emptyList(); // 分别对应top1的 Daily Weekly Monthly
        private MemberVO members = new MemberVO();

        public List<FamilyDevoteDao.FamilyMemberTop1Ranking> getRanking() {
            return ranking;
        }

        public void setRanking(List<FamilyDevoteDao.FamilyMemberTop1Ranking> ranking) {
            this.ranking = ranking;
        }

        public MemberVO getMembers() {
            return members;
        }

        public void setMembers(MemberVO members) {
            this.members = members;
        }
    }

    public static class MemberVO {
        private int maxMember; // 家族会员最大限额
        private int curMember; // 当前家族会员数量
        private List<MemberDetailVO> members = new ArrayList<>();

        public int getMaxMember() {
            return maxMember;
        }

        public void setMaxMember(int maxMember) {
            this.maxMember = maxMember;
        }

        public int getCurMember() {
            return curMember;
        }

        public void setCurMember(int curMember) {
            this.curMember = curMember;
        }

        public List<MemberDetailVO> getMembers() {
            return members;
        }

        public void setMembers(List<MemberDetailVO> members) {
            this.members = members;
        }
    }

    public static class MemberDetailVO {
        private String aid;
        private String name;
        private String head;
        private int role; // 家族角色

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public int getRole() {
            return role;
        }

        public void setRole(int role) {
            this.role = role;
        }
    }
}

