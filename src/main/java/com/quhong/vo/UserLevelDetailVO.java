package com.quhong.vo;

public class UserLevelDetailVO {
    private int level; // 当前等级
    private long exp; // 当前经验值
    private long curLevelExp; // 当前等级的经验值
    private long levelUpExp; // 下一等级的经验值
    private long levelUpNeedExp; // 升级所需经验值
    // 固定配置
    private int per; // 每份值（最低限额）
    private int points; // 分数

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public long getExp() {
        return exp;
    }

    public void setExp(long exp) {
        this.exp = exp;
    }

    public long getCurLevelExp() {
        return curLevelExp;
    }

    public void setCurLevelExp(long curLevelExp) {
        this.curLevelExp = curLevelExp;
    }

    public long getLevelUpExp() {
        return levelUpExp;
    }

    public void setLevelUpExp(long levelUpExp) {
        this.levelUpExp = levelUpExp;
    }

    public long getLevelUpNeedExp() {
        return levelUpNeedExp;
    }

    public void setLevelUpNeedExp(long levelUpNeedExp) {
        this.levelUpNeedExp = levelUpNeedExp;
    }

    public int getPer() {
        return per;
    }

    public void setPer(int per) {
        this.per = per;
    }

    public int getPoints() {
        return points;
    }

    public void setPoints(int points) {
        this.points = points;
    }
}
