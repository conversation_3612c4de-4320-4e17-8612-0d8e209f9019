package com.quhong.vo;

import java.util.List;

public class TaskTurntableRecordVO {

    private List<Record> recordList;
    private Integer nextUrl;


    public static class Record{
        private String iconEn;
        private String iconAr;
        private String nameEn;
        private String nameAr;
        private String numDayEn;
        private String numDayAr;
        private Integer amount;
        private Integer ctime;

        public String getIconEn() {
            return iconEn;
        }

        public void setIconEn(String iconEn) {
            this.iconEn = iconEn;
        }

        public String getIconAr() {
            return iconAr;
        }

        public void setIconAr(String iconAr) {
            this.iconAr = iconAr;
        }

        public String getNameEn() {
            return nameEn;
        }

        public void setNameEn(String nameEn) {
            this.nameEn = nameEn;
        }

        public String getNameAr() {
            return nameAr;
        }

        public void setNameAr(String nameAr) {
            this.nameAr = nameAr;
        }

        public String getNumDayEn() {
            return numDayEn;
        }

        public void setNumDayEn(String numDayEn) {
            this.numDayEn = numDayEn;
        }

        public String getNumDayAr() {
            return numDayAr;
        }

        public void setNumDayAr(String numDayAr) {
            this.numDayAr = numDayAr;
        }

        public Integer getAmount() {
            return amount;
        }

        public void setAmount(Integer amount) {
            this.amount = amount;
        }

        public Integer getCtime() {
            return ctime;
        }

        public void setCtime(Integer ctime) {
            this.ctime = ctime;
        }
    }

    public List<Record> getRecordList() {
        return recordList;
    }

    public void setRecordList(List<Record> recordList) {
        this.recordList = recordList;
    }

    public Integer getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(Integer nextUrl) {
        this.nextUrl = nextUrl;
    }
}
