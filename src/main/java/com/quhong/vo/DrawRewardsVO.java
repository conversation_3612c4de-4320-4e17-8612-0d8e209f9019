package com.quhong.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/17
 */
public class DrawRewardsVO {

    private long coins;

    private long diamonds;

    private List<TaskVO.AwardVO> list;

    public DrawRewardsVO() {
    }

    public DrawRewardsVO(long coins, long diamonds, List<TaskVO.AwardVO> list) {
        this.coins = coins;
        this.diamonds = diamonds;
        this.list = list;
    }

    public long getCoins() {
        return coins;
    }

    public void setCoins(long coins) {
        this.coins = coins;
    }

    public long getDiamonds() {
        return diamonds;
    }

    public void setDiamonds(long diamonds) {
        this.diamonds = diamonds;
    }

    public List<TaskVO.AwardVO> getList() {
        return list;
    }

    public void setList(List<TaskVO.AwardVO> list) {
        this.list = list;
    }
}
