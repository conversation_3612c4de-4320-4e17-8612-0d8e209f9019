package com.quhong.vo;

import java.io.Serializable;
import java.util.List;

public class TaskVO implements Serializable {

    private int task_id;
    private String name;
    private String namear;
    private int task_value;
    private int task_num;
    private int finish_value;
    private int finish_percent;
    private int hot_task;
    private int award_status;
    private String jump_call;
    private List<AwardVO> award_list;
    private int m_time;
    private int c_time;
    // 任务完成进度按时间计算 0否 1是
    private int time_calculation;

    public void incrTaskValue() {
        this.task_value = task_value + 1;
    }

    public void incrFinishValue() {
        this.finish_value = finish_value + 1;
    }

    public int getTask_id() {
        return task_id;
    }

    public void setTask_id(int task_id) {
        this.task_id = task_id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNamear() {
        return namear;
    }

    public void setNamear(String namear) {
        this.namear = namear;
    }

    public int getTask_value() {
        return task_value;
    }

    public void setTask_value(int task_value) {
        this.task_value = task_value;
    }

    public int getFinish_value() {
        return finish_value;
    }

    public void setFinish_value(int finish_value) {
        this.finish_value = finish_value;
    }

    public int getHot_task() {
        return hot_task;
    }

    public void setHot_task(int hot_task) {
        this.hot_task = hot_task;
    }

    public int getAward_status() {
        return award_status;
    }

    public void setAward_status(int award_status) {
        this.award_status = award_status;
    }

    public String getJump_call() {
        return jump_call;
    }

    public void setJump_call(String jump_call) {
        this.jump_call = jump_call;
    }

    public List<AwardVO> getAward_list() {
        return award_list;
    }

    public void setAward_list(List<AwardVO> award_list) {
        this.award_list = award_list;
    }

    public int getM_time() {
        return m_time;
    }

    public void setM_time(int m_time) {
        this.m_time = m_time;
    }

    public int getC_time() {
        return c_time;
    }

    public void setC_time(int c_time) {
        this.c_time = c_time;
    }

    public int getTime_calculation() {
        return time_calculation;
    }

    public void setTime_calculation(int time_calculation) {
        this.time_calculation = time_calculation;
    }

    public int getFinish_percent() {
        return finish_percent;
    }

    public void setFinish_percent(int finish_percent) {
        this.finish_percent = finish_percent;
    }

    public int getTask_num() {
        return task_num;
    }

    public void setTask_num(int task_num) {
        this.task_num = task_num;
    }

    public static class AwardVO implements Serializable{
        private String img_src;
        private String award_title;
        private String award_artitle;
        private int atype;
        private int award_num;

        public String getImg_src() {
            return img_src;
        }

        public void setImg_src(String img_src) {
            this.img_src = img_src;
        }

        public String getAward_title() {
            return award_title;
        }

        public void setAward_title(String award_title) {
            this.award_title = award_title;
        }

        public String getAward_artitle() {
            return award_artitle;
        }

        public void setAward_artitle(String award_artitle) {
            this.award_artitle = award_artitle;
        }

        public int getAtype() {
            return atype;
        }

        public void setAtype(int atype) {
            this.atype = atype;
        }

        public int getAward_num() {
            return award_num;
        }

        public void setAward_num(int award_num) {
            this.award_num = award_num;
        }
    }
}
