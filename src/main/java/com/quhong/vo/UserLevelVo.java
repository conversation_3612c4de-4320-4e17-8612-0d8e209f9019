package com.quhong.vo;

import java.util.List;

public class UserLevelVo {
    private String uid;
    private int level;
    private long exp;
    private double[] expRate;
    private double levelRate;
    private long activeExp;
    private long charmExp;
    private long wealthExp;
    private int nextLevel;
    private long curLevelExp;
    private long levelUpExp;
    private long levelUpNeedExp;
    private int todayExp;
    private String levelUrl;

    private List<ResourceVo> levelBadgeList;
    private List<ResourceVo> levelChatBubbleList;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public long getExp() {
        return exp;
    }

    public void setExp(long exp) {
        this.exp = exp;
    }

    public double[] getExpRate() {
        return expRate;
    }

    public void setExpRate(double[] expRate) {
        this.expRate = expRate;
    }

    public double getLevelRate() {
        return levelRate;
    }

    public void setLevelRate(double levelRate) {
        this.levelRate = levelRate;
    }

    public long getActiveExp() {
        return activeExp;
    }

    public void setActiveExp(long activeExp) {
        this.activeExp = activeExp;
    }

    public long getCharmExp() {
        return charmExp;
    }

    public void setCharmExp(long charmExp) {
        this.charmExp = charmExp;
    }

    public long getWealthExp() {
        return wealthExp;
    }

    public void setWealthExp(long wealthExp) {
        this.wealthExp = wealthExp;
    }

    public int getNextLevel() {
        return nextLevel;
    }

    public void setNextLevel(int nextLevel) {
        this.nextLevel = nextLevel;
    }

    public long getCurLevelExp() {
        return curLevelExp;
    }

    public void setCurLevelExp(long curLevelExp) {
        this.curLevelExp = curLevelExp;
    }

    public long getLevelUpExp() {
        return levelUpExp;
    }

    public void setLevelUpExp(long levelUpExp) {
        this.levelUpExp = levelUpExp;
    }

    public long getLevelUpNeedExp() {
        return levelUpNeedExp;
    }

    public void setLevelUpNeedExp(long levelUpNeedExp) {
        this.levelUpNeedExp = levelUpNeedExp;
    }

    public int getTodayExp() {
        return todayExp;
    }

    public void setTodayExp(int todayExp) {
        this.todayExp = todayExp;
    }

    public String getLevelUrl() {
        return levelUrl;
    }

    public void setLevelUrl(String levelUrl) {
        this.levelUrl = levelUrl;
    }

    public List<ResourceVo> getLevelBadgeList() {
        return levelBadgeList;
    }

    public void setLevelBadgeList(List<ResourceVo> levelBadgeList) {
        this.levelBadgeList = levelBadgeList;
    }

    public List<ResourceVo> getLevelChatBubbleList() {
        return levelChatBubbleList;
    }

    public void setLevelChatBubbleList(List<ResourceVo> levelChatBubbleList) {
        this.levelChatBubbleList = levelChatBubbleList;
    }
}
