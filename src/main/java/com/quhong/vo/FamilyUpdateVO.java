package com.quhong.vo;

/**
 * <AUTHOR>
 * @date 2023/6/2
 */
public class FamilyUpdateVO {
    private String name; // 公会名称，不为空时更新
    private String head; // 公会头像，不为空时更新
    private String announce; // 公告内容，不为空时更新
    private Integer familyRid; // 公会rid

    public FamilyUpdateVO() {
    }

    public FamilyUpdateVO(String name, String head, String announce, Integer familyRid) {
        this.name = name;
        this.head = head;
        this.announce = announce;
        this.familyRid = familyRid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getAnnounce() {
        return announce;
    }

    public void setAnnounce(String announce) {
        this.announce = announce;
    }

    public Integer getFamilyRid() {
        return familyRid;
    }

    public void setFamilyRid(Integer familyRid) {
        this.familyRid = familyRid;
    }
}
