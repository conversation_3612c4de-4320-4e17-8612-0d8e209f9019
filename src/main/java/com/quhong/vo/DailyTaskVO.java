package com.quhong.vo;

import java.util.List;

public class DailyTaskVO {

    private int sign_today;
    private int sign_end_time;
    private int getTaskNum;
    private TaskVO mission_task;
    private List<TaskVO> novice_task;
    private List<TaskVO> daily_task;
    private List<TaskVO> more_task;

    public int getSign_today() {
        return sign_today;
    }

    public void setSign_today(int sign_today) {
        this.sign_today = sign_today;
    }

    public int getSign_end_time() {
        return sign_end_time;
    }

    public void setSign_end_time(int sign_end_time) {
        this.sign_end_time = sign_end_time;
    }

    public int getGetTaskNum() {
        return getTaskNum;
    }

    public void setGetTaskNum(int getTaskNum) {
        this.getTaskNum = getTaskNum;
    }

    public TaskVO getMission_task() {
        return mission_task;
    }

    public void setMission_task(TaskVO mission_task) {
        this.mission_task = mission_task;
    }

    public List<TaskVO> getNovice_task() {
        return novice_task;
    }

    public void setNovice_task(List<TaskVO> novice_task) {
        this.novice_task = novice_task;
    }

    public List<TaskVO> getDaily_task() {
        return daily_task;
    }

    public void setDaily_task(List<TaskVO> daily_task) {
        this.daily_task = daily_task;
    }

    public List<TaskVO> getMore_task() {
        return more_task;
    }

    public void setMore_task(List<TaskVO> more_task) {
        this.more_task = more_task;
    }
}
