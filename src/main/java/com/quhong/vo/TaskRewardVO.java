package com.quhong.vo;

import com.quhong.data.ResourceMetaData;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/22
 */
public class TaskRewardVO {

    /**
     * 金币余额
     */
    private Long coinBalance;

    /**
     * 抽奖卷数量
     */
    private Integer lotteryTicketNum;

    private Reward reward;

    private List<ResourceMetaData> rewardConfigList;

    public static class Reward {
        private String name;
        private String icon;
        private int resType; //奖励类型 0抽奖卷 1 勋章 2 麦位框 3 坐骑 4 背包礼物  5 房间锁 6 聊天气泡 7 声波纹 8 浮屏 9 个人背景资源
        private int num;
        private String wheelUrl; //转盘地址


        public Reward() {
        }

        public Reward(String name, String icon, int resType, int num, String wheelUrl) {
            this.name = name;
            this.icon = icon;
            this.resType = resType;
            this.num = num;
            this.wheelUrl = wheelUrl;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }


        public int getNum() {
            return num;
        }

        public void setNum(int num) {
            this.num = num;
        }

        public int getResType() {
            return resType;
        }

        public void setResType(int resType) {
            this.resType = resType;
        }

        public String getWheelUrl() {
            return wheelUrl;
        }

        public void setWheelUrl(String wheelUrl) {
            this.wheelUrl = wheelUrl;
        }
    }

    public Long getCoinBalance() {
        return coinBalance;
    }

    public void setCoinBalance(Long coinBalance) {
        this.coinBalance = coinBalance;
    }

    public Integer getLotteryTicketNum() {
        return lotteryTicketNum;
    }

    public void setLotteryTicketNum(Integer lotteryTicketNum) {
        this.lotteryTicketNum = lotteryTicketNum;
    }

    public Reward getReward() {
        return reward;
    }

    public void setReward(Reward reward) {
        this.reward = reward;
    }

    public List<ResourceMetaData> getRewardConfigList() {
        return rewardConfigList;
    }

    public void setRewardConfigList(List<ResourceMetaData> rewardConfigList) {
        this.rewardConfigList = rewardConfigList;
    }
}
