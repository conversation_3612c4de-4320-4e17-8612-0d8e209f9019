package com.quhong.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/20
 */
public class StageTaskVO {

    /**
     * 任务名
     */
    private String name;
    /**
     * 完成进度
     */
    private Integer num;
    /**
     * 跳转房间
     */
    private String jumpRoomId;

    /**
     * 客户端跳转短链
     */
    private String jumpCall;
    private String webJumpCall;

    /**
     * 宝箱列表
     */
    private List<Reward> rewards;

    public static class Reward {

        private String key;
        private Integer limit;
        private Integer status;

        public Reward() {
        }

        public Reward(String key, Integer limit, Integer status) {
            this.key = key;
            this.limit = limit;
            this.status = status;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public Integer getLimit() {
            return limit;
        }

        public void setLimit(Integer limit) {
            this.limit = limit;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public List<Reward> getRewards() {
        return rewards;
    }

    public void setRewards(List<Reward> rewards) {
        this.rewards = rewards;
    }

    public String getJumpRoomId() {
        return jumpRoomId;
    }

    public void setJumpRoomId(String jumpRoomId) {
        this.jumpRoomId = jumpRoomId;
    }

    public String getJumpCall() {
        return jumpCall;
    }

    public void setJumpCall(String jumpCall) {
        this.jumpCall = jumpCall;
    }

    public String getWebJumpCall() {
        return webJumpCall;
    }

    public void setWebJumpCall(String webJumpCall) {
        this.webJumpCall = webJumpCall;
    }
}
