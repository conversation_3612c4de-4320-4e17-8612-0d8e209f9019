package com.quhong.vo;

import com.quhong.mongo.data.ResourceKeyConfigData;

/**
 * <AUTHOR>
 * @date 2023/9/20
 */
public class CommonTaskVO {

    /**
     * 任务key
     */
    private String key;

    /**
     * 任务名称
     */
    private String name;
    /**
     * 任务图标
     */
    private String icon;
    private String webIcon;
    /**
     * 任务完成数量
     */
    private Integer num;
    /**
     * 最大限制
     */
    private Integer limit;
    /**
     * 奖励类型 0金币 1抽奖卷
     */
    private Integer rewardType;
    /**
     * 奖励数量
     */
    private Integer rewardNum;
    /**
     * 状态 0未完成 1待领取 2已领取
     */
    private Integer status;

    /**
     * 跳转房间
     */
    private String jumpRoomId;

    /**
     * 跳转地址
     */
    private String jumpUrl;

    /**
     * 客户端跳转短链
     */
    private String jumpCall;
    private String webJumpCall;
    /**
     * h5版奖励资源key
     */
    private ResourceKeyConfigData resourceKeyConfigData;

    /**
     * 任务结束时间戳
     */
    private Integer countdownTime;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getWebIcon() {
        return webIcon;
    }

    public void setWebIcon(String webIcon) {
        this.webIcon = webIcon;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getRewardType() {
        return rewardType;
    }

    public void setRewardType(Integer rewardType) {
        this.rewardType = rewardType;
    }

    public Integer getRewardNum() {
        return rewardNum;
    }

    public void setRewardNum(Integer rewardNum) {
        this.rewardNum = rewardNum;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getJumpRoomId() {
        return jumpRoomId;
    }

    public void setJumpRoomId(String jumpRoomId) {
        this.jumpRoomId = jumpRoomId;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public String getJumpCall() {
        return jumpCall;
    }

    public void setJumpCall(String jumpCall) {
        this.jumpCall = jumpCall;
    }

    public String getWebJumpCall() {
        return webJumpCall;
    }

    public void setWebJumpCall(String webJumpCall) {
        this.webJumpCall = webJumpCall;
    }

    public ResourceKeyConfigData getResourceKeyConfigData() {
        return resourceKeyConfigData;
    }

    public void setResourceKeyConfigData(ResourceKeyConfigData resourceKeyConfigData) {
        this.resourceKeyConfigData = resourceKeyConfigData;
    }

    public Integer getCountdownTime() {
        return countdownTime;
    }

    public void setCountdownTime(Integer countdownTime) {
        this.countdownTime = countdownTime;
    }
}
