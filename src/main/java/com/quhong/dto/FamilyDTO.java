package com.quhong.dto;

import com.alibaba.fastjson.JSON;
import com.quhong.handler.HttpEnvData;


public class FamilyDTO extends HttpEnvData {

    private Integer familyRid; // 公会id，FamilyData的rid

    public Integer getFamilyRid() {
        return familyRid;
    }

    public void setFamilyRid(Integer familyRid) {
        this.familyRid = familyRid;
    }

    public static class Members extends FamilyDTO {
        private int page; // 页码，从1开始
        private int membersSortBy; // 公会成员列表排序 0默认排序 1贡献降序 2贡献升序 3加入时间降序 4加入时间升序
        private String searchKey; // 搜索key，目前仅支持数字搜索

        public int getPage() {
            return page;
        }

        public void setPage(int page) {
            this.page = page;
        }

        public int getMembersSortBy() {
            return membersSortBy;
        }

        public void setMembersSortBy(int membersSortBy) {
            this.membersSortBy = membersSortBy;
        }

        public String getSearchKey() {
            return searchKey;
        }

        public void setSearchKey(String searchKey) {
            this.searchKey = searchKey;
        }
    }

    public static class Ranking extends FamilyDTO {
        private String startDate; // 开始时间 yyyy-MM-dd
        private String endDate; // 结束时间 yyyy-MM-dd
        private Integer rankType;  // 成员榜使用: 0: 日榜 1: 周榜 2: 月榜  3: 总榜

        public String getStartDate() {
            return startDate;
        }

        public void setStartDate(String startDate) {
            this.startDate = startDate;
        }

        public String getEndDate() {
            return endDate;
        }

        public void setEndDate(String endDate) {
            this.endDate = endDate;
        }

        public Integer getRankType() {
            return rankType;
        }

        public void setRankType(Integer rankType) {
            this.rankType = rankType;
        }
    }

    public static class RequestList extends FamilyDTO {
        private int page; // 页码，从1开始
        private int type; // 0加入申请 1退出申请   0 推荐 1 优质

        public int getPage() {
            return page;
        }

        public void setPage(int page) {
            this.page = page;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }
    }

    public static class Square extends FamilyDTO {
        private int page; // 页码，从1开始

        public int getPage() {
            return page;
        }

        public void setPage(int page) {
            this.page = page;
        }
    }

    public static class Operate extends FamilyDTO {
        private String aid;

        private Integer operateType; // 1将成员为管理员 2将管理员为普通成员 3移除成员

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public Integer getOperateType() {
            return operateType;
        }

        public void setOperateType(Integer operateType) {
            this.operateType = operateType;
        }
    }

    public static class Quit extends FamilyDTO {
        private String aid;

        private Integer operateType; // 1 解散家族或者退出家族 2 取消解散

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public Integer getOperateType() {
            return operateType;
        }

        public void setOperateType(Integer operateType) {
            this.operateType = operateType;
        }
    }

    public static class Approval extends FamilyDTO {
        private Integer reqId; // 申请id
        private Integer approvalType; // 1通过申请 2拒绝申请 3删除申请(用户主动撤销时无需传reqId)
        private int type; // 0加入申请 1退出申请

        public Integer getReqId() {
            return reqId;
        }

        public void setReqId(Integer reqId) {
            this.reqId = reqId;
        }

        public Integer getApprovalType() {
            return approvalType;
        }

        public void setApprovalType(Integer approvalType) {
            this.approvalType = approvalType;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }
    }

    public static class UpdateInfo extends FamilyDTO {
        private String name; // 公会名称，不为空时更新
        private String head; // 公会头像，不为空时更新
        private String announce; // 公告内容，不为空时更新
        private Integer familyRid; // 公会rid

        public UpdateInfo() {
        }

        public UpdateInfo(String name, String head, String announce, Integer familyRid) {
            this.name = name;
            this.head = head;
            this.announce = announce;
            this.familyRid = familyRid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public String getAnnounce() {
            return announce;
        }

        public void setAnnounce(String announce) {
            this.announce = announce;
        }

        @Override
        public Integer getFamilyRid() {
            return familyRid;
        }

        @Override
        public void setFamilyRid(Integer familyRid) {
            this.familyRid = familyRid;
        }
    }

    public static class Anchor extends FamilyDTO {
        private Integer anchorRid; // 搜索的主播rid，可以为空
        private String anchorName; // 搜索的主播昵称
        private int page; // 从1开始
        /**
         * 排序方式 ：
         * 0默认排序 1按工作天数升序 2按工作天数降序
         * 3按金币收入升序 4按金币收入降序 5按房间总收入升序
         * 6按房间总收入降序 7按打赏人数升序 8按打赏人数降序
         * 9按游戏总支出升序 10按游戏总支出降序
         */
        private int sortBy;
        private int stime; // 报表开始时间
        private int etime; // 报表结束时间
        private String email; // 报表接受邮件地址
        private String fieldKey; // 数据字段key

        public String getAnchorName() {
            return anchorName;
        }

        public void setAnchorName(String anchorName) {
            this.anchorName = anchorName;
        }

        public Integer getAnchorRid() {
            return anchorRid;
        }

        public void setAnchorRid(Integer anchorRid) {
            this.anchorRid = anchorRid;
        }

        public int getPage() {
            return page;
        }

        public void setPage(int page) {
            this.page = page;
        }

        public int getStime() {
            return stime;
        }

        public void setStime(int stime) {
            this.stime = stime;
        }

        public int getEtime() {
            return etime;
        }

        public void setEtime(int etime) {
            this.etime = etime;
        }

        public int getSortBy() {
            return sortBy;
        }

        public void setSortBy(int sortBy) {
            this.sortBy = sortBy;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getFieldKey() {
            return fieldKey;
        }

        public void setFieldKey(String fieldKey) {
            this.fieldKey = fieldKey;
        }
    }

    public static class Request extends FamilyDTO {
        private String aid;
        private String content; // 申请加入公会时的验证内容
        private int type; // 0加入申请 1退出申请

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
