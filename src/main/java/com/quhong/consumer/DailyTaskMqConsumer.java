package com.quhong.consumer;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.config.DailyTaskCenterConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.DailyTaskService;
import com.quhong.data.DailyTaskMqData;
import com.quhong.msg.room.RoomNoviceFinishPushMsg;
import com.quhong.mysql.dao.DailyTaskManagerDao;
import com.quhong.mysql.data.DailyTaskManager;
import com.quhong.mysql.data.OnceTaskRecord;
import com.quhong.room.RoomWebSender;
import com.quhong.service.DailyTaskCenterService;
import com.quhong.service.OnceTaskService;
import com.quhong.vo.TaskVO;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/14
 */
@Component
public class DailyTaskMqConsumer {

    private static final Logger logger = LoggerFactory.getLogger(DailyTaskMqConsumer.class);

    private static final Interner<String> stringPool = Interners.newWeakInterner();
    public static final String DAILY_TASK_LOCK_KEY = "daily_task_handle_";

    @Resource
    private DailyTaskManagerDao dailyTaskManagerDao;
    @Resource
    private DailyTaskCenterService dailyTaskCenterService;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private DailyTaskCenterConfig taskCenterConfig;
    @Resource
    private OnceTaskService onceTaskService;

    // @RabbitListener(containerFactory = "dailyTaskListenerFactory", queuesToDeclare = @Queue(DailyTaskService.ROUTE_KEY))
    public void handleMessage(Channel channel, Message message) {
        try {
            String json = new String(message.getBody());
            // logger.info("receive daily task rabbit mq message, message body={}", json);
            DailyTaskMqData dailyTaskMqData = JSON.parseObject(json, DailyTaskMqData.class);
            if (dailyTaskMqData == null) {
                return;
            }
            Map<Integer, TaskVO> taskMap = dailyTaskCenterService.getTaskMap();
            TaskVO dailyTaskData = taskMap.get(dailyTaskMqData.getTask_id());
            if (dailyTaskData == null) {
                logger.info("can not find daily task data. taskId={}", dailyTaskMqData.getTask_id());
                return;
            }
            boolean finishFlag = false;
            int nowTime = DateHelper.getNowSeconds();
            int num = dailyTaskMqData.getNum() != 0 ? dailyTaskMqData.getNum() : 1;
            int dateKey = Integer.parseInt(dailyTaskMqData.getFinish_date());
            List<Integer> noviceTaskIds = taskCenterConfig.getNovice_task().stream().map(TaskVO::getTask_id).collect(Collectors.toList());
            synchronized (stringPool.intern(DAILY_TASK_LOCK_KEY + dailyTaskMqData.getUid())) {
                if (noviceTaskIds.contains(dailyTaskMqData.getTask_id())) {
                    // 一次性任务
                    OnceTaskRecord onceTaskRecord = onceTaskService.getByUidAndTaskId(dailyTaskMqData.getUid(), dailyTaskMqData.getTask_id());
                    if (onceTaskRecord != null) {
                        if (num >= dailyTaskData.getTask_num() && onceTaskRecord.getAwardStatus() == 0) {
                            finishFlag = true;
                            onceTaskRecord.setDateKey(dateKey);
                            onceTaskRecord.setAwardStatus(1);
                            onceTaskRecord.setmTime(nowTime);
                            onceTaskService.updateById(onceTaskRecord);
                        }
                    } else {
                        onceTaskRecord = new OnceTaskRecord();
                        onceTaskRecord.setUid(dailyTaskMqData.getUid());
                        onceTaskRecord.setDateKey(dateKey);
                        onceTaskRecord.setTaskId(dailyTaskMqData.getTask_id());
                        onceTaskRecord.setAwardStatus(0);
                        onceTaskRecord.setcTime(nowTime);
                        onceTaskRecord.setmTime(nowTime);
                        if (num >= dailyTaskData.getTask_num()) {
                            finishFlag = true;
                            onceTaskRecord.setAwardStatus(1);
                        }
                        onceTaskService.save(onceTaskRecord);
                    }
                } else {
                    // 日常任务
                    DailyTaskManager oldRecord = dailyTaskManagerDao.getTaskById(dailyTaskMqData.getUid(), dailyTaskMqData.getFinish_date(), dailyTaskMqData.getTask_id());
                    if (oldRecord != null) {
                        oldRecord.setTaskValue(oldRecord.getTaskValue() + num);
                        if (oldRecord.getTaskValue() >= dailyTaskData.getTask_value() && oldRecord.getTaskValue() - num < dailyTaskData.getTask_value()) {
                            if (oldRecord.getAwardStatus() == 0) {
                                oldRecord.setAwardStatus(1);
                                oldRecord.setmTime(nowTime);
                            }
                            finishFlag = true;
                        }
                        dailyTaskManagerDao.update(oldRecord);
                    } else {
                        DailyTaskManager newRecord = new DailyTaskManager();
                        newRecord.setUid(dailyTaskMqData.getUid());
                        newRecord.setDateKey(dateKey);
                        newRecord.setTaskId(dailyTaskMqData.getTask_id());
                        newRecord.setTaskValue(num);
                        newRecord.setAwardStatus(0);
                        newRecord.setcTime(nowTime);
                        newRecord.setmTime(nowTime);
                        if (num >= dailyTaskData.getTask_value()) {
                            finishFlag = true;
                            newRecord.setAwardStatus(1);
                        }
                        dailyTaskManagerDao.insert(newRecord);
                    }
                }
                if (finishFlag) {
                    RoomNoviceFinishPushMsg msg = new RoomNoviceFinishPushMsg();
                    msg.setTaskId(dailyTaskMqData.getTask_id());
                    msg.setType(1);
                    msg.setTaskType(1);
                    roomWebSender.sendPlayerWebMsg("", dailyTaskMqData.getUid(), dailyTaskMqData.getUid(), msg, false);
                }
            }
        } catch (Exception e) {
            logger.error("handle message error. message body={}", new String(message.getBody()), e);
        } finally {
            try {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } catch (IOException e) {
                logger.error("message ack error message={}", new String(message.getBody()));
            }
        }
    }
}
