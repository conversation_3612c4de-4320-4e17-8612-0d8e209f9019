package com.quhong.consumer;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.NoviceTaskRecordEvent;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.TaskInfo;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.msg.room.UserTaskCompletionMsg;
import com.quhong.mysql.dao.NewcomerTaskDao;
import com.quhong.mysql.dao.UserTaskDao;
import com.quhong.mysql.data.NewcomerTaskData;
import com.quhong.mysql.data.UserTaskData;
import com.quhong.redis.RoomMicRedis;
import com.quhong.redis.UserTaskRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.service.DailyTaskCenterService;
import com.quhong.service.WebTaskCenterService;
import com.quhong.task.TaskFactory;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/9/21
 */
@Component
public class UserTaskMqConsumer {

    private static final Logger logger = LoggerFactory.getLogger(UserTaskMqConsumer.class);

    private static final Interner<String> stringPool = Interners.newWeakInterner();
    public static final String USER_TASK_LOCK_KEY = "user_task_handle_";
    public static final String NEWCOMER_TASK_LOCK_KEY = "newcomer_task_handle_";

    @Resource
    private UserTaskDao userTaskDao;
    @Resource
    private NewcomerTaskDao newcomerTaskDao;
    @Resource
    private DailyTaskCenterService dailyTaskCenterService;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private RoomMicRedis roomMicRedis;
    @Resource
    private EventReport eventReport;
    @Resource
    private UserTaskRedis userTaskRedis;
    @Resource
    private WebTaskCenterService webTaskCenterService;

    @RabbitListener(queues = CommonMqTaskConstant.USER_TASK_QUEUE)
    public void handleMessage(Message message) {
        try {
            TaskFactory.getFactory().addPush(new Task() {
                @Override
                protected void execute() {
                    String json = new String(message.getBody());
                    if (ServerConfig.isNotProduct()) {
                        logger.info("receive rabbit mq message, message body={}", json);
                    }
                    CommonMqTopicData mqData = JSON.parseObject(json, CommonMqTopicData.class);
                    handleTaskMqData(mqData);
                    webTaskCenterService.handleWebTaskMqData(mqData);
                }
            });
        } catch (Exception e) {
            logger.error("handle message error. message body={}", new String(message.getBody()), e);
        }
    }

    private void handleTaskMqData(CommonMqTopicData mqData) {
        if (mqData == null || StringUtils.isEmpty(mqData.getUid())) {
            return;
        }
        Map<String, TaskInfo> taskMap = dailyTaskCenterService.getTaskInfoMap();
        int num = mqData.getValue() != 0 ? mqData.getValue() : 1;
        int taskDate = Integer.parseInt(mqData.getDateStr().replace("-", ""));
        String handleId = mqData.getHandleId();
        switch (mqData.getItem()) {
            case CommonMqTaskConstant.UPDATE_PERSONAL_INFO:
                handleNewcomerTask(taskMap.get("update_personal_info"), mqData.getUid(), num, handleId, false);
                break;
            case CommonMqTaskConstant.SEND_ROOM_MSG:
            case CommonMqTaskConstant.SEND_HALL_MSG:
                if (RoomUtils.isGameRoom(mqData.getRoomId())) {
                    break;
                }
                handleNewcomerTask(taskMap.get("send_room_msg_1"), mqData.getUid(), num, handleId, false);
                handleUserTask(taskMap.get("send_room_msg_2"), mqData.getUid(), taskDate, num, handleId, false);
                break;
            case CommonMqTaskConstant.SEND_ROOM_GIFT:
                if (RoomUtils.isGameRoom(mqData.getRoomId())) {
                    break;
                }
                handleNewcomerTask(taskMap.get("send_room_gift_1"), mqData.getUid(), 1, handleId, false);
                handleUserTask(taskMap.get("send_room_gift_2"), mqData.getUid(), taskDate, 1, handleId, false);
                handleUserTask(taskMap.get("gift_cost_diamond_1"), mqData.getUid(), taskDate, num, handleId, false);
                handleUserTask(taskMap.get("gift_cost_diamond_2"), mqData.getUid(), taskDate, num, handleId, false);
                handleUserTask(taskMap.get("gift_cost_diamond_3"), mqData.getUid(), taskDate, num, handleId, false);
                handleUserTask(taskMap.get("gift_cost_diamond_4"), mqData.getUid(), taskDate, num, handleId, false);
                break;
            case CommonMqTaskConstant.ADD_FRIEND:
                handleNewcomerTask(taskMap.get("add_friend"), mqData.getUid(), num, handleId, true);
                if (isNewUser(mqData.getAid())) {
                    handleUserTask(taskMap.get("add_new_friend"), mqData.getUid(), taskDate, num, handleId, true);
                }
                break;
            case CommonMqTaskConstant.FOLLOW_ROOM:
                handleNewcomerTask(taskMap.get("follow_room"), mqData.getUid(), num, handleId, true);
                break;
            case CommonMqTaskConstant.JOIN_ROOM_MEMBER:
                handleNewcomerTask(taskMap.get("join_room_member"), mqData.getUid(), num, handleId, true);
                if (isNewUser(mqData.getUid())) {
                    handleUserTask(taskMap.get("get_new_room_member"), RoomUtils.getRoomHostId(mqData.getRoomId()), taskDate, num, mqData.getUid(), true);
                }
                break;
            case CommonMqTaskConstant.LIKE_MOMENT:
                handleNewcomerTask(taskMap.get("like_or_comment_moment_1"), mqData.getUid(), num, handleId, true);
                handleUserTask(taskMap.get("like_or_comment_moment_2"), mqData.getUid(), taskDate, num, handleId, true);
                break;
            case CommonMqTaskConstant.COMMENT_MOMENT:
                handleNewcomerTask(taskMap.get("like_or_comment_moment_1"), mqData.getUid(), num, handleId, true);
                handleUserTask(taskMap.get("like_or_comment_moment_2"), mqData.getUid(), taskDate, num, handleId, true);
                break;
            case CommonMqTaskConstant.ON_MIC_TIME:
                if (RoomUtils.isGameRoom(mqData.getRoomId())) {
                    break;
                }
                handleUserTask(taskMap.get("on_mic_time_1"), mqData.getUid(), taskDate, num, handleId, false);
                handleUserTask(taskMap.get("on_mic_time_2"), mqData.getUid(), taskDate, num, handleId, false);
                handleUserTask(taskMap.get("on_mic_time_3"), mqData.getUid(), taskDate, num, handleId, false);
                handleUserTask(taskMap.get("on_mic_time_4"), mqData.getUid(), taskDate, num, handleId, false);
                break;
            // case CommonMqTaskConstant.DAILY_LOGIN:
            //     handleUserTask(taskMap.get("daily_login"), mqData.getUid(), taskDate, num, handleId, false);
            //     break;
            case CommonMqTaskConstant.INVITE_USER_ON_MIC:
                int upMicType = roomMicRedis.getUpMicType(mqData.getAid());
                if (upMicType == 2) {
                    handleUserTask(taskMap.get("invite_new_users_on_mic"), mqData.getUid(), taskDate, num, handleId, true);
                }
                break;
            case CommonMqTaskConstant.POST_MOMENT:
                handleUserTask(taskMap.get("post_moment"), mqData.getUid(), taskDate, num, handleId, true);
                break;
            case CommonMqTaskConstant.SEND_MOMENT_GIFT:
                handleUserTask(taskMap.get("send_moment_gift"), mqData.getUid(), taskDate, 1, handleId, true);
                break;
            case CommonMqTaskConstant.WATCH_VIDEO_TIME:
                if (num >= 5) {
                    handleUserTask(taskMap.get("watch_video_time"), mqData.getUid(), taskDate, 1, handleId, false);
                }
                break;
            case CommonMqTaskConstant.PLAY_WHEEL:
                handleUserTask(taskMap.get("play_wheel"), mqData.getUid(), taskDate, num, handleId, false);
                break;
            case CommonMqTaskConstant.PLAY_FRUIT_MACHINE:
                handleUserTask(taskMap.get("play_fruit_machine"), mqData.getUid(), taskDate, 1, handleId, true);
                break;
            case CommonMqTaskConstant.PLAY_LUDO:
                handleUserTask(taskMap.get("play_ludo"), mqData.getUid(), taskDate, num, handleId, false);
                break;
            case CommonMqTaskConstant.PLAY_UMO:
                handleUserTask(taskMap.get("play_umo"), mqData.getUid(), taskDate, num, handleId, false);
                break;
            case CommonMqTaskConstant.PLAY_MONSTER_CRUSH:
                handleUserTask(taskMap.get("play_monster_crush"), mqData.getUid(), taskDate, num, handleId, false);
                break;
            case CommonMqTaskConstant.WIN_FINGER_GUESS:
                handleUserTask(taskMap.get("win_finger_guess"), mqData.getUid(), taskDate, num, handleId, false);
                break;
            case CommonMqTaskConstant.WIN_LUDO:
                handleUserTask(taskMap.get("win_ludo"), mqData.getUid(), taskDate, num, handleId, false);
                break;
            case CommonMqTaskConstant.WIN_UMO:
                handleUserTask(taskMap.get("win_umo"), mqData.getUid(), taskDate, num, handleId, false);
                break;
            case CommonMqTaskConstant.WIN_MONSTER_CRUSH:
                handleUserTask(taskMap.get("win_monster_crush"), mqData.getUid(), taskDate, num, handleId, false);
                break;
            case CommonMqTaskConstant.RECHARGE_DIAMONDS:
                handleUserTask(taskMap.get("daily_recharge"), mqData.getUid(), taskDate, 1, handleId, true);
                break;
            default:
                break;
        }
    }

    /**
     * 日常任务和进阶任务
     */
    private void handleUserTask(TaskInfo taskInfo, String uid, int taskDate, int num, String handleId, boolean checkRepeat) {
        if (taskInfo == null){
            return;
        }

        int nowTime = DateHelper.getNowSeconds();
        boolean finishedTask = false;
        synchronized (stringPool.intern(USER_TASK_LOCK_KEY + uid)) {
            UserTaskData oldRecord = userTaskDao.getTaskByTaskKey(uid, taskDate, taskInfo.getKey());
            if (oldRecord != null) {
                if (checkRepeat && oldRecord.getTaskValue().contains(handleId)) {
                    return;
                }
                if (oldRecord.getTaskNum() >= taskInfo.getLimit()) {
                    return;
                }
                oldRecord.setTaskNum(oldRecord.getTaskNum() + num);
                oldRecord.setMtime(nowTime);
                if (checkRepeat) {
                    oldRecord.setTaskValue(oldRecord.getTaskValue() + "," + handleId);
                }
                if (oldRecord.getTaskNum() >= taskInfo.getLimit() && oldRecord.getStatus() == 0) {
                    finishedTask = true;
                    oldRecord.setStatus(1);
                }
                userTaskDao.update(oldRecord);
            } else {
                UserTaskData newRecord = new UserTaskData();
                newRecord.setUid(uid);
                newRecord.setTaskDate(taskDate);
                newRecord.setTaskKey(taskInfo.getKey());
                newRecord.setTaskNum(num);
                newRecord.setTaskValue(checkRepeat ? handleId : "");
                if (newRecord.getTaskNum() >= taskInfo.getLimit()) {
                    finishedTask = true;
                    newRecord.setStatus(1);
                } else {
                    newRecord.setStatus(0);
                }
                newRecord.setMtime(nowTime);
                newRecord.setCtime(nowTime);
                userTaskDao.insert(newRecord);
            }
        }
        if (finishedTask) {
            sendUserTaskCompletionMsg(uid, taskInfo.getKey());
            userTaskRedis.incUserHasRewardCount(uid, 1);
        }
    }

    /**
     * 新人任务
     */
    public void handleNewcomerTask(TaskInfo taskInfo, String uid, int num, String handleId, boolean checkRepeat) {
        if (taskInfo == null){
            return;
        }

        int nowTime = DateHelper.getNowSeconds();
        if (!isNewUser(uid)) {
            return;
        }
        boolean finishedTask = false;
        synchronized (stringPool.intern(NEWCOMER_TASK_LOCK_KEY + uid)) {
            NewcomerTaskData oldRecord = newcomerTaskDao.getByUidAndTaskKey(uid, taskInfo.getKey());
            if (oldRecord != null) {
                if (checkRepeat && oldRecord.getTaskValue().contains(handleId)) {
                    return;
                }
                if (oldRecord.getTaskNum() >= taskInfo.getLimit()) {
                    return;
                }
                oldRecord.setTaskNum(oldRecord.getTaskNum() + num);
                oldRecord.setMtime(nowTime);
                if (checkRepeat) {
                    oldRecord.setTaskValue(oldRecord.getTaskValue() + "," + handleId);
                }
                if (oldRecord.getTaskNum() >= taskInfo.getLimit() && oldRecord.getStatus() == 0) {
                    finishedTask = true;
                    oldRecord.setStatus(1);
                }
                newcomerTaskDao.update(oldRecord);
            } else {
                NewcomerTaskData newRecord = new NewcomerTaskData();
                newRecord.setUid(uid);
                newRecord.setTaskKey(taskInfo.getKey());
                newRecord.setTaskNum(num);
                newRecord.setTaskValue(checkRepeat ? handleId : "");
                if (newRecord.getTaskNum() >= taskInfo.getLimit()) {
                    finishedTask = true;
                    newRecord.setStatus(1);
                } else {
                    newRecord.setStatus(0);
                }
                newRecord.setMtime(nowTime);
                newRecord.setCtime(nowTime);
                newcomerTaskDao.insert(newRecord);
            }
        }
        if (finishedTask) {
            sendUserTaskCompletionMsg(uid, taskInfo.getKey());
            // 新手任务埋点
            NoviceTaskRecordEvent event = new NoviceTaskRecordEvent();
            event.setUid(uid);
            event.setTask_key(taskInfo.getKey());
            event.setCtime(DateHelper.getNowSeconds());
            eventReport.track(new EventDTO(event));
        }
    }

    private void sendUserTaskCompletionMsg(String uid, String taskKey) {
        UserTaskCompletionMsg msg = new UserTaskCompletionMsg();
        msg.setTaskKey(taskKey);
        // msg.setShowGuide(ActorUtils.isNewRegisterActor(uid, 7) ? 1 : 0);  // 7天用户展示引导
        msg.setShowGuide(1);
        logger.info("sendUserTaskCompletionMsg uid: {}, msg:{}", uid, JSON.toJSONString(msg));
        roomWebSender.sendPlayerWebMsg("", uid, uid, msg, false);
    }

    private boolean isNewUser(String uid) {
        return ActorUtils.isNewRegisterActor(uid, 7);
    }
}
