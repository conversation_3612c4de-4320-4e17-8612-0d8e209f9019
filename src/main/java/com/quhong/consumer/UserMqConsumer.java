package com.quhong.consumer;

import com.alibaba.fastjson.JSON;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.SpringUtils;
import com.quhong.data.CommonMqTopicData;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.handler.MqMessageHandler;
import com.quhong.task.TaskFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 用户进房推荐
 */
@Component
public class UserMqConsumer {

    private static final Logger logger = LoggerFactory.getLogger(UserMqConsumer.class);
    private static final List<MqMessageHandler> HANDLERS = new ArrayList<>();

    @PostConstruct
    public void postInit() {
        // 初始化监听器
        Map<String, MqMessageHandler> beans = SpringUtils.getApplicationContext().getBeansOfType(MqMessageHandler.class);
        for (String bean : beans.keySet()) {
            HANDLERS.add(beans.get(bean));
            logger.info("add send gift handler name={}", bean);
        }
    }

    @RabbitListener(queues = CommonMqTaskConstant.USER_ROOM_RECOMMEND_QUEUE)
    public void handleMessage(Message message) {
        try {
            TaskFactory.getFactory().addPush(new Task() {
                @Override
                protected void execute() {
                    String json = new String(message.getBody());
//                    logger.info("UserRoomRecommend mq message, message body={}", json);
                    for (MqMessageHandler handler : HANDLERS) {
                        try {
                            handler.process(JSON.parseObject(json, CommonMqTopicData.class));
                        } catch (Exception e) {
                            logger.error("process handler error handler={}", handler.getClass().getSimpleName(), e);
                        }
                    }
                }
            });
        } catch (Exception e) {
            logger.error("handle message error. message body={}", new String(message.getBody()), e);
        }
    }
}
