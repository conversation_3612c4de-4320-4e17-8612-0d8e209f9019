package com.quhong.consumer;

import com.alibaba.fastjson.JSON;
import com.quhong.constant.MqTopicItemConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.EnterRoomMqData;
import com.quhong.data.UserLevelTaskData;
import com.quhong.enums.UserLevelConstant;
import com.quhong.mongo.dao.NoticeNewDao;
import com.quhong.mongo.dao.OfficialDao;
import com.quhong.mongo.dao.UserLevelDao;
import com.quhong.mongo.data.NoticeNewData;
import com.quhong.mongo.data.OfficialData;
import com.quhong.mongo.data.UserLevelData;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.room.NewUserExpToastMsg;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.msg.room.UserLevelUpMsg;
import com.quhong.mysql.data.UserExpDetailData;
import com.quhong.room.RoomWebSender;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.service.UserExpDetailDao;
import com.quhong.service.UserLevelService;
import com.quhong.task.TaskFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Component
public class UserLevelMqConsumer {
    private static final Logger logger = LoggerFactory.getLogger(UserLevelMqConsumer.class);

    private static final String SPLIT = "|";

    private static final String SEND_HEART_GIFT = "send_heart_gift";
    private static final String UPLOAD_PICTURE_ROOKIE = "upload_picture_rookie";
    private static final String FILL_HOBBIES_ROOKIE = "fill_hobbies_rookie";
    private static final String USER_REGISTER = "user_register";

    private static final int ACTIVE = 1;//活跃值
    private static final int CHARISMA = 2;//魅力值
    private static final int WEALTH = 3;//财富值

    private static final int NONE = -2;
    private static final int ONE_TIME = -1;//一次性加分
    private static final int UNLIMITED = 0;//无限

    private static final int UNREAD = 1;

    private static final Object LOCK = new Object();

    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private UserExpDetailDao userExpDetailDao;
    @Resource
    private UserLevelService userLevelService;
    @Resource
    protected RoomWebSender roomSender;
    @Resource
    private OfficialDao officialDao;
    @Resource
    private NoticeNewDao noticeNewDao;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private MqSenderService mqSenderService;

    @RabbitListener(queues = UserLevelConstant.QUEUE)
    public void handleMessage(Message message) {
        try {
            TaskFactory.getFactory().addPush(new Task() {
                @Override
                protected void execute() {
                    String json = new String(message.getBody());
                    // logger.info("receive rabbit mq message, message body={}", json);
                    parseUserExpData(JSON.parseObject(json, UserLevelTaskData.class));
                }
            });
        } catch (Exception e) {
            logger.error("handle message error. message body={}", new String(message.getBody()), e);
        }
    }

    @SuppressWarnings("SynchronizationOnLocalVariableOrMethodParameter")
    private void parseUserExpData(UserLevelTaskData data) {
        if (StringUtils.isEmpty(data.getUid()) || StringUtils.isEmpty(data.getDate_str())) {
            logger.error("parse user exp data error uid or date str is empty item={}", data.getItem());
            return;
        }
        UserExpDetailData detail = userExpDetailDao.getUserExpDetail(data.getUid(), data.getDate_str());
        int nowSeconds = DateHelper.getNowSeconds();
        if (null == detail) {
            synchronized (LOCK) {
                detail = userExpDetailDao.getUserExpDetail(data.getUid(), data.getDate_str());
                if (null == detail) {
                    detail = new UserExpDetailData();
                    detail.setUid(data.getUid());
                    detail.setDate_str(data.getDate_str());
                    detail.setCtime(nowSeconds);
                }
                detail.setMtime(nowSeconds);
                handleUserExpData(data, detail);
            }
        } else {
            synchronized (detail) {
                detail.setMtime(nowSeconds);
                handleUserExpData(data, detail);
            }
        }
    }

    private void handleUserExpData(UserLevelTaskData data, UserExpDetailData detail) {
        int score;
        switch (data.getItem()) {
            case SEND_HEART_GIFT:
//                noviceTaskService.sendGift(data.getUid());
                break;
            case UPLOAD_PICTURE_ROOKIE:
//                noviceTaskService.compactPersonPage(data.getUid());
                break;
            case FILL_HOBBIES_ROOKIE:
//                noviceTaskService.compactPersonPage(data.getUid());
                break;
            case USER_REGISTER:
//                noviceService.initNoviceTaskStatus(data.getUid());
                break;
            case UserLevelConstant.LOGIN:
                if (detail.getLogin() == 0) {
                    detail.setLogin(UserLevelConstant.LOGIN_POINT);
                    logUserExp(data, detail, ACTIVE, UserLevelConstant.LOGIN_POINT, NONE);
                }
//                noviceService.initNoviceTaskStatus(data.getUid());
                break;
            case UserLevelConstant.STAY_ROOM:
                if (detail.getStay_room() < UserLevelConstant.STAY_ROOM_LIMIT) {
                    score = calLimitScore(data.getValue(), UserLevelConstant.STAY_ROOM_PER,
                            UserLevelConstant.STAY_ROOM_POINT, detail.getStay_room(), UserLevelConstant.STAY_ROOM_LIMIT);
                    if (0 != score) {
                        detail.setStay_room(detail.getStay_room() + score);
                        logUserExp(data, detail, ACTIVE, score, NONE);
                    }
                }
                break;
            case UserLevelConstant.UP_MIC:
                if (detail.getUp_mic() < UserLevelConstant.UP_MIC_LIMIT) {
                    score = calLimitScore(data.getValue(), UserLevelConstant.UP_MIC_PER,
                            UserLevelConstant.UP_MIC_POINT, detail.getUp_mic(), UserLevelConstant.UP_MIC_LIMIT);
                    if (0 != score) {
                        detail.setUp_mic(detail.getUp_mic() + score);
                        logUserExp(data, detail, ACTIVE, score, NONE);
                    }
                }
                EnterRoomMqData micUpMqData = new EnterRoomMqData();
                micUpMqData.setUid(data.getUid());
                micUpMqData.setRoomId("");
                micUpMqData.setNum(data.getValue());
                micUpMqData.setItem(MqTopicItemConstant.UP_MIC);
                logger.info("send up mic to mq. uid={}", micUpMqData.getUid());
                mqSenderService.sendTopicMsgToMq(MqSenderService.ROUTE_KEY_USER_MIC_UP, micUpMqData);
                break;
            case UserLevelConstant.POST_MOMENT:
                if (detail.getPost_moment() < UserLevelConstant.POST_MOMENT_LIMIT) {
                    score = checkLimit(UserLevelConstant.POST_MOMENT_POINT, detail.getPost_moment(),
                            UserLevelConstant.POST_MOMENT_LIMIT);
                    if (0 != score) {
                        detail.setPost_moment(detail.getPost_moment() + score);
                        logUserExp(data, detail, ACTIVE, score, NONE);
                    }
                }
                break;
            case UserLevelConstant.SEND_MSG:
                if (detail.getSend_msg() < UserLevelConstant.SEND_MSG_LIMIT) {
                    if (!detail.getSend_msg_ids().contains(data.getHandle_id())) {
                        score = checkLimit(UserLevelConstant.SEND_MSG_POINT, detail.getSend_msg(),
                                UserLevelConstant.SEND_MSG_LIMIT);
                        if (0 != score) {
                            detail.setSend_msg(detail.getSend_msg() + score);
                            detail.setSend_msg_ids(detail.getSend_msg_ids() + SPLIT + data.getHandle_id());
                            logUserExp(data, detail, ACTIVE, score, NONE);
                            sendNewUserExpToastMsg(data.getUid(), score);
                        }
                    }
                }
                break;
            case UserLevelConstant.SEND_GIFT:
                if (detail.getSend_gift() < UserLevelConstant.SEND_GIFT_LIMIT) {
                    score = checkLimit(UserLevelConstant.SEND_GIFT_POINT, detail.getSend_gift(),
                            UserLevelConstant.SEND_GIFT_LIMIT);
                    if (0 != score) {
                        detail.setSend_gift(detail.getSend_gift() + score);
                        logUserExp(data, detail, ACTIVE, score, NONE);
                        sendNewUserExpToastMsg(data.getUid(), score);
                    }
                }
                break;
            case UserLevelConstant.LIKE_MOMENT:
                if (detail.getLike_moment() < UserLevelConstant.LIKE_MOMENT_LIMIT) {
                    if (!detail.getLike_moment_ids().contains(data.getHandle_id())) {
                        score = checkLimit(UserLevelConstant.LIKE_MOMENT_POINT, detail.getLike_moment(),
                                UserLevelConstant.LIKE_MOMENT_LIMIT);
                        if (0 != score) {
                            detail.setLike_moment(detail.getLike_moment() + score);
                            detail.setLike_moment_ids(detail.getLike_moment_ids() + SPLIT + data.getHandle_id());
                            logUserExp(data, detail, ACTIVE, score, NONE);
                            sendNewUserExpToastMsg(data.getUid(), score);
                        }
                    }
                }
                break;
            case UserLevelConstant.SEND_ROOM_MSG:
                if (detail.getSend_room_msg() < UserLevelConstant.SEND_ROOM_MSG_LIMIT) {
                    int count = detail.getSend_room_msg_count() + 1;
                    // 每发送5条消息增加5经验值
                    if (count == 5) {
                        detail.setSend_room_msg(detail.getSend_room_msg() + UserLevelConstant.SEND_ROOM_MSG_POINT);
                        detail.setSend_room_msg_count(0);
                        logUserExp(data, detail, ACTIVE, UserLevelConstant.SEND_ROOM_MSG_POINT, NONE);
                        sendNewUserExpToastMsg(data.getUid(), UserLevelConstant.SEND_ROOM_MSG_POINT);
                    } else {
                        // 记录发送房间消息次数（仅使用cacheMap记录）
                        detail.setSend_room_msg_count(count);
                    }
                }
                break;
            case UserLevelConstant.UPLOAD_PICTURE:
                logUserExp(data, detail, ACTIVE, UserLevelConstant.UPLOAD_PICTURE_POINT, ONE_TIME);
                break;
            case UserLevelConstant.FILL_HOBBIES:
                logUserExp(data, detail, ACTIVE, UserLevelConstant.FILL_HOBBIES_POINT, ONE_TIME);
                break;
            case UserLevelConstant.FOLLOWED:
                if (detail.getFollowed() < UserLevelConstant.FOLLOWED_LIMIT) {
                    if (!detail.getFollowed_ids().contains(data.getHandle_id())) {
                        score = checkLimit(UserLevelConstant.FOLLOWED_POINT, detail.getFollowed(),
                                UserLevelConstant.FOLLOWED_LIMIT);
                        if (0 != score) {
                            detail.setFollowed(detail.getFollowed() + score);
                            detail.setFollowed_ids(detail.getFollowed_ids() + SPLIT + data.getHandle_id());
                            logUserExp(data, detail, CHARISMA, score, NONE);
                        }
                    }
                }
                break;
            case UserLevelConstant.BECOME_FRIENDS:
                if (detail.getBecome_friends() < UserLevelConstant.BECOME_FRIENDS_LIMIT) {
                    if (!detail.getBecome_friends_ids().contains(data.getHandle_id())) {
                        score = checkLimit(UserLevelConstant.BECOME_FRIENDS_POINT, detail.getBecome_friends(),
                                UserLevelConstant.BECOME_FRIENDS_LIMIT);
                        if (0 != score) {
                            detail.setBecome_friends(detail.getBecome_friends() + score);
                            detail.setBecome_friends_ids(detail.getBecome_friends_ids() + SPLIT + data.getHandle_id());
                            logUserExp(data, detail, CHARISMA, score, NONE);
                            sendNewUserExpToastMsg(data.getUid(), score);
                        }
                    }
                }
                break;
            case UserLevelConstant.HOMEPAGE_VIEWED:
                if (detail.getHomepage_viewed() < UserLevelConstant.HOMEPAGE_VIEWED_LIMIT) {
                    if (!detail.getHomepage_viewed_ids().contains(data.getHandle_id())) {
                        score = checkLimit(UserLevelConstant.HOMEPAGE_VIEWED_POINT, detail.getHomepage_viewed(),
                                UserLevelConstant.HOMEPAGE_VIEWED_LIMIT);
                        if (0 != score) {
                            detail.setHomepage_viewed(detail.getHomepage_viewed() + score);
                            detail.setHomepage_viewed_ids(detail.getHomepage_viewed_ids() + SPLIT + data.getHandle_id());
                            logUserExp(data, detail, CHARISMA, score, NONE);
                        }
                    }
                }
                break;
            case UserLevelConstant.MOMENT_LIKED:
                if (detail.getMoment_liked() < UserLevelConstant.MOMENT_LIKED_LIMIT) {
                    if (!detail.getMoment_liked_ids().contains(data.getHandle_id())) {
                        score = checkLimit(UserLevelConstant.MOMENT_LIKED_POINT, detail.getMoment_liked(),
                                UserLevelConstant.MOMENT_LIKED_LIMIT);
                        if (0 != score) {
                            detail.setMoment_liked(detail.getMoment_liked() + score);
                            detail.setMoment_liked_ids(detail.getMoment_liked_ids() + SPLIT + data.getHandle_id());
                            logUserExp(data, detail, CHARISMA, score, NONE);
                        }
                    }
                }
                break;
            case UserLevelConstant.MOMENT_COMMENT:
                if (detail.getMoment_comment() < UserLevelConstant.MOMENT_COMMENT_LIMIT) {
                    if (!detail.getMoment_comment_ids().contains(data.getHandle_id())) {
                        score = checkLimit(UserLevelConstant.MOMENT_COMMENT_POINT, detail.getMoment_comment(),
                                UserLevelConstant.MOMENT_COMMENT_LIMIT);
                        if (0 != score) {
                            detail.setMoment_comment(detail.getMoment_comment() + score);
                            detail.setMoment_comment_ids(detail.getMoment_comment_ids() + SPLIT + data.getHandle_id());
                            logUserExp(data, detail, CHARISMA, score, NONE);
                        }
                    }
                }
                break;
            case UserLevelConstant.RECEIVE_GIFT:
                if (data.getValue() >= UserLevelConstant.RECEIVE_GIFT_PER) {
                    score = calUnLimitScore(data.getValue(), UserLevelConstant.RECEIVE_GIFT_PER,
                            UserLevelConstant.RECEIVE_GIFT_POINT);
                    if (0 != score) {
                        logUserExp(data, detail, CHARISMA, score, UNLIMITED);
                    }
                }
                break;
            case UserLevelConstant.FIRST_FRIEND:
                logUserExp(data, detail, CHARISMA, UserLevelConstant.FIRST_FRIEND_POINT, ONE_TIME);
                break;
            case UserLevelConstant.MORE_FRIENDS:
                logUserExp(data, detail, CHARISMA, UserLevelConstant.MORE_FRIENDS_POINT, ONE_TIME);
                break;
            case UserLevelConstant.CHECK_IN:
                if (detail.getCheck_in() == 0) {
                    detail.setCheck_in(UserLevelConstant.CHECK_IN_POINT);
                    logUserExp(data, detail, WEALTH, UserLevelConstant.CHECK_IN_POINT, NONE);
                }
                break;
            case UserLevelConstant.RECHARGE:
                if (data.getValue() >= UserLevelConstant.RECHARGE_PER) {
                    score = calUnLimitScore(data.getValue(), UserLevelConstant.RECHARGE_PER,
                            UserLevelConstant.RECHARGE_POINT);
                    if (0 != score) {
                        logUserExp(data, detail, WEALTH, score, UNLIMITED);
                    }
                }
                break;
            case UserLevelConstant.RECHARGE_VIP:
                int beans = data.getValue();
                score = (int) ((beans / 20) * 1.2);
                logUserExp(data, detail, WEALTH, score, UNLIMITED);
                break;
            case UserLevelConstant.CONSUME:
                if (data.getValue() >= UserLevelConstant.CONSUME_PER) {
                    score = calUnLimitScore(data.getValue(), UserLevelConstant.CONSUME_PER,
                            UserLevelConstant.CONSUME_POINT);
                    if (0 != score) {
                        logUserExp(data, detail, WEALTH, score, UNLIMITED);
                    }
                }
                break;
            case UserLevelConstant.FIRST_RECHARGE:
                logUserExp(data, detail, WEALTH, UserLevelConstant.FIRST_RECHARGE_POINT, ONE_TIME);
                break;
            default:
                break;
        }
    }

    /**
     * 记录用户经验值
     *
     * @param data    mq消息
     * @param expType 等级积分构成
     * @param score   分值
     * @param limit   分数每日限额,-1表示一次性任务加分,0表示无上限
     */
    private void logUserExp(UserLevelTaskData data, UserExpDetailData detail, int expType, int score, int limit) {
        UserLevelData userLevelData = userLevelDao.getUserLevelData(data.getUid());
        if (null == userLevelData) {
            return;
        }
        if (UNLIMITED == limit) {
            updateExp(userLevelData, detail, expType, score);
        } else if (ONE_TIME == limit) {
            if (!userLevelData.getReached().contains(data.getItem())) {
                userLevelData.getReached().add(data.getItem());
                updateExp(userLevelData, detail, expType, score);
            }
        } else {
            // 首次发送礼物
            if (data.getItem().equals(UserLevelConstant.SEND_GIFT)
                    && !userLevelData.getReached().contains(UserLevelConstant.SEND_FIRST_GIFT)) {
                userLevelData.getReached().add(UserLevelConstant.SEND_FIRST_GIFT);
                int sendFirstGiftScore = UserLevelConstant.SEND_FIRST_GIFT_POINT + UserLevelConstant.SEND_GIFT_POINT;
                updateExp(userLevelData, detail, expType, sendFirstGiftScore);
            } else {
                updateExp(userLevelData, detail, expType, score);
            }
        }
        logger.info("log user exp uid={} item={} expType={} score={}", data.getUid(), data.getItem(), expType, score);
        // 保存用户等级和经验信息
        userLevelDao.updateUserLevel(userLevelData);
    }

    private void updateExp(UserLevelData userLevelData, UserExpDetailData detail, int expType, int score) {
        upgradeExp(userLevelData);
        if (expType == ACTIVE) {
            userLevelData.setActive_exp(userLevelData.getActive_exp() + score);
        } else if (expType == CHARISMA) {
            userLevelData.setCharm_exp(userLevelData.getCharm_exp() + score);
        } else {
            userLevelData.setWealth_exp(userLevelData.getWealth_exp() + score);
        }
        userLevelData.setNew_exp(userLevelData.getNew_exp() + score);
        upgradeLevel(userLevelData);
        detail.setToday_exp(detail.getToday_exp() + score);
        userExpDetailDao.saveOrUpdate(detail);
    }

    private void upgradeLevel(UserLevelData data) {
        int level = userLevelService.getLevelByExp(data.getNew_exp());
        int oldLevel = data.getLevel();
        if (level != oldLevel && 0 != level) {
            if (data.getLevel() > level) {
                logger.error("fatal error: user level data uid={} newLevel={} oldLevel={} exp={}  newExp={}",
                        data.getUid(), level, oldLevel, data.getExp(), data.getNew_exp());
                return;
            }
            logger.info("user level up uid={} level={} exp={}", data.getUid(), level, data.getNew_exp());
            data.setLevel(level);
            handleUserLevelUp(data, oldLevel);
            userLevelDao.setUserLevelToRedis(data.getUid(), level);
        }
    }

    private void handleUserLevelUp(UserLevelData userLevelData, int oldLevel) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                String uid = userLevelData.getUid();
                int level = userLevelData.getLevel();
                // 官方消息
                OfficialData officialMessage = userLevelService.getOfficialMessage(uid, level);
                officialDao.save(officialMessage);
                // 通知消息
                NoticeNewData noticeNewData = new NoticeNewData();
                noticeNewData.setAid(uid);
                noticeNewData.setOfficial_id(officialMessage.get_id().toString());
                noticeNewData.setCtime(DateHelper.getNowSeconds());
                noticeNewData.setStatus(UNREAD);
                noticeNewDao.save(noticeNewData);
                // 下发用户所在房间的升级消息
                String actorRoomId = roomPlayerRedis.getActorRoomStatus(uid);
                if (!StringUtils.isEmpty(actorRoomId)) {
                    RoomNotificationMsg msg = userLevelService.getRoomMessage(uid, level);
                    if (null != msg) {
                        roomSender.sendRoomWebMsg(actorRoomId, null, msg, false);
                    }
                }
                // 用户升级消息
                UserLevelUpMsg userLevelUpMsg = userLevelService.getUserLevelUpMsg(uid, level, oldLevel);
                roomSender.sendPlayerWebMsg("", uid, uid, userLevelUpMsg, true);
            }
        });
    }

    /**
     * 升级旧数据
     */
    private void upgradeExp(UserLevelData data) {
        if (data.getNew_exp() == 0 && data.getExp() != 0) {
            long newExp = data.getExp() * 100L;
            long wealthExp = (long) (newExp * 0.8);
            long remainExp = newExp - wealthExp;
            long activeExp = (long) (remainExp * 0.6);
            long charmExp = remainExp - activeExp;

            data.setNew_exp(newExp);
            data.setActive_exp(activeExp);
            data.setCharm_exp(charmExp);
            data.setWealth_exp(wealthExp);
            logger.info("upgrade user exp uid={} exp={} newExp={} activeExp={} charmExp={} wealthExp={}",
                    data.getUid(), data.getExp(), newExp, activeExp, charmExp, wealthExp);
        } else {
            int level = userLevelService.getLevelByExp(data.getNew_exp());
            if (data.getLevel() > level) {
                // 数据有问题需要修复
                logger.error("user level data error uid={} newLevel={} oldLevel={} exp={}  newExp={}",
                        data.getUid(), level, data.getLevel(), data.getExp(), data.getNew_exp());
                fixUserExp(data);
            }
        }
    }

    /**
     * 修复有问题的用户等级数据
     */
    private void fixUserExp(UserLevelData data) {
        long newExp = userLevelService.getExpByLevel(data.getLevel());

        long wealthExp = (long) (newExp * 0.8);
        long remainExp = newExp - wealthExp;
        long activeExp = (long) (remainExp * 0.6);
        long charmExp = remainExp - activeExp;

        data.setActive_exp(activeExp);
        data.setCharm_exp(charmExp);
        data.setWealth_exp(wealthExp);
        data.setNew_exp(newExp);
        logger.error("fix user exp uid={} exp={} newExp={} activeExp={} charmExp={} wealthExp={}",
                data.getUid(), data.getExp(), newExp, activeExp, charmExp, wealthExp);
    }

    /**
     * 针对7天内注册的新用户发送toast消息提示
     */
    private void sendNewUserExpToastMsg(String uid, int score) {
        if (userLevelService.getMeetDay(uid) < 7) {
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    NewUserExpToastMsg msg = userLevelService.getNewUserExpToastMsg(uid, score);
                    roomSender.sendPlayerWebMsg("", uid, uid, msg, true);
                }
            });
        }
    }

    /**
     * 计算有限额的分数
     *
     * @param value   计算项目值
     * @param per     每份值
     * @param points  分值
     * @param current 当日分值总额
     * @param limit   限额
     * @return 分数值
     */
    private int calLimitScore(int value, int per, int points, int current, int limit) {
        int score = 0;
        if (0 == value) {
            return score;
        } else {
            int i = value / per;
            score = i * points;
        }
        if (score < points) {
            return 0;
        }
        if (score + current > limit) {
            score = limit - current;
        }
        return score;
    }

    /**
     * 计算无限额的分数
     * 例如：发送礼物消耗了value=128钻石，每消耗per=20钻算points=100经验值，则总共可以获得6*100=100经验值
     *
     * @param value  计算项目值（钻石）
     * @param per    每份值（最低限额）
     * @param points 分数（每份值的经验值）
     * @return 经验值
     */
    private int calUnLimitScore(int value, int per, int points) {
        int score;
        if (0 == value) {
            return 0;
        } else {
            int i = value / per;
            score = i * points;
        }
        if (score < points) {
            return 0;
        }
        return score;
    }

    /**
     * 检查每日限额
     *
     * @param points  分值
     * @param current 当日总分
     * @param limit   每日限额
     * @return 分数值
     */
    private int checkLimit(int points, int current, int limit) {
        if (points + current > limit) {
            points = limit - current;
        }
        return points;
    }
}
