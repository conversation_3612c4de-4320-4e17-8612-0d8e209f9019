package com.quhong.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Base64;

public class JwsUtil {
    private final static Logger logger = LoggerFactory.getLogger(JwsUtil.class);

    public static Jws<Claims> verifyJWT(String x5c, String jws) {
        try {
            X509Certificate cert = getCert(x5c);
            if (!cert.getSubjectX500Principal().getName().contains("Apple Inc")) {
                logger.info("not apple cert . name = {}", cert.getIssuerX500Principal().getName());
                return null;
            }
            return Jwts.parser().verifyWith(cert.getPublicKey()).build().parseSignedClaims(jws);
        } catch (JwtException exc) {
            logger.info("jws verify failure.", exc);
            return null;
        } catch (Exception exc) {
            logger.info("jws verify error.", exc);
            return null;
        }
    }

    public static X509Certificate getCert(String x5c) throws CertificateException {
        String stripped = x5c.replaceAll("-----BEGIN (.*)-----", "");
        stripped = stripped.replaceAll("-----END (.*)----", "");
        stripped = stripped.replaceAll("\r\n", "");
        stripped = stripped.replaceAll("\n", "");
        stripped.trim();
        byte[] keyBytes = Base64.getDecoder().decode(stripped);
        CertificateFactory fact = CertificateFactory.getInstance("X.509");
        return (X509Certificate) fact.generateCertificate(new ByteArrayInputStream(keyBytes));
    }
}