package com.quhong.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.quhong.core.utils.DateHelper;
import com.quhong.exception.CommonH5Exception;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.UUID;

@Component
public class OSSUploadService {

    private static final Logger logger = LoggerFactory.getLogger(OSSUploadService.class);

    private static final String OSS_END_POINT = "https://oss-me-east-1.aliyuncs.com";
    private static final String CLOUDCDN_DOMAIN = "https://cloudcdn.qmovies.tv/";

    private static final String accessKeyId = "LTAI5tPAmfMkNHRaC4XMgHf1";
    private static final String accessKeySecret = "******************************";
    private static final String bucketName = "qhclient";
    private static final String WEB_PATH = "user/fast_video/";
    private static final String pngReplace = "data:image/png;base64,";
    private static OSS ossClient = null;

    public OSSUploadService() {
        ossClient = new OSSClientBuilder().build(OSS_END_POINT, accessKeyId, accessKeySecret);
    }


    public String upload(String fileName, String path) {
        String fileUrl = null;
        try {
            // 设置存储路径
            String ossFilePath = WEB_PATH + fileName;
            File file = new File(path);
            InputStream inputStream = Files.newInputStream(file.toPath());
            ossClient.putObject(bucketName, ossFilePath, inputStream);
            fileUrl = CLOUDCDN_DOMAIN + ossFilePath;
            inputStream.close();
        } catch (IOException e) {
            logger.error("uploading failed {}", e.getMessage(), e);
        }
        return fileUrl;
    }

    public String genFileName() {
        return UUID.randomUUID().toString().substring(0, 5);
    }


    public String uploadWebFile(MultipartFile file) {
        String fileUrl;
        try {
            // 设置存储路径
            fileUrl = WEB_PATH + "op_" + DateHelper.getNowSeconds() + "_" + genFileName() + ".png";
            ossClient.putObject(bucketName, fileUrl, file.getInputStream());
        } catch (IOException e) {
            logger.error("uploadWebFile failed {}", e.getMessage(), e);
            throw new CommonH5Exception();
        }
        return fileUrl;
    }


    public String doesObjectExist(String fileName) {
        String url = null;
        try {
            // 设置存储路径
            String ossFilePath = WEB_PATH + fileName;
            if (ossClient.doesObjectExist(bucketName, ossFilePath)) {
                url = CLOUDCDN_DOMAIN + ossFilePath;
            }
        } catch (Exception e) {
            logger.error("doesObjectExist failed fileName:{} msg:{}", fileName, e.getMessage(), e);
        }
        return url;
    }

}
