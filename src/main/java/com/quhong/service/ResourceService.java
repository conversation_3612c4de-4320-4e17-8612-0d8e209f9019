package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.quhong.data.ResourceMqData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class ResourceService {

    private static final Logger logger = LoggerFactory.getLogger(ResourceService.class);
    private static final String ROUTE_KEY = "resource_distribute_h5";

    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 下发资源，由python后台进行处理
     */
    public void sendResourceToMq(ResourceMqData data) {
        try {
            logger.info("send resource to mq. data={}", data);
            rabbitTemplate.convertAndSend("", ROUTE_KEY, JSON.toJSONString(data));
        } catch (Exception e) {
            logger.error("send gift to mq error. data={}", JSON.toJSONString(data), e);
        }
    }

}
