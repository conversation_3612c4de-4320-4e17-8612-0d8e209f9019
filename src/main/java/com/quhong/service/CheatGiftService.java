package com.quhong.service;

import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.mysql.dao.RoomMicDao;
import com.quhong.room.RoomWebSender;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
public class CheatGiftService {
    private static final Logger logger = LoggerFactory.getLogger(CheatGiftService.class);

    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private RoomMicDao roomMicDao;

    public void sendCheatGiftChange(String roomId, String fromUid) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                String path = "mic_change/to_all";
                Map<String, String> params = new HashMap<>();
                params.put("room_id", roomId);
                params.put("from_uid", fromUid);
                roomMicDao.incVersion(roomId);
                roomWebSender.sendPost(path, roomId, params);
            }
        });
        logger.info("send cheat gift change. roomId={} fromUid={}", roomId, fromUid);
    }
}
