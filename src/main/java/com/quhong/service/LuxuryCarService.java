package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.core.config.ServerConfig;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.LuxuryCarVO;
import com.quhong.redis.LuxuryCarRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/1/13
 */
@Service
public class LuxuryCarService {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private final Interner<String> stringPool = Interners.newWeakInterner();
    public static final String ACTIVITY_ID = "68c907844b31041905dfb878";
    private static final String SEND_GIFT_BEANS = "send_gift_beans";
    private static final String CUR_TASK_LEVEL = "cur_task_level;";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/motorCycle_club/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/motorCycle_club/?activityId=%s", ACTIVITY_ID);

    private static final List<LuxuryCarVO.Task> TASK_LIST = Arrays.asList(
            new LuxuryCarVO.Task(1, 100,   "MotorcycleClubLV1"),
            new LuxuryCarVO.Task(2, 12000,   "MotorcycleClubLV2"),
            new LuxuryCarVO.Task(3, 20000,  "MotorcycleClubLV3"),
            new LuxuryCarVO.Task(4, 30000, "MotorcycleClubLV4"),
            new LuxuryCarVO.Task(5, 240000, "MotorcycleClubLV5")
    );

    @Resource
    private LuxuryCarRedis luxuryCarRedis;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;

    public LuxuryCarVO getInfo(String activityId, String uid) {
        Map<String, Long> userDataMap = luxuryCarRedis.getUserData(activityId, uid);
        long curValue = userDataMap.getOrDefault(SEND_GIFT_BEANS, 0L);
        long curLevel = userDataMap.getOrDefault(CUR_TASK_LEVEL, 0L).intValue();
        List<LuxuryCarVO.Task> list = new ArrayList<>();
        for (LuxuryCarVO.Task task : TASK_LIST) {
            LuxuryCarVO.Task vo = new LuxuryCarVO.Task();
            BeanUtils.copyProperties(task, vo);
            vo.setCurValue((int)Math.min(curValue, task.getLimit()));
            vo.setStatus(curLevel >= task.getLevel() ? 1 : 0);
            list.add(vo);
        }
        return new LuxuryCarVO(list);
    }

    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        String uid = giftData.getFrom_uid();
        int sendBeans = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
        synchronized (stringPool.intern(uid)) {
            long afterValue = luxuryCarRedis.incUserData(activityId, uid, SEND_GIFT_BEANS, sendBeans);
            if (afterValue >= TASK_LIST.get(0).getLimit()) {
                int curLevel = (int) luxuryCarRedis.getUserData(activityId, uid, CUR_TASK_LEVEL);
                int newLevel = 0;
                for (LuxuryCarVO.Task task : TASK_LIST) {
                    if (afterValue >= task.getLimit() && curLevel < task.getLevel()) {
                        // 发送奖励
                        resourceKeyHandlerService.sendResourceData(uid, task.getRewardKey(), "", "");
                        newLevel = task.getLevel();
                    }
                }
                if (newLevel > curLevel) {
                    luxuryCarRedis.setUserData(activityId, uid, CUR_TASK_LEVEL, newLevel);
                }
            }
        }
    }
}
