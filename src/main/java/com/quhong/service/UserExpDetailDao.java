package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.quhong.cache.CacheMap;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.dao.MonthShardingDao;
import com.quhong.mysql.data.UserExpDetailData;
import com.quhong.mysql.mapper.ustar_log.UserExpDetailMapper;
import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;


@Component
public class UserExpDetailDao extends MonthShardingDao<UserExpDetailMapper> {
    private static final Logger logger = LoggerFactory.getLogger(UserExpDetailDao.class);
    /**
     * 缓存30分钟
     */
    private static final long CACHE_TIME_MILLIS = 30 * 60 * 1000L;
    private static final int TIME_OUT = 1;
    private final CacheMap<String, UserExpDetailData> cacheMap;

//    @Resource(name = DataRedisBean.USER_LEVEL)
//    private StringRedisTemplate redisTemplate;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate mainRedisTemplate;

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public UserExpDetailDao() {
        super("t_user_exp_detail");
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    public UserExpDetailData getUserExpDetail(String uid, String dateStr) {
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(dateStr)) {
            return null;
        }
        UserExpDetailData userExpDetailData = cacheMap.getData(getUserExpDetailKey(uid, dateStr), true);
        if (null != userExpDetailData) {
            return userExpDetailData;
        }
        userExpDetailData = getUserExpDetailFromRedis(uid, dateStr);
        if (null != userExpDetailData) {
            cacheMap.cacheData(getUserExpDetailKey(uid, dateStr), userExpDetailData);
            return userExpDetailData;
        }
        userExpDetailData = findUserExpDetail(uid, dateStr);
        if (userExpDetailData == null) {
            return null;
        }
        cacheUserExpDetail(userExpDetailData, dateStr);
        cacheMap.cacheData(getUserExpDetailKey(uid, dateStr), userExpDetailData);
        return userExpDetailData;
    }

    private UserExpDetailData getUserExpDetailFromRedis(String uid, String dateStr) {
        try {
            String userExpDetail = mainRedisTemplate.opsForValue().get(getUserExpDetailKey(uid, dateStr));
            if (StringUtils.isEmpty(userExpDetail)) {
                return null;
            }
            return JSON.parseObject(userExpDetail, UserExpDetailData.class);
        } catch (Exception e) {
            logger.error("get user exp detail from redis error. uid={} dateStr={}", uid, dateStr, e);
        }
        return null;
    }

    private void cacheUserExpDetail(UserExpDetailData userExpDetailData, String dateStr) {
        try {
            String key = getUserExpDetailKey(userExpDetailData.getUid(), dateStr);
            String json = JSON.toJSONString(userExpDetailData);
            mainRedisTemplate.opsForValue().set(key, json, TIME_OUT, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("cache user exp detail error. {}", e.getMessage(), e);
        }
    }

    private String getUserExpDetailKey(String uid, String dateStr) {
        return "exp_detail_" + dateStr + "_" + uid;
    }

    private UserExpDetailData findUserExpDetail(String uid, String dateStr) {
        try {
            String tableSuffix = DateHelper.DEFAULT.getTableSuffix(DateHelper.DEFAULT.parseDate(dateStr));
            createTable(tableSuffix);
            return tableMapper.getByUid(tableSuffix, uid, dateStr);
        } catch (Exception e) {
            logger.error("get user exp detail data from db error. uid={} dateStr={}", uid, dateStr, e);
        }
        return null;
    }

    public void insert(UserExpDetailData data) {
        try {
            String suffix = DateHelper.ARABIAN.getTableSuffix(DateHelper.formatDate(data.getCtime()));
            createTable(suffix);
            tableMapper.insert(suffix, data);
            cacheUserExpDetail(data, data.getDate_str());
        } catch (Exception e) {
            logger.error("insert user exp detail error. uid={} dateStr={}", data.getUid(), data.getDate_str(), e);
        }
    }

    public void update(UserExpDetailData data) {
        try {
            String suffix = DateHelper.ARABIAN.getTableSuffix(DateHelper.formatDate(data.getCtime()));
            tableMapper.update(suffix, data);
            cacheUserExpDetail(data, data.getDate_str());
        } catch (Exception e) {
            logger.error("update user exp detail error. uid={} dateStr={}", data.getUid(), data.getDate_str(), e);
        }
    }

    public void saveOrUpdate(UserExpDetailData detail) {
        if (detail.getId() == 0) {
            insert(detail);
        } else {
            update(detail);
        }
    }
}
