package com.quhong.service;

import com.quhong.config.AsyncConfig;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.queues.SlowTaskQueue;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.GiftBoxInfo;
import com.quhong.data.LuckyBoxInfo;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.dto.GiftBoxDTO;
import com.quhong.dto.LuckyBoxDTO;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.ApiResult;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.GameHttpCode;
import com.quhong.exception.GameException;
import com.quhong.feign.DataCenterService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.LuckyBoxDailyNumData;
import com.quhong.mongo.data.LuckyBoxData;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.obj.UNameObject;
import com.quhong.msg.room.BigLuckyBoxMsg;
import com.quhong.msg.room.GiftBoxPushMsg;
import com.quhong.msg.room.LuckyBoxPushMsg;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.GiftBoxConfData;
import com.quhong.mysql.data.GiftBoxData;
import com.quhong.mysql.data.GiftBoxRecordData;
import com.quhong.mysql.data.GiftData;
import com.quhong.redis.GiftBoxRedis;
import com.quhong.redis.LuckyBoxRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.redis.MicFrameRedis;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/21
 */
@Service
public class LuckyBoxService extends SlowTaskQueue {

    private static final Logger logger = LoggerFactory.getLogger(LuckyBoxService.class);

    /**
     * 根据钻石数倒序排序
     */
    private static final Comparator<LuckyBoxRecordVO.GetBoxInfo> BEANS_DESC = Comparator.comparing(LuckyBoxRecordVO.GetBoxInfo::getBeans).reversed();
    /**
     * 根据创建时间升序排序
     */
    private static final Comparator<CheckRoomLuckyBoxVO.BoxInfo> CTIME_ASC = Comparator.comparing(CheckRoomLuckyBoxVO.BoxInfo::getCtime);

    private static final int LUCKY_BOX_TAKE_MONEY = 5; // 抽成钻石

    private static final int USER_LEVEL_LIMIT = 20; // 红包用户等级限制
    private static final String VALID_STATUS = "active";

    private static final int GIFT_BOX_NUM = 50;
    private static final int GIFT_BOX_VALID = 10;

    private static final int SEND_LUCKY_BOX_ACT_TYPE = 800;
    private static final String SEND_LUCKY_BOX_TITLE = "Send Lucky Box";

    private static final int GET_LUCKY_BOX_ACT_TYPE = 801;
    private static final String GET_LUCKY_BOX_TITLE = "Get Lucky Box";

    private static final int RETURN_LUCKY_BOX_ACT_TYPE = 802;
    private static final String RETURN_LUCKY_BOX_TITLE = "Return Lucky Box";

    private static final int SEND_GIFT_BOX_ACT_TYPE = 803;
    private static final String SEND_GIFT_BOX_TITLE = "Send Gift Box";

    private static final int RETURN_GIFT_BOX_ACT_TYPE = 3001;
    private static final String RETURN_GIFT_BOX_TITLE = "Gift Box Return";

    private static final int LOOP_TIME = 5 * 1000; // 每5秒扫描一下是否有未领取完的红包
    private static final int BOX_TIME_OUT = 300; // 红包超时时间
    private static final int GIFT_BOX_TIME_OUT = 300; // 礼物红包超时时间

    private static final GiftBoxConfigVO GIFT_BOX_CONFIG_VO = new GiftBoxConfigVO();
    private static final GiftBoxConfigVO.LuckyBoxConfig LUCKY_BOX_CONFIG = new GiftBoxConfigVO.LuckyBoxConfig();

    private static final int LUCKY_BOX_MSG = 0;
    private static final int GIFT_BOX_MSG = 1;

    private static final float ROBOT_POOL_BOX_NUM_PROP = 0.2F;

    @Resource
    private MongoRoomDao roomDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private MicFrameRedis micFrameRedis;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private LuckyBoxDao luckyBoxDao;
    @Resource
    private LuckyBoxRedis luckyBoxRedis;
    @Resource
    private BeansTotalDevoteDao beansTotalDevoteDao;
    @Resource
    private LuckyBoxDailyNumDao luckyBoxDailyNumDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private GiftBoxConfDao giftBoxConfDao;
    @Resource
    private GiftDao giftDao;
    @Resource
    private GiftBoxDao giftBoxDao;
    @Resource
    private GiftBoxRedis giftBoxRedis;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private GiftBoxRecordDao giftBoxRecordDao;
    @Resource(name = AsyncConfig.ASYNC_TASK)
    private Executor executor;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private ActorCommonService actorCommonService;


    @PostConstruct
    public void postInit() {
        // 初始化礼物红包配置
        if (ServerConfig.isProduct()) {
            LUCKY_BOX_CONFIG.setNum_min(100);
            LUCKY_BOX_CONFIG.setNum_max(1000);
            LUCKY_BOX_CONFIG.setBeans_min(1000);
            LUCKY_BOX_CONFIG.setBeans_max(20000);
        } else {
            LUCKY_BOX_CONFIG.setNum_min(100);
            LUCKY_BOX_CONFIG.setNum_max(1000);
            LUCKY_BOX_CONFIG.setBeans_min(1000);
            LUCKY_BOX_CONFIG.setBeans_max(20000);
        }
        buildGiftBoxConfigVO();
        TimerService.getService().addDelay(new LoopTask(this, LOOP_TIME) {
            @Override
            protected void execute() {
                // 返钻过期未领取完的幸运红包
                handleExpiredLuckyBox();
                // 返钻过期未领取完的礼物红包
                handleExpiredGiftBox();
            }
        });
    }

    /**
     * 返钻过期未领取完的幸运红包
     */
    private void handleExpiredLuckyBox() {
        Set<String> boxIdSet = luckyBoxRedis.getWaitingEndBoxIds(DateHelper.getNowSeconds());
        if (CollectionUtils.isEmpty(boxIdSet)) {
            return;
        }
        logger.info("dismiss expire lucky box. boxIdSet.size={} boxIdSet={}", boxIdSet.size(), Arrays.toString(boxIdSet.toArray()));
        for (String boxId : boxIdSet) {
            luckyBoxRedis.removeBoxTimerWaiting(boxId);
            LuckyBoxData data = luckyBoxDao.findData(boxId);
            if (data == null) {
                logger.error("can not find lucky box data. boxId={}", boxId);
                return;
            }
            // 退还过期未领取完的红包
            List<Integer> allBoxMoney = luckyBoxRedis.getAllBoxMoneyAndRemove(boxId);
            logger.info("allBoxMoney={}", Arrays.toString(allBoxMoney.toArray()));
            if (CollectionUtils.isEmpty(allBoxMoney)) {
                // 增加发送红包用户的生涯发钻数
                beansTotalDevoteDao.incrDevote(data.getUid(), data.getMoney());
                return;
            }
            // 退还钻石
            int totalMoney = allBoxMoney.stream().reduce(Integer::sum).orElse(0);
            if (totalMoney > 0) {
                MoneyDetailReq moneyDetailReq = buildMoneyDetailReq(
                        data.getUid(),
                        data.getRoom_id(),
                        RETURN_LUCKY_BOX_ACT_TYPE,
                        totalMoney,
                        RETURN_LUCKY_BOX_TITLE
                );
                mqSenderService.asyncChargeDiamonds(moneyDetailReq);
                logger.info("return lucky box. uid={} boxId={} totalNum={} totalMoney={}", data.getUid(), boxId, allBoxMoney.size(), totalMoney);
            }
            // 增加发送红包用户的生涯发钻数
            beansTotalDevoteDao.incrDevote(data.getUid(), data.getMoney() - totalMoney);
            if (VALID_STATUS.equals(data.getValid())) {
                // 更新数据库数据状态
                luckyBoxDao.updateValidEnd(boxId);
            }
        }
    }

    /**
     * 返钻过期未领取完的礼物红包
     */
    private void handleExpiredGiftBox() {
        int nowTime = DateHelper.getNowSeconds();
        Set<String> strBoxIdSet = giftBoxRedis.getWaitingEndBoxIds(nowTime);
        if (CollectionUtils.isEmpty(strBoxIdSet)) {
            return;
        }
        Set<Integer> boxIdSet = strBoxIdSet.stream().map(Integer::parseInt).collect(Collectors.toSet());
        logger.info("dismiss expire gift box. boxIdSet.size={} boxIdSet={}", boxIdSet.size(), Arrays.toString(boxIdSet.toArray()));
        for (Integer boxId : boxIdSet) {
            giftBoxRedis.removeBoxTimerWaiting(boxId);
            GiftBoxData data = giftBoxDao.selectOne(boxId);
            if (data == null) {
                logger.error("can not find gift box data. boxId={}", boxId);
                return;
            }
            // 退还过期未领取完的红包
            List<Integer> allBoxGiftList = giftBoxRedis.getAllBoxGiftAndRemove(boxId);
            if (CollectionUtils.isEmpty(allBoxGiftList)) {
                return;
            }
            // 退还钻石
            int totalMoney = allBoxGiftList.stream().map(k -> giftDao.getGiftFromCache(k)).filter(Objects::nonNull).mapToInt(GiftData::getPrice).sum();
            if (totalMoney > 0) {
                MoneyDetailReq moneyDetailReq = buildMoneyDetailReq(
                        data.getUid(),
                        data.getRoomId(),
                        RETURN_GIFT_BOX_ACT_TYPE,
                        totalMoney,
                        RETURN_GIFT_BOX_TITLE
                );
                mqSenderService.asyncChargeDiamonds(moneyDetailReq);
                logger.info("return gift box. uid={} boxId={} totalNum={} totalMoney={}", data.getUid(), boxId, allBoxGiftList.size(), totalMoney);
            }
            // 更新数据库数据状态
            giftBoxDao.updateStatus(boxId, 2);
        }
    }

    /**
     * 初始化礼物红包配置
     */
    private void buildGiftBoxConfigVO() {
        List<GiftBoxConfData> giftBoxConfDataList = giftBoxConfDao.getGiftBoxConfDataList();
        List<GiftBoxConfigVO.GiftBoxConfig> giftBoxConfigVos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(giftBoxConfDataList)) {
            Map<Integer, List<GiftBoxConfData>> map = giftBoxConfDataList.stream().collect(Collectors.groupingBy(GiftBoxConfData::getBoxType));
            for (Map.Entry<Integer, List<GiftBoxConfData>> entry : map.entrySet()) {
                List<GiftBoxConfigVO.Gift> giftList = new ArrayList<>();
                int boxBeans = 0;
                int totalNum = 0;
                for (GiftBoxConfData data : entry.getValue()) {
                    GiftData giftData = giftDao.getGiftFromCache(data.getGiftId());
                    if (giftData == null) {
                        continue;
                    }
                    boxBeans = data.getBoxBeans();
                    totalNum += data.getGiftNum();
                    giftList.add(new GiftBoxConfigVO.Gift(
                            giftData.getGicon(),
                            data.getGiftNum(),
                            data.getGiftId()));
                }
                giftBoxConfigVos.add(new GiftBoxConfigVO.GiftBoxConfig(
                        giftList,
                        entry.getKey(),
                        boxBeans,
                        totalNum));
            }
            giftBoxConfigVos.sort(Comparator.comparing(GiftBoxConfigVO.GiftBoxConfig::getBox_beans));
        }
        GIFT_BOX_CONFIG_VO.setGift_box_conf(giftBoxConfigVos);
        GIFT_BOX_CONFIG_VO.setLucky_box_conf(LUCKY_BOX_CONFIG);
    }

    /**
     * 发送红包
     */
    public SendLuckyBoxVO sendLuckyBox(LuckyBoxDTO reqDTO) {
        String uid = reqDTO.getUid();
        // 红包留言长度不能大于50
        if (!StringUtils.isEmpty(reqDTO.getMsg()) && reqDTO.getMsg().length() > 50) {
            logger.info("msg too long. msg={}", reqDTO.getMsg());
            throw new GameException(GameHttpCode.MSG_TOO_LONG);
        }
        // 红包个数和钻石不能为空
        if (reqDTO.getMoney() == 0 || reqDTO.getNum() == 0) {
            logger.info("not money or number. money={} num={}", reqDTO.getMoney(), reqDTO.getNum());
            throw new GameException(GameHttpCode.NOT_MONEY_OR_NUMBER);
        }
        // 红包数量必须在100~1000之间
        if (reqDTO.getNum() < LUCKY_BOX_CONFIG.getNum_min() || reqDTO.getNum() > LUCKY_BOX_CONFIG.getNum_max()) {
            logger.info("Boxes number:{}~{}", LUCKY_BOX_CONFIG.getNum_min(), LUCKY_BOX_CONFIG.getNum_max());
            throw new GameException(GameHttpCode.BOXES_NUMBER_RANGE, new Object[]{LUCKY_BOX_CONFIG.getNum_min(), LUCKY_BOX_CONFIG.getNum_max()});
        }
        // 红包总金额必须在1000~20000之间
        if (reqDTO.getMoney() < LUCKY_BOX_CONFIG.getBeans_min() || reqDTO.getMoney() > LUCKY_BOX_CONFIG.getBeans_max()) {
            logger.info("Diamonds number:{}~{}", LUCKY_BOX_CONFIG.getBeans_min(), LUCKY_BOX_CONFIG.getBeans_max());
            throw new GameException(GameHttpCode.DIAMONDS_NUMBER_RANGE, new Object[]{LUCKY_BOX_CONFIG.getBeans_min(), LUCKY_BOX_CONFIG.getBeans_max()});
        }
        MongoRoomData room = roomDao.findData(reqDTO.getRoom_id());
        if (room == null) {
            logger.info("can not find room data. roomId={}", reqDTO.getRoom_id());
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        // 房间上锁不能发送红包
        if (!StringUtils.isEmpty(room.getPwd())) {
            logger.info("The room is locked so you cannot send lucky box. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoom_id());
            throw new GameException(GameHttpCode.ROOM_IS_LOCKED_CANNOT_SEND_LUCKY_BOX);
        }
        // 被封的房间不能发送红包
        if (checkRoomBlocked(reqDTO.getRoom_id())) {
            logger.info("The room is blocked so you cannot send lucky box. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoom_id());
            throw new GameException(GameHttpCode.ROOM_IS_BLOCKED_CANNOT_SEND_LUCKY_BOX);
        }
        int totalMoney = reqDTO.getMoney() + LUCKY_BOX_TAKE_MONEY;
        ActorData actorData = actorDao.getActorData(uid);
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", uid);
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        deductCost(uid, reqDTO.getRoom_id(), LuckyBoxService.SEND_LUCKY_BOX_ACT_TYPE, totalMoney, LuckyBoxService.SEND_LUCKY_BOX_TITLE);
        LuckyBoxData luckyBoxData = buildLuckyBoxData(reqDTO);
        // 保存红包信息
        luckyBoxDao.save(luckyBoxData);
        String boxId = luckyBoxData.get_id().toString();
        saveLuckyBoxInfo(boxId, luckyBoxData);
        luckyBoxRedis.saveRoomIdByBoxId(boxId, reqDTO.getRoom_id());
        if (StringUtils.isEmpty(boxId)) {
            logger.error("boxId is empty Data error. boxId={}", boxId);
            throw new GameException(GameHttpCode.SERVER_ERROR);
        }
        // 分配红包
        assignLuckyBox(reqDTO.getNum(), reqDTO.getMoney(), boxId);
        // 增加用户生涯发钻数
        beansTotalDevoteDao.incrDevote(uid, LUCKY_BOX_TAKE_MONEY);
        if (userLevelDao.getUserLevel(uid) < USER_LEVEL_LIMIT) {
            luckyBoxRedis.saveSendOrReceiveRecord(uid, LuckyBoxRedis.SEND_RECORD, reqDTO.getMoney());
        }
        int rmLock = !StringUtils.isEmpty(room.getPwd()) ? 1 : 0;
        int rmCount = room.getOnline();
        saveLuckyBoxDailyNumData(uid, reqDTO.getRoom_id(), boxId, totalMoney, reqDTO.getNum(), 1, rmLock, rmCount, reqDTO);
        // 保存红包过期时间
        int expireTime = DateHelper.getNowSeconds() + BOX_TIME_OUT;
        luckyBoxRedis.setBoxTimerWaiting(boxId, expireTime);
        // 发送im消息
        sendLuckyBoxPushMsg(reqDTO.getRoom_id(), uid, boxId, reqDTO.getMoney(), reqDTO.getMsg(), expireTime);
        return new SendLuckyBoxVO(boxId, actorData.getBeans() - totalMoney, GIFT_BOX_NUM, GIFT_BOX_VALID);
    }

    private boolean checkRoomBlocked(String roomId) {
        ActorData actorData = actorDao.getActorData(RoomUtils.getRoomHostId(roomId));
        return actorData.getValid() == 0;
    }

    /**
     * 抢红包
     */
    public GetLuckyBoxVO getLuckyBox(LuckyBoxDTO reqDTO) {
        String uid = reqDTO.getUid();
        String boxId = reqDTO.getBox_id();
        if (StringUtils.isEmpty(boxId)) {
            logger.error("getLuckyBox param error. uid={} boxId={}", uid, boxId);
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        int userLevel = userLevelDao.getUserLevel(uid);
        boolean isRobot = reqDTO.getRobot() == 1;
        if (!isRobot && getLuckyBoxNumLimit(reqDTO.getUid(), userLevel)) {
            // 非机器人每日抢红包有数量限制
            logger.info("Sorry, you didn't get diamonds. uid={} boxId={}", uid, boxId);
            throw new GameException(GameHttpCode.CAN_NOT_GET_DIAMONDS);
        }
        String roomId = luckyBoxRedis.getRoomIdByBoxId(reqDTO.getBox_id());
        LuckyBoxInfo luckyBoxInfo = luckyBoxRedis.getLuckyBoxInfo(roomId, boxId);
        if (luckyBoxInfo == null) {
            logger.info("No diamonds remaining in this box. uid={} roomId={} boxId={}", uid, roomId, boxId);
            throw new GameException(GameHttpCode.NO_DIAMONDS_REMAINING);
        }
        if (!isRobot && userLevel < USER_LEVEL_LIMIT) {
            // 非机器人，20级以下用户，单日接收红包不得超过10000
            int receiveNum = luckyBoxRedis.getSendOrReceiveMoney(uid, LuckyBoxRedis.RECEIVE_RECORD);
            int receiveNumLimit = 10000;
            if (receiveNum > receiveNumLimit) {
                logger.info("Today's quota has been used up, please upgrade to level 20 or above. uid={} roomId={} boxId={}", uid, roomId, boxId);
                throw new GameException(GameHttpCode.QUOTA_HAS_BEEN_USED_UP);
            }
        }
        String aid = luckyBoxInfo.getUid();
        // 自己不能抢自己发的红包
        if (uid.equals(aid)) {
            logger.info("can not get yourself box. uid={} roomId={} boxId={}", uid, roomId, boxId);
            throw new GameException(GameHttpCode.CAN_NOT_GET_YOURSELF_BOX);
        }
        // 抢过一次就不能再抢了
        if (luckyBoxRedis.hasGainedLuckyBox(boxId, uid)) {
            logger.info("you can not get box again. uid={} roomId={} boxId={}", uid, roomId, boxId);
            throw new GameException(GameHttpCode.CAN_NOT_GET_BOX_AGAIN);
        }
        int money = luckyBoxRedis.getBoxMoney(boxId, isRobot);
        if (money == 0) {
            if (luckyBoxRedis.getBoxNum(boxId) == 0) {
                // 抢完了
                removeLuckyBoxDataInRedis(roomId, boxId);
                luckyBoxDao.updateValidEnd(boxId);
            }
            logger.info("Sorry, you didn't get diamonds. uid={} roomId={} boxId={}", uid, roomId, boxId);
            throw new GameException(GameHttpCode.CAN_NOT_GET_DIAMONDS);
        }
        ActorData actorData = actorDao.getActorData(uid);
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", uid);
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        // 保存红包领取记录
        luckyBoxRedis.saveLuckyBoxGainRecord(boxId, uid);
        luckyBoxRedis.saveGainLuckyBoxReward(uid, boxId);
        if (!isRobot && userLevel < USER_LEVEL_LIMIT) {
            luckyBoxRedis.saveSendOrReceiveRecord(uid, LuckyBoxRedis.RECEIVE_RECORD, money);
        }
        // 异步执行钻石下发和数据入库
        executor.execute(() -> asyncSendBeansAndSaveData(roomId, boxId, uid, money, reqDTO));
        return new GetLuckyBoxVO(money, actorData.getBeans() + money);
    }

    /**
     * 进房间时检查房间内的红包
     */
    public CheckRoomLuckyBoxVO checkRoomLuckyBox(LuckyBoxDTO reqDTO) {
        return new CheckRoomLuckyBoxVO(getRoomLuckyBoxList(reqDTO), getRoomGiftBoxList(reqDTO));
    }

    /**
     * 房间内红包领取记录
     */
    public LuckyBoxRecordVO getLuckyBoxRecord(LuckyBoxDTO reqDTO) {
        LuckyBoxData luckyBoxData = luckyBoxDao.findData(reqDTO.getBox_id());
        if (luckyBoxData == null) {
            logger.error("can not find lucky box data. boxId={}", reqDTO.getBox_id());
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        List<LuckyBoxDailyNumData> boxHistoryList = luckyBoxDailyNumDao.getLuckyGainReward(reqDTO.getBox_id());
        int hasGainedBoxNum = 0;
        List<LuckyBoxRecordVO.GetBoxInfo> list;
        if (!CollectionUtils.isEmpty(boxHistoryList)) {
            Map<String, LuckyBoxRecordVO.GetBoxInfo> getBoxInfoMap = new HashMap<>(boxHistoryList.size());
            for (LuckyBoxDailyNumData data : boxHistoryList) {
                hasGainedBoxNum += 1;
                if (getBoxInfoMap.containsKey(data.getUid())) {
                    int beans = getBoxInfoMap.get(data.getUid()).getBeans();
                    getBoxInfoMap.get(data.getUid()).setBeans(beans + data.getBeans());
                } else {
                    ActorData actorData = actorDao.getActorDataFromCache(data.getUid());
                    int vipLevel = vipInfoDao.getIntVipLevel(data.getUid());
                    String vipMedal = actorCommonService.getCommonVipMedal(data.getUid(), vipLevel);
                    LuckyBoxRecordVO.GetBoxInfo boxInfo = new LuckyBoxRecordVO.GetBoxInfo(
                            actorData.getName(),
                            ImageUrlGenerator.generateNormalUrl(actorData.getHead()),
                            vipLevel,
                            vipMedal,
                            data.getBeans(),
                            vipLevel > 0 ? 1 : 0);
                    getBoxInfoMap.put(data.getUid(), boxInfo);
                    boxInfo.setMicFrame(micFrameRedis.getMicSourceFromCache(actorData.getUid()));
                }
            }
            list = new ArrayList<>(getBoxInfoMap.values());
            // 根据红包钻石数倒序排序
            list.sort(BEANS_DESC);
        } else {
            list = Collections.emptyList();
        }
        // 红包发送者信息
        LuckyBoxRecordVO.BoxOwnerInfo ownerInfo = getBoxOwnerInfo(luckyBoxData.getUid());
        // 红包信息
        LuckyBoxRecordVO.BoxInfo boxInfo = new LuckyBoxRecordVO.BoxInfo(luckyBoxData.getMsg(), luckyBoxData.getNum(), hasGainedBoxNum);
        return new LuckyBoxRecordVO(list, boxInfo, ownerInfo);
    }

    /**
     * 房间红包和礼物红包配置
     */
    public GiftBoxConfigVO getGiftBoxConfig() {
        return GIFT_BOX_CONFIG_VO;
    }

    /**
     * 发送礼物红包
     */
    public SendGiftBoxVO sendGiftBox(GiftBoxDTO reqDTO) {
        ActorData actorData = actorDao.getActorData(reqDTO.getUid());
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", reqDTO.getUid());
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        // 红包留言长度不能大于50
        if (!StringUtils.isEmpty(reqDTO.getMsg()) && reqDTO.getMsg().length() > 50) {
            logger.info("msg too long. msg={}", reqDTO.getMsg());
            throw new GameException(GameHttpCode.MSG_TOO_LONG);
        }
        MongoRoomData room = roomDao.findData(reqDTO.getRoom_id());
        if (room == null) {
            logger.info("can not find room data. roomId={}", reqDTO.getRoom_id());
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        // 房间上锁不能发送红包
        if (!StringUtils.isEmpty(room.getPwd())) {
            logger.info("The room is locked so you cannot send lucky box. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoom_id());
            throw new GameException(GameHttpCode.ROOM_IS_LOCKED_CANNOT_SEND_LUCKY_BOX);
        }
        // 被封的房间不能发送红包
        if (checkRoomBlocked(reqDTO.getRoom_id())) {
            logger.info("The room is blocked so you cannot send lucky box. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoom_id());
            throw new GameException(GameHttpCode.ROOM_IS_BLOCKED_CANNOT_SEND_LUCKY_BOX);
        }
        GiftBoxConfigVO.GiftBoxConfig giftBoxConfig = getGiftBoxConfig(reqDTO.getBox_type());
        if (giftBoxConfig == null) {
            logger.error("invalid box_type. box_type={}", reqDTO.getBox_type());
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        int boxBeans = giftBoxConfig.getBox_beans();
        deductCost(reqDTO.getUid(), reqDTO.getRoom_id(), SEND_GIFT_BOX_ACT_TYPE, boxBeans, SEND_GIFT_BOX_TITLE);
        int nowTime = DateHelper.getNowSeconds();
        GiftBoxData giftBoxData = new GiftBoxData(reqDTO.getUid(), reqDTO.getRoom_id(), 1, reqDTO.getBox_type(), reqDTO.getMsg(), nowTime, nowTime);
        giftBoxDao.insert(giftBoxData);
        if (giftBoxData.getId() == null) {
            logger.error("add send record error. giftBoxData={}", giftBoxData.toString());
            throw new GameException(GameHttpCode.SERVER_ERROR);
        }
        int boxId = giftBoxData.getId();
        List<Integer> giftIdList = new ArrayList<>();
        for (GiftBoxConfigVO.Gift gift : giftBoxConfig.getList()) {
            for (int i = 1; i <= gift.getGift_num(); i++) {
                giftIdList.add(gift.getGift_id());
            }
        }
        Collections.shuffle(giftIdList);
        // 保存礼物红包信息
        giftBoxRedis.addGiftBox(boxId, giftIdList);
        saveGiftBoxInfo(boxId, giftBoxData);
        giftBoxRedis.saveRoomGiftBox(reqDTO.getRoom_id(), boxId);
        // 设置过期时间
        int expireTime = nowTime + GIFT_BOX_TIME_OUT;
        giftBoxRedis.setBoxTimerWaiting(boxId, expireTime);
        // 发送im消息
        sendGiftBoxPushMsg(reqDTO.getRoom_id(), reqDTO.getUid(), boxId, boxBeans, reqDTO.getMsg(), expireTime);
        return new SendGiftBoxVO(boxId, actorData.getBeans() - boxBeans, GIFT_BOX_NUM, GIFT_BOX_VALID);
    }

    /**
     * 领取礼物红包
     */
    public GetGiftBoxVO getGiftBox(GiftBoxDTO reqDTO) {
        GiftBoxInfo giftBoxInfo = giftBoxRedis.getGiftBoxInfo(reqDTO.getRoom_id(), reqDTO.getBox_id());
        // 判断礼物红包是否存在或者已经被抢完了
        if (giftBoxInfo == null) {
            logger.info("box invalid. uid={} roomId={} boxId={}", reqDTO.getUid(), reqDTO.getRoom_id(), reqDTO.getBox_id());
            throw new GameException(GameHttpCode.BOX_INVALID);
        }
        // 抢过一次就不能再抢了
        if (giftBoxRedis.hasGainedGiftBox(reqDTO.getBox_id(), reqDTO.getUid())) {
            logger.info("you can not get box again. uid={} roomId={} boxId={}", reqDTO.getUid(), reqDTO.getRoom_id(), reqDTO.getBox_id());
            throw new GameException(GameHttpCode.CAN_NOT_GET_BOX_AGAIN);
        }
        int giftId = giftBoxRedis.getGiftBox(reqDTO.getBox_id());
        if (giftId == 0) {
            // 抢完了
            removeGiftBoxDataInRedis(reqDTO.getRoom_id(), reqDTO.getBox_id());
            giftBoxDao.updateStatus(reqDTO.getBox_id(), 1);
            logger.info("Sorry, you didn't receive the reward. uid={} roomId={} boxId={}", reqDTO.getUid(), reqDTO.getRoom_id(), reqDTO.getBox_id());
            throw new GameException(GameHttpCode.NOT_RECEIVE_THE_REWARD);
        }
        GiftData giftData = giftDao.getGiftFromCache(giftId);
        if (giftData == null) {
            logger.error("box invalid. uid={} roomId={} boxId={} giftId={}", reqDTO.getUid(), reqDTO.getRoom_id(), reqDTO.getBox_id(), giftId);
            throw new GameException(GameHttpCode.BOX_INVALID);
        }
        sendGiftReward(reqDTO.getUid(), giftId);
        // 保存抢红包记录
        giftBoxRedis.saveLuckyBoxGainRecord(reqDTO.getBox_id(), reqDTO.getUid());
        giftBoxRecordDao.insert(new GiftBoxRecordData(reqDTO.getUid(), reqDTO.getRoom_id(), reqDTO.getBox_id(), giftId, DateHelper.getNowSeconds()));
        return new GetGiftBoxVO(giftId, giftData.getGicon());
    }

    /**
     * 获取房间幸运红包信息
     */
    private List<CheckRoomLuckyBoxVO.BoxInfo> getRoomLuckyBoxList(LuckyBoxDTO reqDTO) {
        Map<String, LuckyBoxInfo> luckyBoxInfoMap = luckyBoxRedis.getRoomAllLuckyBoxInfo(reqDTO.getRoom_id());
        List<CheckRoomLuckyBoxVO.BoxInfo> boxInfoList = new ArrayList<>();
        if (!luckyBoxInfoMap.isEmpty()) {
            CheckRoomLuckyBoxVO.BoxInfo boxInfo;
            for (Map.Entry<String, LuckyBoxInfo> entry : luckyBoxInfoMap.entrySet()) {
                // 控制红包一次的数量
                if (boxInfoList.size() > 20) {
                    break;
                }
                String boxId = entry.getKey();
                LuckyBoxInfo luckyBoxInfo = entry.getValue();
                String aid = luckyBoxInfo.getUid();
                // 红包被抢完了
                if (luckyBoxRedis.getBoxNum(entry.getKey()) == 0) {
                    removeLuckyBoxDataInRedis(reqDTO.getRoom_id(), boxId);
                    continue;
                }
                // 红包已过期
                if (DateHelper.getNowSeconds() - luckyBoxInfo.getCtime() >= BOX_TIME_OUT) {
                    continue;
                }
                // 自己发的红包
                if (reqDTO.getUid().equals(aid)) {
                    continue;
                }
                // 已经领取过的红包
                if (luckyBoxRedis.hasGainedLuckyBox(boxId, reqDTO.getUid())) {
                    continue;
                }
                ActorData actorData = actorDao.getActorDataFromCache(aid);
                if (actorData == null) {
                    logger.error("can not find actor data. uid={}", aid);
                    continue;
                }
                boxInfo = new CheckRoomLuckyBoxVO.BoxInfo(
                        boxId,
                        luckyBoxInfo.getNum(),
                        luckyBoxInfo.getMsg(),
                        aid,
                        actorData.getName(),
                        ImageUrlGenerator.generateNormalUrl(actorData.getHead()),
                        vipInfoDao.getIntVipLevel(aid),
                        userLevelDao.getUserLevel(aid),
                        luckyBoxInfo.getCtime(),
                        luckyBoxInfo.getCtime() + BOX_TIME_OUT,
                        0);
                boxInfoList.add(boxInfo);
                boxInfoList.sort(CTIME_ASC);
            }
        }
        return boxInfoList;
    }

    /**
     * 获取房间礼物红包信息
     */
    private List<CheckRoomLuckyBoxVO.BoxInfo> getRoomGiftBoxList(LuckyBoxDTO reqDTO) {
        Map<String, GiftBoxInfo> giftBoxInfoMap = giftBoxRedis.getRoomAllGiftBoxInfo(reqDTO.getRoom_id());
        List<CheckRoomLuckyBoxVO.BoxInfo> giftBoxList = new ArrayList<>();
        if (!giftBoxInfoMap.isEmpty()) {
            CheckRoomLuckyBoxVO.BoxInfo boxInfo;
            for (Map.Entry<String, GiftBoxInfo> entry : giftBoxInfoMap.entrySet()) {
                // 控制红包一次的数量
                if (giftBoxList.size() > 20) {
                    break;
                }
                int boxId = Integer.parseInt(entry.getKey());
                GiftBoxInfo giftBoxInfo = entry.getValue();
                String aid = giftBoxInfo.getUid();
                // 红包被抢完了
                if (giftBoxRedis.getBoxNum(boxId) == 0) {
                    removeGiftBoxDataInRedis(reqDTO.getRoom_id(), boxId);
                    continue;
                }
                // 红包已过期
                if (DateHelper.getNowSeconds() - giftBoxInfo.getcTime() >= GIFT_BOX_TIME_OUT) {
                    continue;
                }
                // 自己发的红包
                if (reqDTO.getUid().equals(aid)) {
                    continue;
                }
                // 已经领取过的红包
                if (giftBoxRedis.hasGainedGiftBox(boxId, reqDTO.getUid())) {
                    continue;
                }
                ActorData actorData = actorDao.getActorDataFromCache(aid);
                if (actorData == null) {
                    logger.error("can not find actor data. uid={}", aid);
                    continue;
                }
                boxInfo = new CheckRoomLuckyBoxVO.BoxInfo(
                        boxId + "",
                        0,
                        giftBoxInfo.getMsg(),
                        aid,
                        actorData.getName(),
                        ImageUrlGenerator.generateNormalUrl(actorData.getHead()),
                        vipInfoDao.getIntVipLevel(aid),
                        userLevelDao.getUserLevel(aid),
                        giftBoxInfo.getcTime(),
                        giftBoxInfo.getcTime() + GIFT_BOX_TIME_OUT,
                        1);
                giftBoxList.add(boxInfo);
                giftBoxList.sort(CTIME_ASC);
            }
        }
        return giftBoxList;
    }


    private void removeGiftBoxDataInRedis(String roomId, Integer boxId) {
        giftBoxRedis.removeGiftBoxInfo(roomId, boxId);
        giftBoxRedis.removeRoomGiftBox(roomId, boxId);
    }

    /**
     * 礼物红包领取记录
     */
    public GiftBoxRecordVO getGiftBoxRecord(GiftBoxDTO reqDTO) {
        GiftBoxData giftBoxData = giftBoxDao.selectOne(reqDTO.getBox_id());
        if (giftBoxData == null) {
            logger.error("Gift box not existed. boxId={}", reqDTO.getBox_id());
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        List<GiftBoxRecordData> giftBoxRecordList = giftBoxRecordDao.selectList(reqDTO.getBox_id());
        List<GiftBoxRecordVO.BoxRecord> list;
        if (!CollectionUtils.isEmpty(giftBoxRecordList)) {
            Map<String, GiftBoxRecordVO.BoxRecord> boxRecordMap = new HashMap<>();
            Map<Integer, Integer> giftPriceMap = new HashMap<>();
            for (GiftBoxRecordData recordData : giftBoxRecordList) {
                String uid = recordData.getUid();
                String key = uid + "_" + recordData.getGiftId();
                if (boxRecordMap.containsKey(key)) {
                    int gotNum = boxRecordMap.get(key).getGot_num();
                    boxRecordMap.get(key).setGot_num(gotNum + 1);
                } else {
                    ActorData actorData = actorDao.getActorDataFromCache(uid);
                    if (actorData == null) {
                        logger.error("can not find actor data. uid={}", uid);
                        continue;
                    }
                    GiftData giftData = giftDao.getGiftFromDb(recordData.getGiftId());
                    if (giftData == null) {
                        logger.error("can not find gift data. giftId={}", recordData.getGiftId());
                        continue;
                    }
                    if (!giftPriceMap.containsKey(recordData.getGiftId())) {
                        giftPriceMap.put(recordData.getGiftId(), giftData.getPrice() != null ? giftData.getPrice() : 0);
                    }
                    GiftBoxRecordVO.BoxRecord boxRecord = new GiftBoxRecordVO.BoxRecord();
                    boxRecord.setUid(uid);
                    boxRecord.setName(actorData.getName());
                    boxRecord.setHead(ImageUrlGenerator.generateNormalUrl(actorData.getHead()));
                    int vipLevel = vipInfoDao.getIntVipLevel(uid);
                    boxRecord.setVip_level(vipLevel);
                    boxRecord.setVipMedal(actorCommonService.getCommonVipMedal(uid, vipLevel));
                    boxRecord.setGift_icon(giftData.getGicon());
                    boxRecord.setIdentify(vipLevel > 0 ? 1 : 0);
                    boxRecord.setGift_id(recordData.getGiftId());
                    boxRecord.setGot_num(1);
                    boxRecord.setPrize_type(1);
                    boxRecordMap.put(key, boxRecord);
                }
            }
            list = new ArrayList<>(boxRecordMap.values());
            // 根据总价值倒序排序
            Comparator<GiftBoxRecordVO.BoxRecord> totalPriceAsc = Comparator.comparing(o -> o.getGot_num() * giftPriceMap.get(o.getGift_id()));
            list.sort(totalPriceAsc.reversed());
        } else {
            list = Collections.emptyList();
        }
        // 礼物红包发送者信息
        ActorData ownerInfo = actorDao.getActorDataFromCache(giftBoxData.getUid());
        int vipLevel = vipInfoDao.getIntVipLevel(giftBoxData.getUid());
        String ownerVipMedal = actorCommonService.getCommonVipMedal(giftBoxData.getUid(), vipLevel);
        GiftBoxRecordVO.BoxSenderInfo boxSenderInfo = new GiftBoxRecordVO.BoxSenderInfo(
                ownerInfo != null ? ownerInfo.getName() : "",
                ownerInfo != null ? ImageUrlGenerator.generateNormalUrl(ownerInfo.getHead()) : "",
                micFrameRedis.getMicSourceFromCache(giftBoxData.getUid()),
                vipLevel,
                ownerVipMedal,
                vipLevel > 0 ? 1 : 0);
        GiftBoxConfigVO.GiftBoxConfig giftBoxConfig = getGiftBoxConfig(giftBoxData.getBoxType());
        // 礼物红包信息
        GiftBoxRecordVO.BoxInfo boxInfo = new GiftBoxRecordVO.BoxInfo(
                giftBoxData.getMsg(),
                giftBoxConfig != null && giftBoxConfig.getTotal_num() != null ? giftBoxConfig.getTotal_num() : 0,
                !CollectionUtils.isEmpty(giftBoxRecordList) ? giftBoxRecordList.size() : 0);
        return new GiftBoxRecordVO(list, boxInfo, boxSenderInfo);
    }

    /**
     * 保存LuckyBoxInfo到redis
     */
    private void saveLuckyBoxInfo(String boxId, LuckyBoxData luckyBoxData) {
        LuckyBoxInfo luckyBoxInfo = new LuckyBoxInfo();
        BeanUtils.copyProperties(luckyBoxData, luckyBoxInfo);
        luckyBoxInfo.setBoxId(boxId);
        luckyBoxRedis.saveLuckyBoxInfo(luckyBoxInfo);
    }

    /**
     * 保存GiftBoxInfo到redis
     */
    private void saveGiftBoxInfo(int boxId, GiftBoxData giftBoxData) {
        GiftBoxInfo giftBoxInfo = new GiftBoxInfo();
        BeanUtils.copyProperties(giftBoxData, giftBoxInfo);
        giftBoxInfo.setBoxId(boxId);
        giftBoxRedis.saveGiftBoxInfo(giftBoxInfo);
    }

    /**
     * 异步执行钻石下发和数据入库
     */
    public void asyncSendBeansAndSaveData(String roomId, String boxId, String uid, int money, LuckyBoxDTO reqDTO) {
        MoneyDetailReq moneyDetailReq = buildMoneyDetailReq(uid, roomId, LuckyBoxService.GET_LUCKY_BOX_ACT_TYPE, money, LuckyBoxService.GET_LUCKY_BOX_TITLE);
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
        // 保存抢红包记录到数据库
        saveLuckyBoxDailyNumData(uid, roomId, boxId, money, 1, 2, null, null, reqDTO);

        // 发送红包任务消息
        commonTaskService.sendCommonTaskMq(new CommonMqTopicData(uid, roomId, "", "", CommonMqTaskConstant.GET_LUCKY_BAG, 1));
    }

    /**
     * 发送红包消息
     */
    private void sendLuckyBoxPushMsg(String roomId, String uid, String boxId, int beans, String luckBoxMsg, int expireTime) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                RoomActorDetailData actorData = roomActorCache.getData(roomId, uid, false);
                if (actorData == null) {
                    return;
                }
                LuckyBoxPushMsg msg = new LuckyBoxPushMsg();
                msg.setBox_id(boxId);
                msg.setBox_type(LUCKY_BOX_MSG);
                msg.setBox_num(GIFT_BOX_NUM);
                msg.setValid_box(GIFT_BOX_VALID);
                msg.setMsg(luckBoxMsg);
                UNameObject uNameObject = buildUnameObject(actorData);
                msg.setUname(uNameObject);
                msg.setEnd_time(expireTime);
                roomWebSender.sendRoomWebMsg(roomId, uid, msg, true);
                if (beans >= 1000) {
                    // 发送大礼物红包消息
                    sendBigLuckyBoxMsg(boxId, roomId, uid, LUCKY_BOX_MSG, uNameObject);
                }
            }
        });
    }

    /**
     * 构建UNameObject
     */
    private UNameObject buildUnameObject(RoomActorDetailData actorData) {
        UNameObject userInfo = new UNameObject();
        userInfo.setRid(actorData.getRid());
        userInfo.setName(actorData.getName());
        userInfo.setRole(actorData.getNewRole());
        userInfo.setHead(actorData.getHead());
        // 设置vip
        userInfo.setVip(actorData.getVipLevel());
        userInfo.setVipMedal(actorData.getVipMedal());
        // 设置徽章
        userInfo.setBadgeList(actorData.getBadgeList());
        userInfo.setLevel(actorData.getLevel());
        // 设置气泡
        userInfo.setBid(actorData.getBubbleId());
        userInfo.setIdentify(actorData.getIdentify());
        userInfo.setUid(actorData.getAid());
        userInfo.setIsNewUser(actorData.getIsNewUser());
        return userInfo;
    }

    /**
     * 领取完红包删除redis数据
     */
    private void removeLuckyBoxDataInRedis(String roomId, String boxId) {
        luckyBoxRedis.removeLuckyBoxRoom(roomId, boxId);
        luckyBoxRedis.removeLuckyBoxInfo(roomId, boxId);
        luckyBoxRedis.removeBoxTimerWaiting(boxId);
    }

    /**
     * 保存LuckyBoxDailyNumData到数据库
     */
    private void saveLuckyBoxDailyNumData(String uid, String roomId, String boxId, int money, int num, int action, Integer rmLock, Integer rmCount, LuckyBoxDTO reqDTO) {
        LuckyBoxDailyNumData boxDailyNumData = new LuckyBoxDailyNumData();
        boxDailyNumData.setUid(uid);
        boxDailyNumData.setRoom_id(roomId);
        boxDailyNumData.setBox_id(boxId);
        boxDailyNumData.setBeans(money);
        boxDailyNumData.setNum(num);
        boxDailyNumData.setAction(action);
        boxDailyNumData.setC_time(DateHelper.getNowSeconds());
        if (action == 1) {
            boxDailyNumData.setRm_lock(rmLock);
            boxDailyNumData.setRm_count(rmCount);
        } else {
            boxDailyNumData.setX_pos(reqDTO.getX_pos());
            boxDailyNumData.setY_pos(reqDTO.getY_pos());
            boxDailyNumData.setIs_robot(reqDTO.getRobot());
        }
        luckyBoxDailyNumDao.save(boxDailyNumData);
    }

    /**
     * 抢红包个数限制
     */
    private boolean getLuckyBoxNumLimit(String uid, int userLevel) {
        // vip用户没有抢红包数量限制
        if (vipInfoDao.getIntVipLevel(uid) >= 1) {
            return false;
        }
        // 用户等级大于10级的没有抢红包数量限制
        if (userLevel > 10) {
            return false;
        }
        // 充值笔数大于2没有抢红包数量限制
        if (rechargeDailyInfoDao.getRechargeTotalNum(uid) > 2) {
            return false;
        }
        int nowTime = DateHelper.getNowSeconds();
        // 非vip和充值的level<=10的用户每天40个
        // 2小时内不能超过20个
        if (luckyBoxRedis.getUserGainLuckyBoxNum(uid, nowTime - 2 * 3600, nowTime) >= 20) {
            return true;
        }
        // 24小时内不能超过40个
        return luckyBoxRedis.getUserGainLuckyBoxNum(uid, nowTime - 24 * 3600, nowTime) >= 40;
    }

    private void assignLuckyBox(Integer num, Integer money, String boxId) {
        //固定100个或者200个红包发送
        num = num < 100 ? num : num <= 500 ? 100 : 200;
        int robotPoolBoxNum = (int)(num * ROBOT_POOL_BOX_NUM_PROP);
        List<Integer> robotPoolMoneyList = newWeChatSumLuckyBox(num, robotPoolBoxNum, money);
        int robotPoolBoxSumMoney = robotPoolMoneyList.stream().reduce(Integer::sum).orElse(0);
        luckyBoxRedis.addBoxMoneyInRobotPool(boxId, robotPoolMoneyList);
        int userPoolBoxNum = num - robotPoolBoxNum;
        money -= robotPoolBoxSumMoney;
        List<Integer> userPoolMoneyList = newWeChatSumLuckyBox(userPoolBoxNum, userPoolBoxNum, money);
        luckyBoxRedis.addBoxMoneyInUserPool(boxId, userPoolMoneyList);
    }

    /**
     * 微信算法(前面多后面少算法)
     */
    private List<Integer> newWeChatSumLuckyBox(int totalNum, int actualNum, int money) {
        List<Integer> moneyList = new ArrayList<>();
        while (totalNum != 1) {
            int boxMin = 1;
            int boxMax = (money / totalNum) * 10;
            int boxMoney;
            if (boxMax < 1) {
                boxMoney = 1;
            } else {
                boxMoney = ThreadLocalRandom.current().nextInt(boxMin, boxMax);
            }
            money -= boxMoney;
            if (money < 0) {
                money += boxMoney;
                boxMoney = 1;
                money -= boxMoney;
                if (money < 0) {
                    money += boxMoney;
                    boxMoney = 0;
                }
            }
            totalNum -= 1;
            moneyList.add(boxMoney);
            if (moneyList.size() >= actualNum) {
                return moneyList;
            }
        }
        moneyList.add(money);
        return moneyList;
    }

    /**
     * 微信算法
     */
    @Deprecated
    private void weChatSumLuckyBox(Integer num, Integer money, String boxId) {
        List<Integer> moneyList = new ArrayList<>();
        while (num != 1) {
            int boxMin = 1;
            int boxMax = (money / num) * 2;
            int boxMoney = ThreadLocalRandom.current().nextInt(boxMin, boxMax + 1);
            money -= boxMoney;
            num -= 1;
            if (money == 0) {
                boxMoney -= 1;
                money += 1;
            }
            moneyList.add(boxMoney);
        }
        moneyList.add(money);
        luckyBoxRedis.addBoxMoneyInUserPool(boxId, moneyList);
    }

    /**
     * 平均算法
     */
    @Deprecated
    private void avgSumLuckyBox(Integer num, Integer money, String boxId) {
        List<Integer> moneyList = new ArrayList<>();
        if (money >= num) {
            int countNum = money - num;
            for (int i = 0; i < num; i++) {
                int boxMoney = 1;
                if (countNum > 0) {
                    int ack = ThreadLocalRandom.current().nextInt(0, 3);
                    int moneyAdd = Math.min(countNum, ack);
                    countNum -= moneyAdd;
                    boxMoney += moneyAdd;
                }

                moneyList.add(boxMoney);
            }
        } else {
            while (money > 0) {
                money -= 1;
                int boxMoney = 1;
                moneyList.add(boxMoney);
            }
        }
        luckyBoxRedis.addBoxMoneyInUserPool(boxId, moneyList);
    }

    private LuckyBoxData buildLuckyBoxData(LuckyBoxDTO reqDTO) {
        int nowTime = DateHelper.getNowSeconds();
        LuckyBoxData data = new LuckyBoxData();
        data.setUid(reqDTO.getUid());
        data.setRoom_id(reqDTO.getRoom_id());
        data.setValid(VALID_STATUS);
        data.setMoney(reqDTO.getMoney());
        data.setNum(reqDTO.getNum());
        data.setMsg(reqDTO.getMsg());
        data.setCtime(nowTime);
        data.setMtime(nowTime);
        return data;
    }

    private MoneyDetailReq buildMoneyDetailReq(String uid, String roomId, int aType, int changeBean, String title) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setRoomId(roomId);
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(aType);
        moneyDetailReq.setChanged(changeBean);
        moneyDetailReq.setTitle(title);
        moneyDetailReq.setDesc("");
        return moneyDetailReq;
    }

    /**
     * 扣除发送红包费用
     */
    private void deductCost(String uid, String roomId, int actType, int changeBean, String title) {
        ApiResult<String> result = dataCenterService.reduceBeans(buildMoneyDetailReq(uid, roomId, actType, -changeBean, title));
        if (result.isError()) {
            if (1 == result.getCode().getCode()) {
                throw new GameException(GameHttpCode.DIAMOND_NOT_ENOUGH);
            }
            logger.error("reduce beans error, msg={}", result.getCode().getMsg());
            throw new GameException(GameHttpCode.CREATE_PK_FAILED);
        }
    }

    /**
     * 获取红包发送者信息
     */
    private LuckyBoxRecordVO.BoxOwnerInfo getBoxOwnerInfo(String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", uid);
            return null;
        }
        int vipLevel = vipInfoDao.getIntVipLevel(uid);
        String vipMedal = actorCommonService.getCommonVipMedal(uid, vipLevel);
        return new LuckyBoxRecordVO.BoxOwnerInfo(
                actorData.getName(),
                ImageUrlGenerator.generateNormalUrl(actorData.getHead()),
                micFrameRedis.getMicSourceFromCache(uid),
                vipLevel,
                vipMedal,
                vipLevel > 0 ? 1 : 0);
    }

    /**
     * 发送礼物红包消息
     */
    private void sendGiftBoxPushMsg(String roomId, String uid, int boxId, int beans, String luckBoxMsg, int expireTime) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                RoomActorDetailData actorData = roomActorCache.getData(roomId, uid, false);
                if (actorData == null) {
                    return;
                }
                UNameObject uNameObject = buildUnameObject(actorData);
                GiftBoxPushMsg msg = new GiftBoxPushMsg();
                msg.setBox_id(String.valueOf(boxId));
                msg.setBox_type(GIFT_BOX_MSG);
                msg.setBox_num(GIFT_BOX_NUM);
                msg.setValid_box(GIFT_BOX_VALID);
                msg.setMsg(luckBoxMsg);
                msg.setUname(uNameObject);
                msg.setEnd_time(expireTime);
                roomWebSender.sendRoomWebMsg(roomId, uid, msg, true);
                if (beans >= 1000) {
                    // 发送大礼物红包消息
                    sendBigLuckyBoxMsg(String.valueOf(boxId), roomId, uid, GIFT_BOX_MSG, uNameObject);
                }
            }
        });
    }

    private void sendBigLuckyBoxMsg(String boxId, String roomId, String uid, int boxType, UNameObject uNameObject) {
        BigLuckyBoxMsg bigMsg = new BigLuckyBoxMsg();
        bigMsg.setBoxId(boxId);
        bigMsg.setBoxType(boxType);
        bigMsg.setFromRoomId(roomId);
        bigMsg.setFromName(uNameObject.getName());
        bigMsg.setFromHead(uNameObject.getHead());
        bigMsg.setFromRid(uNameObject.getRid() + "");
        bigMsg.setFromVipLevel(uNameObject.getVip());
        bigMsg.setFromVipMedal(uNameObject.getVipMedal());
        roomWebSender.sendRoomWebMsg(RoomWebSender.ALL_ROOM, uid, bigMsg, false);
    }

    /**
     * 获取礼物红包配置
     */
    private GiftBoxConfigVO.GiftBoxConfig getGiftBoxConfig(int boxType) {
        List<GiftBoxConfigVO.GiftBoxConfig> giftBoxConfList = GIFT_BOX_CONFIG_VO.getGift_box_conf();
        Map<Integer, GiftBoxConfigVO.GiftBoxConfig> giftBoxConfigMap = giftBoxConfList.stream().collect(Collectors.toMap(GiftBoxConfigVO.GiftBoxConfig::getBox_type, Function.identity()));
        return giftBoxConfigMap.get(boxType);
    }

    /**
     * 下发资源奖励
     */
    private void sendGiftReward(String uid, Integer giftId) {
        logger.info("send bag gift to mq. toUid={}, giftId={}", uid, giftId);
        ResourcesDTO resourcesDTO = new ResourcesDTO();
        resourcesDTO.setUid(uid);
        resourcesDTO.setResId(String.valueOf(giftId));
        resourcesDTO.setResType(4);
        resourcesDTO.setDesc("get gift lucky box");
        resourcesDTO.setItemsSourceDetail("get gift lucky box");
        resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_GET);
        resourcesDTO.setmTime(DateHelper.getNowSeconds());
        resourcesDTO.setNum(1);
        resourcesDTO.setDays(-1);
        mqSenderService.asyncHandleResources(resourcesDTO);
    }
}
