package com.quhong.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.config.AsyncConfig;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.constant.FamilyConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.dto.FamilyDTO;
import com.quhong.dto.ImageDTO;
import com.quhong.dto.TextDTO;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.enums.UserHttpCode;
import com.quhong.exception.CommonException;
import com.quhong.feign.IDetectService;
import com.quhong.handler.HttpEnvData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.FamilyDevoteDao;
import com.quhong.mongo.dao.UserLevelDao;
import com.quhong.mongo.data.OfficialData;
import com.quhong.mysql.config.DBMysqlBean;
import com.quhong.mysql.dao.FamilyDao;
import com.quhong.mysql.dao.FamilyMemberDao;
import com.quhong.mysql.dao.FamilyRequestDao;
import com.quhong.mysql.data.CountData;
import com.quhong.mysql.data.FamilyData;
import com.quhong.mysql.data.FamilyMemberData;
import com.quhong.mysql.data.FamilyRequestData;
import com.quhong.redis.FamilyRedis;
import com.quhong.redis.FamilyTaskRedis;
import com.quhong.redis.GenFamilyRidRedis;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.utils.CDNUtils;
import com.quhong.utils.CollectionUtil;
import com.quhong.utils.MatchUtils;
import com.quhong.utils.PageUtils;
import com.quhong.vo.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

@Lazy
@Service
public class FamilyService {

    private static final Logger logger = LoggerFactory.getLogger(FamilyService.class);

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    public static final int MAX_MEMBER = 100;
    private static final int PAGE_SIZE = 20;
    private static final int MAX_ADMIN = 5;
    //    private static int FAMILY_DISMISS_SECONDS = (int) TimeUnit.MINUTES.toSeconds(5);
    private static int FAMILY_REQUEST_SECONDS = (int) TimeUnit.HOURS.toSeconds(48);

    private static int FAMILY_DISMISS_SECONDS = (int) TimeUnit.HOURS.toSeconds(72);
    //    private static int FAMILY_REQUEST_SECONDS = (int) TimeUnit.HOURS.toSeconds(72);
    //  24:家族主页 25: 家族成员申页
    private static final int FAMILY_HOME_PAGE = 24;
    private static final int FAMILY_REQUEST_PAGE = 25;

    // 审批通过，给新成员发
    private static final String FAMILY_ADD_NOTIFICATION = "Join family notification";
    private static final String FAMILY_ADD_NOTIFICATION_AR = "إشعار الانضمام للعائلة";
    private static final String FAMILY_JOINED = "Congratulations, you have successfully joined the %s family.";
    private static final String FAMILY_JOINED_AR = "تهانينا، لقد انضممت بنجاح إلى عائلة %s.";
    public static final String ACTION = "View details";
    public static final String ACTION_AR = "عرض التفاصيل";

    // 进程统计触发，给所有管理员发
    private static final String FAMILY_REQUEST_NOTIFICATION = "Family application notice";
    private static final String FAMILY_REQUEST_NOTIFICATION_AR = "إشعار طلب الانضمام للعائلة";
    private static final String FAMILY_REQUEST_BODY = "There are currently %s people who want to join the family, go check it out";
    private static final String FAMILY_REQUEST_BODY_AR = "هناك حاليًا %s من الأشخاص يرغبون في الانضمام إلى العائلة، تحقق من ذلك.";

    // 审批通过，给所有管理员发
    private static final String FAMILY_ADD_NOTIFICATION_ADMIN = "Notification of adding new family members";
    private static final String FAMILY_ADD_NOTIFICATION_ADMIN_AR = "إشعار بإضافة أعضاء جدد للعائلة";
    private static final String FAMILY_JOINED_ADMIN = "Congratulations to %s for successfully joining the family";
    private static final String FAMILY_JOINED_ADMIN_AR = "تهانينا لـ %s على الانضمام الناجح إلى العائلة";

    // 成员主动退出，给所有管理员发
    private static final String FAMILY_EXIT_NOTIFICATION_ADMIN = "Member withdrawal notice";
    private static final String FAMILY_EXIT_NOTIFICATION_ADMIN_AR = "إشعار بانسحاب عضو";
    private static final String FAMILY_EXIT_ADMIN = "%s has left the family";
    private static final String FAMILY_EXIT_ADMIN_AR = "%s قد غادر العائلة";

    // 管理员踢出成员，给所有管理员发
    private static final String FAMILY_ADMIN_EXIT_NOTIFICATION_ADMIN = "Member kick notification";
    private static final String FAMILY_ADMIN_EXIT_NOTIFICATION_ADMIN_AR = "إشعار بطرد عضو";
    private static final String FAMILY_ADMIN_EXIT_ADMIN = "%s has kicked %s out of the family";
    private static final String FAMILY_ADMIN_EXIT_ADMIN_AR = "%s قام بطرد %s من العائلة";

    // 设置管理员，给被设置的管理员发
    private static final String SET_ADMIN_NOTIFICATION = "Family Admin Notification";
    private static final String SET_ADMIN_NOTIFICATION_AR = "إشعار ادمن العائلة";
    private static final String SET_ADMIN_BODY = "You have become a family administrator and can operate family members to join or exit.";
    private static final String SET_ADMIN_BODY_AR = "لقد أصبحت ادمن العائلة، ويمكنك الآن إدارة انضمام الأعضاء إليها أو خروجهم منها.";

    // 取消管理员，给被取消的管理员发
    private static final String CANCEL_ADMIN_NOTIFICATION = "家族管理员通知";
    private static final String CANCEL_ADMIN_NOTIFICATION_AR = "家族管理员通知";
    private static final String CANCEL_ADMIN_BODY = "已取消您的家族管理员身份";
    private static final String CANCEL_ADMIN_BODY_AR = "已取消您的家族管理员身份";

    // 管理员踢出成员，给成员发
    private static final String FAMILY_ADMIN_EXIT_MEMBER_TITLE = "Kick Family Notification";
    private static final String FAMILY_ADMIN_EXIT_MEMBER_TITLE_AR = "طرد إشعار الأسرة";
    private static final String FAMILY_ADMIN_EXIT_MEMBER = "%s kicks you out of the family.";
    private static final String FAMILY_ADMIN_EXIT_MEMBER_AR = " %s يطردك من العائلة.";

    private static final String NO_ANNOUNCE = "No family announcement yet";
    private static final String NO_ANNOUNCE_AR = "لا يوجد إعلان عائلي حتى الآن";

    @Resource
    private FamilyDao familyDao;
    @Resource
    private FamilyMemberDao familyMemberDao;
    @Resource
    private FamilyRequestDao familyRequestDao;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private ActorDao actorDao;
    @Resource
    private IDetectService detectService;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private GenFamilyRidRedis genFamilyRidRedis;
    @Resource
    private FamilyDevoteDao familyDevoteDao;
    @Resource
    private OfficialMsgService officialMsgService;
    @Resource
    private FamilyRedis familyRedis;
    @Resource(name = AsyncConfig.ASYNC_TASK)
    private Executor executor;
    @Resource
    private FamilyTaskRedis familyTaskRedis;
    @Resource
    private FamilyService familyService;

    @PostConstruct
    private void init() {
        if (ServerConfig.isProduct()) {
            FAMILY_DISMISS_SECONDS = (int) TimeUnit.HOURS.toSeconds(72);
            FAMILY_REQUEST_SECONDS = (int) TimeUnit.HOURS.toSeconds(48);
        }
    }

    @Transactional(value = DBMysqlBean.USTAR_TRANSACTION, rollbackFor = {Exception.class, RuntimeException.class})
    public FamilyUpdateVO createFamily(FamilyDTO.UpdateInfo dto) {
        String uid = dto.getUid();
        String name = dto.getName();
        String head = dto.getHead();
        if (ObjectUtils.isEmpty(uid) || ObjectUtils.isEmpty(name) || ObjectUtils.isEmpty(head)) {
            logger.error("uid:{} or name:{} or  head:{} is empty", uid, name, head);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        FamilyData myFamily = familyDao.selectByOwnerUid(uid);
        if (myFamily != null) {
            logger.info("uid:{} myFamily already exist familyId:{} ", uid, myFamily.getId());
            throw new CommonException(UserHttpCode.FAMILY_JOIN_ALREADY);
        }
        myFamily = new FamilyData();
        if (!ObjectUtils.isEmpty(dto.getName())) {
            if (!detectText(dto.getName(), dto.getUid())) {
                logger.info("detectText name is not safe  uid:{}  text:{}",
                        dto.getUid(), dto.getName());
                throw new CommonException(UserHttpCode.CODE_NOT_ALLOW);
            }
            myFamily.setName(dto.getName());
        }
        if (!ObjectUtils.isEmpty(dto.getAnnounce())) {
            if (!detectText(dto.getAnnounce(), dto.getUid())) {
                logger.info("detectText announce is not safe uid:{} text:{}",
                        dto.getUid(), dto.getAnnounce());
                throw new CommonException(UserHttpCode.CODE_NOT_ALLOW);
            }
            myFamily.setAnnounce(dto.getAnnounce());
        }

        int level = userLevelDao.getUserLevel(uid);
        if (level < 10) {
            logger.info("uid:{} level:{} no right to operate ", uid, level);
            throw new CommonException(HttpCode.AUTH_ERROR);
        }

        FamilyMemberData memberData = familyMemberDao.selectByUid(uid);
        if (memberData != null) {
            logger.info("uid:{} already in familyId:{} ", uid, memberData.getFamilyId());
            throw new CommonException(UserHttpCode.FAMILY_JOIN_ALREADY);
        }

        int rid = genFamilyRidRedis.getGenFamilyRid();
        if (rid == 0) {
            logger.error("gen rid error myUid:{}", uid);
            throw new CommonException(new HttpCode(1, "gen rid error"));
        }
        if (!head.startsWith("http")) {
            head = ImageUrlGenerator.createCdnUrl(head);
        } else if (!head.startsWith("https://imagecdn.qmovies.tv")) {
            head = CDNUtils.getHttpCdnUrl(head);
        }
        myFamily.setRid(rid);
        myFamily.setOwnerUid(uid);
        myFamily.setHead(head);
        myFamily.setStatus(1);
        myFamily.setDevote(0L);
        myFamily.setCtime(DateHelper.getNowSeconds());
        familyDao.insert(myFamily);
        FamilyMemberData data = new FamilyMemberData();
        data.setUid(uid);
        data.setFamilyId(myFamily.getId());
        data.setRole(FamilyConstant.FAMILY_ROLE_OWNER);
        data.setDevote(0L);
        data.setCtime(DateHelper.getNowSeconds());
        familyMemberDao.insert(data);
        asyncFamilyHead(myFamily.getId(), myFamily.getHead());
        return new FamilyUpdateVO(myFamily.getName(), ImageUrlGenerator.generateNormalUrl(myFamily.getHead()),
                myFamily.getAnnounce(), myFamily.getRid());
    }

    public FamilyListVO familySquare(FamilyDTO.Square dto) {
        FamilyListVO vo = new FamilyListVO();
        List<FamilyVO> list = new ArrayList<>();
        vo.setList(list);
        int page = dto.getPage() <= 0 ? 1 : dto.getPage();
        int pageSize = 10;
        List<FamilyVO> newList = getNew7FamilyList(page, pageSize);
        List<FamilyVO> rankList = getRank7FamilyList(page, pageSize);
        int maxSize = Math.max(newList.size(), rankList.size());
        for (int i = 0; i < maxSize; i++) {
            if (i < newList.size()) {
                list.add(newList.get(i));
            }
            if (i < rankList.size()) {
                list.add(rankList.get(i));
            }
        }
        FamilyMemberData memberData = familyMemberDao.selectByUid(dto.getUid());
        vo.setMyFamilyRid(memberData != null ? memberData.getFamilyId() : 0);
        if (memberData == null) {
            List<FamilyRequestData> requestList = familyRequestDao.getByUid(dto.getUid());
            Set<Integer> requestFamilySet = CollectionUtil.listToPropertySet(requestList, FamilyRequestData::getFamilyId);
            for (FamilyVO familyVO : list) {
                familyVO.setRequestStatus(requestFamilySet.contains(familyVO.getFamilyId()) ? 1 : 0);
            }
        }
        vo.setLastWeekTopFamily(familyService.getLastWeekTopFamily());
        return vo;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<FamilyVO> getLastWeekTopFamily() {
        List<FamilyVO> list = new ArrayList<>();
        List<FamilyDevoteDao.DevoteRanking> devoteRankingList = familyDevoteDao.getLastWeekTop30Family();
        for (FamilyDevoteDao.DevoteRanking item : devoteRankingList) {
            FamilyData familyData = familyDao.selectByIdFromCache(item.getFamilyId());
            if (null == familyData) {
                continue;
            }
            list.add(familyDataToVO(familyData));
        }
        return list;
    }

    private FamilyVO familyDataToVO(FamilyData familyData) {
        FamilyVO itemVO = new FamilyVO();
        itemVO.setFamilyId(familyData.getId());
        itemVO.setRid(familyData.getRid());
        itemVO.setMaxMembers(MAX_MEMBER);
        itemVO.setMembers(familyMemberDao.selectMemberCountFromCache(familyData.getId()));
        itemVO.setName(familyData.getName());
        itemVO.setHead(ImageUrlGenerator.generateNormalUrl(familyData.getHead()));
        itemVO.setAnnounce(familyData.getAnnounce());
        // todo, level, maxMembers
        return itemVO;
    }

    private List<FamilyVO> getNew7FamilyList(int page, int pageSize) {
        // Recommended：最近7天家族战力值降序排列，列表中间每间隔一条数据插入一个最近7天创建的家族（按创建时间倒序）
        List<FamilyVO> newList = new ArrayList<>();
        int startTime = (int) (DateHelper.getNowSeconds() - TimeUnit.HOURS.toSeconds(7));
        IPage<FamilyData> iPage = familyDao.selectValidList(page, pageSize, startTime);
        for (FamilyData familyData : iPage.getRecords()) {
            newList.add(familyDataToVO(familyData));
        }
        return newList;
    }

    private List<FamilyVO> getRank7FamilyList(int page, int pageSize) {
        List<FamilyVO> rankList = new ArrayList<>();
        LocalDate today = LocalDate.now(ZoneId.of("+3"));
        LocalDate past7Day = today.minusDays(7);
        List<FamilyDevoteDao.DevoteRanking> rankingList = familyDevoteDao.getFamilyDevoteRanking(today.toString(), past7Day.toString());
        PageUtils.PageData<FamilyDevoteDao.DevoteRanking> pageData = PageUtils.getPageData(rankingList, page, pageSize);
        for (FamilyDevoteDao.DevoteRanking ranking : pageData.list) {
            FamilyData familyData = familyDao.selectByIdFromCache(ranking.getFamilyId());
            if (null == familyData) {
                continue;
            }
            rankList.add(familyDataToVO(familyData));
        }
        return rankList;
    }

    public FamilyUpdateVO updateInfo(FamilyDTO.UpdateInfo dto) {
        FamilyData familyData = familyDao.selectByFamilyRid(dto.getFamilyRid());
        roleAdminCheck(familyData.getId(), dto.getUid());
        if (!ObjectUtils.isEmpty(dto.getHead())) {
            String head = dto.getHead();
            if (!head.startsWith("http")) {
                String newHead = ImageUrlGenerator.createCdnUrl(head);
                familyData.setHead(newHead);
            } else if (!head.startsWith("https://imagecdn.qmovies.tv")) {
                familyData.setHead(CDNUtils.getHttpCdnUrl(head));
            }
            asyncFamilyHead(familyData.getId(), familyData.getHead());
        }
        if (!ObjectUtils.isEmpty(dto.getName())) {
            if (!detectText(dto.getName(), dto.getUid())) {
                logger.info("detectText name is not safe  familyId:{} uid:{}  text:{}",
                        familyData.getId(), dto.getUid(), dto.getName());
                throw new CommonException(UserHttpCode.CODE_NOT_ALLOW);
            }
            familyData.setName(dto.getName());
        }
        if (!ObjectUtils.isEmpty(dto.getAnnounce())) {
            if (!detectText(dto.getAnnounce(), dto.getUid())) {
                logger.info("detectText announce is not safe  familyId:{} uid:{}  text:{}",
                        familyData.getId(), dto.getUid(), dto.getAnnounce());
                throw new CommonException(UserHttpCode.CODE_NOT_ALLOW);
            }
            familyData.setAnnounce(dto.getAnnounce());
        }
        familyDao.updateById(familyData);
        return new FamilyUpdateVO(familyData.getName(), ImageUrlGenerator.generateNormalUrl(familyData.getHead()),
                familyData.getAnnounce(), familyData.getRid());
    }

    public FamilyMembersPageVO getFamilyMembers(FamilyDTO.Members dto) {
        FamilyMembersPageVO vo = new FamilyMembersPageVO(new ArrayList<>(PAGE_SIZE));
        FamilyData familyData = familyDao.selectByFamilyRid(dto.getFamilyRid());
        if (familyData == null) {
            logger.error("familyData is null familyRid:{}", dto.getFamilyRid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        String aid = null;
        if (!ObjectUtils.isEmpty(dto.getSearchKey())) {
            try {
                aid = actorDao.getActorByStrRid(dto.getSearchKey()).getUid();
            } catch (Exception ignored) {
                FamilyMemberData familyMemberData = familyMemberDao.selectByFamilyIdAndUid(familyData.getId(), dto.getUid());
                if (null != familyMemberData) {
                    vo.setRole(familyMemberData.getRole());
                }
                return vo;
            }
        }
        List<FamilyMemberData> pageList = familyMemberDao.selectMemberPageList(familyData.getId(), aid, dto.getPage(), PAGE_SIZE, dto.getMembersSortBy());
        for (FamilyMemberData familyMemberData : pageList) {
            FamilyMembersVO memberVO = new FamilyMembersVO();
            memberVO.setAid(familyMemberData.getUid());
            RoomActorDetailData detailData = roomActorCache.getData(null, familyMemberData.getUid(), false);
            memberVO.setName(detailData.getName());
            memberVO.setHead(detailData.getHead());
            memberVO.setVipLevel(detailData.getVipLevel());
            memberVO.setMicFrame(detailData.getMicFrame());
            memberVO.setGender(detailData.getGender());
            memberVO.setAge(detailData.getAge());
            memberVO.setBadgeList(detailData.getBadgeList());
            memberVO.setRole(familyMemberData.getRole());
            memberVO.setJoinTime(familyMemberData.getCtime());
            memberVO.setDevote(MatchUtils.formatDevotes(familyMemberData.getDevote()));
            memberVO.setuLevel(detailData.getLevel());
            vo.getList().add(memberVO);
        }
        vo.setNextUrl(pageList.size() < PAGE_SIZE ? "" : String.valueOf(dto.getPage() + 1));
        FamilyMemberData familyMemberData = familyMemberDao.selectByFamilyIdAndUid(familyData.getId(), dto.getUid());
        if (null != familyMemberData) {
            vo.setRole(familyMemberData.getRole());
        }
        return vo;
    }

    public ManageInfoVO manageInfo(FamilyDTO dto) {
        FamilyMemberData familyMemberData = familyMemberDao.selectByUid(dto.getUid());
        FamilyData familyData = familyDao.selectByFamilyRid(dto.getFamilyRid());
        if (familyMemberData == null || familyData == null) {
            logger.info("family is null familyRid:{} uid:{} ",
                    dto.getFamilyRid(), dto.getUid());
            throw new CommonException(HttpCode.AUTH_ERROR);
        }
//        roleAdminCheck(familyData.getId(), dto.getUid());
        ManageInfoVO vo = new ManageInfoVO();
        vo.setRid(familyData.getRid());
        vo.setName(familyData.getName());
        vo.setAnnounce(familyData.getAnnounce());
        vo.setHead(ImageUrlGenerator.generateNormalUrl(familyData.getHead()));
        vo.setMembers(familyMemberDao.selectMemberCount(familyData.getId()));
        vo.setMaxMember(MAX_MEMBER);
        vo.setRole(familyMemberData.getRole());
        int now = DateHelper.getNowSeconds();
        int cTime;
        if (familyMemberData.isOwner()) {
            cTime = familyData.getCtime();
            int toTime = familyRedis.getFamilyDismissScore(familyData.getId());
            if (toTime > 0) {
                int d = toTime - now;
                int H = d > 0 ? d / 3600 : 0;
                vo.setDismissFamilyH(H);
                vo.setFamilyStatus(1);
            } else {
                vo.setFamilyStatus(0);
            }
        } else {
            cTime = familyMemberData.getCtime();
        }
        vo.setCreateTime(cTime);
        vo.setCreateDays((now - cTime) / 86400);

        return vo;
    }

    public FamilyData getFamilyData(Integer familyRid) {
        if (null == familyRid) {
            throw new CommonException(UserHttpCode.PARAM_ERROR);
        }
        FamilyData familyData = familyDao.selectByFamilyRid(familyRid);
        if (familyData == null) {
            throw new CommonException(UserHttpCode.FAMILY_NOT_EXIST);
        }
        return familyData;
    }

    public FamilyUniVO.FamilyTaskVO familyCheck(String uid) {
        int familyId = familyMemberDao.getAllMemberFromCache().getOrDefault(uid, 0);
        if (familyId == 0) {
            throw new CommonException(UserHttpCode.PARAM_ERROR);
        }
        familyTaskRedis.familyCheck(uid);
        return familyTask(uid);
    }

    public FamilyUniVO.FamilyHomeVO familyHome(int familyRid, String uid) {
        FamilyUniVO.FamilyHomeVO vo = new FamilyUniVO.FamilyHomeVO();
        FamilyData familyData = getFamilyData(familyRid);
        // 公会详情
        vo.setRid(familyData.getRid());
        vo.setName(familyData.getName());
        vo.setAnnounce(familyData.getAnnounce());
        vo.setHead(ImageUrlGenerator.generateNormalUrl(familyData.getHead()));
        FamilyDevoteDao.HighestRanking familyHighestRanking = familyDevoteDao.getFamilyHighestRanking(familyData.getId());
        vo.setRank(familyHighestRanking.getRank());
        vo.setRankType(familyHighestRanking.getRankType());
        IPage<FamilyMemberData> memberDataIPage = familyMemberDao.selectMemberPageList(familyData.getId());
        vo.setMaxMember(MAX_MEMBER);
        vo.setCurMember((int) memberDataIPage.getTotal());
        FamilyMemberData familyMemberData = familyMemberDao.selectByUid(uid);
        if (null != familyMemberData) {
            if (familyData.getId().equals(familyMemberData.getFamilyId())) {
                vo.setMemberStatus(-2);
                if (familyMemberData.isAdmin()) {
                    vo.setIsAdmin(1);
                    vo.setIsOwner(familyMemberData.isOwner() ? 1 : 0);
                }
            } else {
                vo.setMemberStatus(-1);
            }
        } else {
            FamilyRequestData requestData = familyRequestDao.getByUidAndFamilyId(uid, familyData.getId());
            if (null != requestData && requestData.getStatus() == 1) {
                vo.setMemberStatus(familyRid);
            }
        }
        // 本月最后一秒
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime lastDayOfMonth = now.withDayOfMonth(now.toLocalDate().lengthOfMonth());
        LocalDateTime lastSecondOfMonth = lastDayOfMonth.withHour(23).withMinute(59).withSecond(59);
        ZonedDateTime zonedLastSecondUTC3 = ZonedDateTime.of(lastSecondOfMonth, ZoneId.of("UTC+3"));
        vo.setMonthEnd(zonedLastSecondUTC3.toInstant().getEpochSecond());
        return vo;
    }

    public FamilyUniVO.FamilyTaskVO familyTask(String uid) {
        FamilyUniVO.FamilyTaskVO vo = new FamilyUniVO.FamilyTaskVO();
        FamilyMemberData memberData = familyMemberDao.selectByUid(uid);
        if (null == memberData) {
            return vo;
        }
        vo.setAccumulation(memberData.getDevote());
        String today = DateHelper.ARABIAN.formatDateInDay();
        vo.setEarnToday(familyDevoteDao.getMemberDevote(uid, memberData.getFamilyId(), today, today));
        vo.setTaskList(familyTaskRedis.getFamilyTaskProcess(memberData.getFamilyId(), uid));
        return vo;
    }

    public FamilyUniVO.FamilyProfileVO familyProfile(Integer familyRid) {
        FamilyUniVO.FamilyProfileVO vo = new FamilyUniVO.FamilyProfileVO();
        FamilyData familyData = getFamilyData(familyRid);
        vo.setRanking(familyDevoteDao.getMemberHighestRanking(familyData.getId()));
        IPage<FamilyMemberData> memberDataIPage = familyMemberDao.selectMemberPageList(familyData.getId());
        for (FamilyMemberData memberData : memberDataIPage.getRecords()) {
            vo.getMembers().getMembers().add(getMemberDetail(memberData.getUid(), memberData.getRole()));
        }
        return vo;
    }

    private FamilyUniVO.MemberDetailVO getMemberDetail(String aid, int role) {
        FamilyUniVO.MemberDetailVO detailVO = new FamilyUniVO.MemberDetailVO();
        detailVO.setAid(aid);
        ActorData actorData = actorDao.getActorDataFromCache(aid);
        if (null == actorData) {
            return null;
        }
        detailVO.setName(actorData.getName());
        detailVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        detailVO.setRole(role);
        return detailVO;
    }

    public Object familyRequest(FamilyDTO.Request dto) {
        ActorData actorData = actorDao.getActorDataFromCache(dto.getUid());
        if (null == actorData) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        FamilyData familyData = familyDao.selectByFamilyRid(dto.getFamilyRid());
        if (null == familyData || 2 == familyData.getStatus()) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        if (familyRequestDao.getLast24hRejectCount(dto.getUid(), familyData.getId()) > 0) {
            throw new CommonException(UserHttpCode.FAMILY_DO_NOT_REAPPLY);
        }
        if (familyRedis.getUidKickOutTime(dto.getUid(), familyData.getId()) > 0) {
            throw new CommonException(UserHttpCode.FAMILY_DO_NOT_KICK);
        }
        int memberCount = familyMemberDao.selectMemberCount(familyData.getId());
        if (memberCount >= MAX_MEMBER) {
            throw new CommonException(UserHttpCode.FAMILY_FULL);
        }
        FamilyMemberData data = familyMemberDao.selectByUid(dto.getUid());
        if (data != null) {
            throw new CommonException(UserHttpCode.FAMILY_ONLY_JOIN_ONE);
        }
        if (StringUtils.hasLength(dto.getContent())) {
            if (!detectText(dto.getContent(), dto.getUid())) {
                logger.info("detectText content is not safe  uid:{} content:{}",
                        dto.getUid(), dto.getContent());
                throw new CommonException(UserHttpCode.CONTENT_NOT_ALLOW);
            }
        }
        if (familyRequestDao.getRequestCount(dto.getUid()) >= 20) {
            // todo 用户同时申请加入家族的数量最多不可超过20个家族。超过20个，点击按钮toast提示“申请的家族超过限制”。
            throw new CommonException(UserHttpCode.FAMILY_FULL);
        }
        FamilyRequestData familyRequestData = familyRequestDao.getByUidAndFamilyId(dto.getUid(), familyData.getId());
        if (familyRequestData == null) {
            familyRequestData = new FamilyRequestData();
        }
        familyRequestData.setUid(dto.getUid());
        familyRequestData.setUid(dto.getAid());
        familyRequestData.setFamilyId(familyData.getId());
        familyRequestData.setContent(dto.getContent() == null ? "" : dto.getContent());
        familyRequestData.setStatus(1);
        familyRequestData.setCtime(DateHelper.getNowSeconds());
        familyRequestData.setMtime(familyRequestData.getCtime());
        if (null == familyRequestData.getId()) {
            familyRequestDao.insertOne(familyRequestData);
        } else {
            familyRequestDao.updateOne(familyRequestData);
        }
        return null;
    }

    public FamilyRequestPageVO familyRequestList(FamilyDTO.RequestList dto) {
        FamilyData familyData = familyDao.selectByFamilyRid(dto.getFamilyRid());
        roleAdminCheck(familyData.getId(), dto.getUid());
        List<FamilyRequestData> pageList = familyRequestDao.selectRequestPageList(familyData.getId(), dto.getPage(), PAGE_SIZE);
        FamilyRequestPageVO vo = new FamilyRequestPageVO(new ArrayList<>(PAGE_SIZE));
        for (FamilyRequestData familyRequestData : pageList) {
            FamilyRequestVO requestVO = new FamilyRequestVO();
            RoomActorDetailData detailData = roomActorCache.getData(null, familyRequestData.getUid(), false);
            requestVO.setReqId(familyRequestData.getId());
            requestVO.setAid(detailData.getAid());
            requestVO.setName(detailData.getName());
            requestVO.setVipLevel(detailData.getVipLevel());
            requestVO.setuLevel(detailData.getLevel());
            requestVO.setHead(detailData.getHead());
            requestVO.setMicFrame(detailData.getMicFrame());
            requestVO.setGender(detailData.getGender());
            requestVO.setAge(detailData.getAge());
            requestVO.setContent(familyRequestData.getContent());
            requestVO.setBadgeList(detailData.getBadgeList());
            requestVO.setStatus(familyRequestData.getStatus());
            requestVO.setCtime(familyRequestData.getCtime());
            vo.getList().add(requestVO);
        }
        vo.setNextUrl(pageList.size() < PAGE_SIZE ? "" : String.valueOf(dto.getPage() + 1));
        if (dto.getPage() == 1) {
            // 清除未读数
            familyRequestDao.cleanUnread(familyData.getId());
        }
        // ???
//        if (dto.getPage() == 1) {
//            executor.execute(() -> {
//                checkNoticeNew(dto.getUid(), familyData.getId(), 1);
//            });
//        }
        return vo;
    }

    @Transactional(value = DBMysqlBean.USTAR_TRANSACTION)
    public Object familyApproval(FamilyDTO.Approval dto) {
        // noinspection UnstableApiUsage
        synchronized (stringPool.intern("lock:" + dto.getReqId())) {
            return doFamilyApproval(dto);
        }
    }

    public Object doFamilyApproval(FamilyDTO.Approval dto) {
        if (3 == dto.getApprovalType()) {
            if (null != dto.getReqId()) {
                // 管理员删除申请
                FamilyRequestData requestData = familyRequestDao.getById(dto.getReqId());
                if (null == requestData) {
                    throw new CommonException(HttpCode.PARAM_ERROR);
                }
                roleAdminCheck(requestData.getFamilyId(), dto.getUid());
                familyRequestDao.deleteById(dto.getReqId());
            } else {
                // 用户主动撤销申请
                FamilyData familyData = familyDao.selectByFamilyRid(dto.getFamilyRid());
                if (null == familyData) {
                    throw new CommonException(HttpCode.PARAM_ERROR);
                }
                familyRequestDao.recallRequest(dto.getUid(), familyData.getId());
            }
        } else {
            FamilyRequestData requestData = familyRequestDao.getById(dto.getReqId());
            if (null == requestData) {
                throw new CommonException(HttpCode.PARAM_ERROR);
            }
            if (requestData.getStatus() != 0 && requestData.getStatus() != 1) {
                // 该申请已被处理
                throw new CommonException(HttpCode.AUTH_ERROR);
            }
            roleAdminCheck(requestData.getFamilyId(), dto.getUid());
            FamilyData familyData = familyDao.selectByFamilyRid(dto.getFamilyRid());
            FamilyMemberData data = familyMemberDao.selectByUid(requestData.getUid());
            if (1 == dto.getApprovalType()) {
                if (data == null) {
                    int memberCount = familyMemberDao.selectMemberCount(familyData.getId());
                    if (memberCount >= MAX_MEMBER) {
                        throw new CommonException(UserHttpCode.FAMILY_FULL);
                    }
                    data = new FamilyMemberData();
                    data.setUid(requestData.getUid());
                    data.setFamilyId(familyData.getId());
                    data.setRole(FamilyConstant.FAMILY_ROLE_MEMBER);
                    data.setDevote(0L);
                    data.setCtime(DateHelper.getNowSeconds());
                    familyMemberDao.insert(data);
                    executor.execute(() -> {
                        ActorData actorData = actorDao.getActorDataFromCache(requestData.getUid());
                        String title = SLangType.ENGLISH == actorData.getSlang() ? FAMILY_ADD_NOTIFICATION : FAMILY_ADD_NOTIFICATION_AR;
                        String act = SLangType.ENGLISH == actorData.getSlang() ? ACTION : ACTION_AR;
                        String body = String.format((SLangType.ENGLISH == actorData.getSlang() ? FAMILY_JOINED : FAMILY_JOINED_AR), familyData.getName());
                        sendOfficialData(requestData.getUid(), title, body, act, familyData.getRid(), FAMILY_HOME_PAGE);

                        List<FamilyMemberData> adminList = familyMemberDao.selectAdminMemberList(familyData.getId());
                        for (FamilyMemberData item : adminList) {
                            ActorData adminData = actorDao.getActorDataFromCache(item.getUid());
                            title = SLangType.ENGLISH == adminData.getSlang() ? FAMILY_ADD_NOTIFICATION_ADMIN : FAMILY_ADD_NOTIFICATION_ADMIN_AR;
                            act = SLangType.ENGLISH == adminData.getSlang() ? ACTION : ACTION_AR;
                            body = String.format((SLangType.ENGLISH == adminData.getSlang() ? FAMILY_JOINED_ADMIN : FAMILY_JOINED_ADMIN_AR), actorData.getName());
                            sendOfficialData(item.getUid(), title, body, act, familyData.getRid(), FAMILY_HOME_PAGE);
                        }
                        // 邀请任务
                        if (!ObjectUtils.isEmpty(requestData.getAid())) {
                            ActorData newMember = actorDao.getActorDataFromCache(requestData.getUid());
                            ActorData inviter = actorDao.getActorDataFromCache(requestData.getAid());
                            if (null != newMember && null != inviter) {
                                String deviceId = null == newMember.getTn_id() ? null : newMember.getTn_id();
                                familyTaskRedis.inviteFamilyMember(requestData.getAid(), requestData.getUid(), deviceId);
                            }
                        }
                    });
                    familyRequestDao.changeStatus(dto.getReqId(), 2);
                } else {
                    if (data.getFamilyId().equals(familyData.getId())) {
                        familyRequestDao.changeStatus(dto.getReqId(), 2);
                        throw new CommonException(UserHttpCode.FAMILY_JOIN_MY);
                    } else {
                        // 批准加入规则：用户同时申请多个家族时，按审核优先级加入家族，加入最早加入的家族。加入其他家族后，后面审核的家族点击同意按钮toast提示：“用户已加入其他家族”按钮变成“拒绝状态”
                        familyRequestDao.changeStatus(dto.getReqId(), 3);
                        throw new CommonException(UserHttpCode.FAMILY_JOIN_OTHER);
                    }
                }
            } else {
                if (data == null) {
                    familyRequestDao.changeStatus(dto.getReqId(), 3);
                } else {
                    if (data.getFamilyId().equals(familyData.getId())) {
                        familyRequestDao.changeStatus(dto.getReqId(), 2);
                        throw new CommonException(UserHttpCode.FAMILY_JOIN_MY);
                    } else {
                        familyRequestDao.deleteById(dto.getReqId());
                        throw new CommonException(UserHttpCode.FAMILY_JOIN_OTHER);
                    }
                }

            }
        }
        return null;
    }


    public Object familyMemberOperate(FamilyDTO.Operate dto) {
        FamilyData familyData = familyDao.selectByFamilyRid(dto.getFamilyRid());
        FamilyMemberData familyMemberData = familyMemberDao.selectByUid(dto.getAid());
        if (dto.getUid().equals(dto.getAid()) || null == familyMemberData || !familyData.getId().equals(familyMemberData.getFamilyId())) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        if (1 == dto.getOperateType() || 2 == dto.getOperateType()) {
            ownerCheck(familyData, dto);
            if (dto.getOperateType() == 1) {
                int memberCount = familyMemberDao.selectAdminMemberCount(familyData.getId());
                if (memberCount >= MAX_ADMIN) {
                    throw new CommonException(UserHttpCode.FAMILY_ADMIN_LIMIT);
                }
                executor.execute(() -> {
                    ActorData actorData = actorDao.getActorDataFromCache(dto.getAid());
                    String title = SLangType.ENGLISH == actorData.getSlang() ? SET_ADMIN_NOTIFICATION : SET_ADMIN_NOTIFICATION_AR;
                    String body = SLangType.ENGLISH == actorData.getSlang() ? SET_ADMIN_BODY : SET_ADMIN_BODY_AR;
                    String act = SLangType.ENGLISH == actorData.getSlang() ? ACTION : ACTION_AR;
                    sendOfficialData(actorData.getUid(), title, body, act, familyData.getRid(), FAMILY_HOME_PAGE);

                });
            }
            familyMemberDao.changeMemberRole(dto.getAid(), dto.getOperateType() == 1 ? FamilyConstant.FAMILY_ROLE_ADMIN : FamilyConstant.FAMILY_ROLE_MEMBER);
        } else if (3 == dto.getOperateType()) {
            if (familyMemberData.isAdmin()) {
                ownerCheck(familyData, dto);
            } else {
                roleAdminCheck(familyData.getId(), dto.getUid());
            }
            familyMemberDao.deleteByFamilyIdAndUid(familyData.getId(), dto.getAid());
            familyRequestDao.deleteByFamilyIdAndUid(familyData.getId(), dto.getAid());
            familyRedis.addUidKickOutTime(dto.getAid(), familyData.getId(), DateHelper.getNowSeconds());
            executor.execute(() -> {
                familyDevoteDao.cleanUserDevote(dto.getAid(), familyData.getId());
            });
            executor.execute(() -> {
                ActorData opActorData = actorDao.getActorDataFromCache(dto.getUid());
                ActorData actorData = actorDao.getActorDataFromCache(dto.getAid());
                List<FamilyMemberData> adminList = familyMemberDao.selectAdminMemberList(familyData.getId());
                for (FamilyMemberData item : adminList) {
                    ActorData adminData = actorDao.getActorDataFromCache(item.getUid());
                    String title = SLangType.ENGLISH == adminData.getSlang() ? FAMILY_ADMIN_EXIT_NOTIFICATION_ADMIN : FAMILY_ADMIN_EXIT_NOTIFICATION_ADMIN_AR;
                    String body = String.format((SLangType.ENGLISH == adminData.getSlang() ? FAMILY_ADMIN_EXIT_ADMIN : FAMILY_ADMIN_EXIT_ADMIN_AR)
                            , opActorData.getName(), actorData.getName());
                    sendOfficialData(item.getUid(), title, body, null, null, 0);
                }

                String title = SLangType.ENGLISH == actorData.getSlang() ? FAMILY_ADMIN_EXIT_MEMBER_TITLE : FAMILY_ADMIN_EXIT_MEMBER_TITLE_AR;
                String body = String.format((SLangType.ENGLISH == actorData.getSlang() ? FAMILY_ADMIN_EXIT_MEMBER : FAMILY_ADMIN_EXIT_MEMBER_AR)
                        , opActorData.getName());
                sendOfficialData(dto.getAid(), title, body, null, null, 0);
            });
        }
        return null;
    }

    @Transactional(value = DBMysqlBean.USTAR_TRANSACTION)
    public ManageInfoVO familyQuit(FamilyDTO.Quit dto) {
        FamilyMemberData data = familyMemberDao.selectByUid(dto.getUid());
        if (data == null) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        int operateType = dto.getOperateType();
        ManageInfoVO vo = new ManageInfoVO();
        vo.setRole(data.getRole());
        if (data.getRole() == FamilyConstant.FAMILY_ROLE_OWNER) {
            if (operateType == 1) {
                int now = DateHelper.getNowSeconds();
                int toTime = now + FAMILY_DISMISS_SECONDS;
                familyRedis.addFamilyDismiss(data.getFamilyId(), toTime);
                int d = toTime - now;
                int H = d > 0 ? d / 3600 : 0;
                vo.setDismissFamilyH(H);
                vo.setFamilyStatus(1);
            } else {
                familyRedis.cancelFamilyDismiss(data.getFamilyId());
                vo.setFamilyStatus(0);
            }
        } else {
            familyMemberDao.deleteByFamilyIdAndUid(data.getFamilyId(), dto.getUid());
            familyRequestDao.deleteByFamilyIdAndUid(data.getFamilyId(), dto.getUid());
            executor.execute(() -> {
                familyDevoteDao.cleanUserDevote(dto.getUid(), data.getFamilyId());
            });
            executor.execute(() -> {
                ActorData actorData = actorDao.getActorDataFromCache(dto.getUid());
                List<FamilyMemberData> adminList = familyMemberDao.selectAdminMemberList(data.getFamilyId());
                for (FamilyMemberData item : adminList) {
                    ActorData adminData = actorDao.getActorDataFromCache(item.getUid());
                    String title = SLangType.ENGLISH == adminData.getSlang() ? FAMILY_EXIT_NOTIFICATION_ADMIN : FAMILY_EXIT_NOTIFICATION_ADMIN_AR;
                    String body = String.format((SLangType.ENGLISH == adminData.getSlang() ? FAMILY_EXIT_ADMIN : FAMILY_EXIT_ADMIN_AR), actorData.getName());
                    sendOfficialData(item.getUid(), title, body, null, null, 0);

                }
            });
            logger.info("delete family member done familyId:{} uid:{} ", data.getFamilyId(), dto.getUid());
        }
        return vo;
    }

    @Transactional(value = DBMysqlBean.USTAR_TRANSACTION)
    public void familyDismiss() {
        int now = DateHelper.getNowSeconds();
        Map<String, Integer> dismissMap = familyRedis.getFamilyDismissMapByScore(now);
        for (Map.Entry<String, Integer> entry : dismissMap.entrySet()) {
            Integer familyId = Integer.parseInt(entry.getKey());
            List<String> uidList = familyMemberDao.selectMemberUidList(familyId);
            int delMemberCount = familyMemberDao.deleteByFamilyId(familyId);
            int requestCount = familyRequestDao.deleteByFamilyId(familyId);
            for (String aid : uidList) {
                actorDao.updateField(aid, "familyId", 0);
            }
            familyDao.deleteByFamilyId(familyId);
            familyRedis.cancelFamilyDismiss(familyId);
            executor.execute(() -> {
                familyDevoteDao.cleanFamilyDevote(familyId);
            });
            logger.info("delete family done familyId:{} memberCount:{} delMemberCount:{} requestCount:{}",
                    familyId, uidList.size(), delMemberCount, requestCount);
        }
    }

    public void requestListExpire() {
        int time = DateHelper.getNowSeconds() - FAMILY_REQUEST_SECONDS;
        List<FamilyRequestData> allList = familyRequestDao.selectRequestListByCtime(time);
        for (FamilyRequestData item : allList) {
            familyRequestDao.changeStatus(item.getId(), 3);
            logger.info("expire request uid:{} familyId:{} reqId:{}", item.getUid(), item.getFamilyId(), item.getId());
        }
    }

    public void familyRequestNewNotice() {
        int start = DateHelper.getNowSeconds() - 30 * 60;
        List<CountData> countDataList = familyRequestDao.familyNewAdd(start);
        for (CountData item : countDataList) {
            Integer familyId = Integer.parseInt(item.getMyKey());
            long count = item.getCount();
            List<FamilyMemberData> memberDataList = familyMemberDao.selectAdminMemberList(familyId);
            if (count > 0) {
                for (FamilyMemberData data : memberDataList) {
                    ActorData actorData = actorDao.getActorDataFromCache(data.getUid());
                    String title = SLangType.ENGLISH == actorData.getSlang() ? FAMILY_REQUEST_NOTIFICATION : FAMILY_REQUEST_NOTIFICATION_AR;
                    String act = SLangType.ENGLISH == actorData.getSlang() ? ACTION : ACTION_AR;
                    String body = String.format(SLangType.ENGLISH == actorData.getSlang() ? FAMILY_REQUEST_BODY : FAMILY_REQUEST_BODY_AR, count);
                    FamilyData familyData = familyDao.selectById(familyId);
                    sendOfficialData(data.getUid(), title, body, act, familyData.getRid(), FAMILY_REQUEST_PAGE);
                    logger.info("familyRequestNewAdd push familyId:{} familyRid:{} uid:{} count:{}",
                            familyId, familyData.getRid(), data.getUid(), count);
                }
            }
        }
    }

    private int getFamilyUnread(Integer id) {
        return familyRequestDao.getUnprocessedCount(id);
    }

    private void asyncFamilyHead(int familyId, String newHead) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                if (!detectImage(newHead, familyId)) {
                    familyDao.updateDetailHead(familyId);
                    logger.info("detectImage is not safe change to default familyId:{} type_name:房间头像  url:{}", familyId, newHead);
                }
            }
        });
    }

    private boolean detectImage(String url, int familyId) {
        try {
//            String origin = "家族头像" + "_" + familyId;
            if (detectService.detectImage(new ImageDTO(url, DetectOriginConstant.FAMILY_PICTURE)).getData().getIsSafe() == 1) {
                return true;
            }
        } catch (Exception e) {
            logger.error("detect image Exception url={} familyId={} typeName={}", url, familyId, "家族头像", e);
        }
        return false;
    }

    private boolean detectText(String text, String fromUid) {
        try {
            if (detectService.detectText(new TextDTO(text, DetectOriginConstant.FAMILY_RELATED, fromUid)).getData().getIsSafe() == 1) {
                return true;
            }
        } catch (Exception e) {
            logger.error("detect text Exception text={} fromUid={} ", text, fromUid, e);
        }
        return false;
    }

    /**
     * 校验是否是公会管理员
     *
     * @param id  公会id，主键id
     * @param uid 用户uid
     */
    private void roleAdminCheck(int id, String uid) {
        FamilyMemberData data = familyMemberDao.selectByUid(uid);
        if (null == data || data.getFamilyId() != id || !data.isAdmin()) {
            throw new CommonException(HttpCode.AUTH_ERROR);
        }
    }

    private void ownerCheck(FamilyData familyData, HttpEnvData envData) {
        if (!envData.getUid().equals(familyData.getOwnerUid())) {
            throw new CommonException(HttpCode.AUTH_ERROR);
        }
    }

    private int getMaxPages(int total, int pageSize) {
        if (pageSize == 0) {
            return 0;
        }
        int pages = total / pageSize;
        if (total % pageSize != 0) {
            pages++;
        }
        return pages;
    }


    private void sendOfficialData(String toUid, String title, String body, String act, Integer familyRid, int atype) {
        OfficialData officialData = new OfficialData();
        officialData.setTo_uid(toUid);
        officialData.setTitle(title);
        officialData.setBody(body);
        officialData.setValid(1);
        officialData.setAtype(atype);
        officialData.setNews_type(0);
        officialData.setCtime(DateHelper.getNowSeconds());
        if (StringUtils.hasLength(act)) {
            officialData.setAct(act);
        }
        // 借用roomId字段下发公会id
        if (familyRid != null) {
            officialData.setRoom_id(String.valueOf(familyRid));
        }
        officialMsgService.officialMsgPush(officialData);
    }

    private boolean checkNoticeNew(String uid, Integer familyId, int cmd) {
        FamilyRequestData familyRequestData = familyRequestDao.selectRequestLastCtime(familyId);
        if (familyRequestData != null) {
            int newTime = familyRequestData.getCtime();
            if (cmd == 1) {
                familyRedis.addFamilyNoticeLast(uid, familyId, newTime);
            } else {
                int oldTime = familyRedis.getFamilyNoticeLast(uid, familyId);
                return newTime > oldTime;
            }
        }
        return false;
    }
}
