package com.quhong.service;

import com.quhong.constant.StoreConstant;
import com.quhong.constant.UserHttpCode;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.data.dto.StoreDTO;
import com.quhong.data.dto.StoreGoodsDTO;
import com.quhong.data.vo.RoomLockVO;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.ApiResult;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.exception.CommonException;
import com.quhong.feign.DataCenterService;
import com.quhong.feign.DataResourcesService;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.RoomLockDao;
import com.quhong.mongo.dao.RoomLockSourceDao;
import com.quhong.mongo.data.RoomLockData;
import com.quhong.mongo.data.RoomLockSourceData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 房间锁
 *
 * <AUTHOR>
 * @date 2022/9/20
 */
@Service
public class RoomLockService {

    private static final Logger logger = LoggerFactory.getLogger(RoomLockService.class);

    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomLockSourceDao roomLockSourceDao;
    @Resource
    private RoomLockDao roomLockDao;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private DataResourcesService dataResourcesService;

    /**
     * 房间锁列表
     */
    public RoomLockVO getRoomLockList(StoreDTO req) {
        RoomLockVO vo = new RoomLockVO();
        ActorData actorData = actorDao.getActorData(req.getUid());
        if (actorData == null) {
            throw new CommonException(UserHttpCode.USER_NOT_EXIST);
        }
        List<RoomLockVO.RoomLock> list = new ArrayList<>();
        List<RoomLockSourceData> lockSourceList = roomLockSourceDao.findAll();
        if (!CollectionUtils.isEmpty(lockSourceList)) {
            for (RoomLockSourceData sourceData : lockSourceList) {
                RoomLockVO.RoomLock roomLock = new RoomLockVO.RoomLock();
                roomLock.setPid(sourceData.getPid());
                roomLock.setDays(sourceData.getVaild_days());
                roomLock.setDiamonds(sourceData.getDiamonds());
                list.add(roomLock);
            }
        }
        fillVailDays(vo, req.getUid());
        vo.setBalance(actorData.getBeans());
        vo.setList(list);
        return vo;
    }


    /**
     * 购买房间锁
     */
    public RoomLockVO buyRoomLock(StoreGoodsDTO req) {
        if (req.getPid() == null) {
            logger.info("pid is null. uid={} pid={}", req.getUid(), req.getPid());
            throw new CommonException(UserHttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorData(req.getUid());
        if (actorData == null) {
            logger.error("not find actor. uid={} pid={}", req.getUid(), req.getPid());
            throw new CommonException(UserHttpCode.USER_NOT_EXIST);
        }
        RoomLockSourceData sourceData = roomLockSourceDao.findDataByStatus(req.getPid(), 1);
        if (sourceData == null) {
            logger.error("not find room lock source. uid={} pid={}", req.getUid(), req.getPid());
            throw new CommonException(UserHttpCode.ROOM_LOCK_NOT_EXIST);
        } else {
            int costBeans = sourceData.getDiamonds();
            int validDays = sourceData.getVaild_days();
            if (validDays <= 0) {
                logger.info("validDays={} < 0. uid={} pid={}",
                        validDays, req.getUid(), req.getPid());
                throw new CommonException(UserHttpCode.ROOM_LOCK_BUY_FAILED);
            }
            if (actorData.getBeans() < costBeans) {
                logger.info("buy room lock not enough diamonds. uid={} pid={} now_beans={} costBeans={}",
                        req.getUid(), req.getPid(), actorData.getBeans(), costBeans);
                throw new CommonException(UserHttpCode.DIAMONDS_NOT_ENOUGH);
            }
            MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
            moneyDetailReq.setRandomId();
            moneyDetailReq.setUid(req.getUid());
            moneyDetailReq.setAtype(StoreConstant.BUY_ROOM_LOCK_TYPE);
            moneyDetailReq.setChanged(-costBeans);
            moneyDetailReq.setTitle(StoreConstant.BUY_ROOM_LOCK_TITLE);
            moneyDetailReq.setDesc(StoreConstant.BUY_ROOM_LOCK_TITLE);
            moneyDetailReq.setRoomId(req.getRoomId());
            ApiResult<String> reduceBeansResult = dataCenterService.reduceBeans(moneyDetailReq);
            if (reduceBeansResult.isOk()) {
                ResourcesDTO resourcesDTO = new ResourcesDTO();
                resourcesDTO.setUid(req.getUid());
                resourcesDTO.setResId(String.valueOf(req.getPid()));
                resourcesDTO.setResType(BaseDataResourcesConstant.TYPE_ROOM_LOCK);
                resourcesDTO.setDays(validDays);
                resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_GET_WEAR);
                resourcesDTO.setDesc(StoreConstant.BUY_ROOM_LOCK_TITLE);
                resourcesDTO.setmTime(DateHelper.getNowSeconds());
                resourcesDTO.setGainType(BaseDataResourcesConstant.GAIN_TYPE_DAY);
                resourcesDTO.setGetWay(BaseDataResourcesConstant.TYPE_BUY_GET);
                ApiResult<String> result = dataResourcesService.handleRes(resourcesDTO);
                if (result.isError()) {
                    logger.info("buy room lock handleRes failure. uid={} pid={} code={} msg={}",
                            req.getUid(), req.getPid(), result.getCode(), result.getCode().getMsg());
                    throw new CommonException(UserHttpCode.ROOM_LOCK_BUY_FAILED);
                }
                RoomLockVO vo = new RoomLockVO();
                fillVailDays(vo, req.getUid());
                ActorData beansActorData = actorDao.getActorData(req.getUid());
                vo.setBalance(beansActorData.getBeans());
                return vo;
            } else {
                logger.info("buy room lock failure. uid={} pid={} code={} msg={}",
                        req.getUid(), req.getPid(), reduceBeansResult.getCode(), reduceBeansResult.getCode().getMsg());
                if (reduceBeansResult.getCode().getCode() == StoreConstant.DIAMONDS_NOT_ENOUGH_CODE) {
                    throw new CommonException(UserHttpCode.NOT_ENOUGH_DIAMONDS);
                }
                throw new CommonException(UserHttpCode.ROOM_LOCK_BUY_FAILED);
            }
        }
    }

    private void fillVailDays(RoomLockVO vo, String uid) {
        RoomLockData roomLockData = roomLockDao.findData(uid);
        if (roomLockData != null) {
            int validTime = (int) (roomLockData.getEnd_time() - DateHelper.getNowSeconds());
            vo.setVaildDays(validTime > 0 ? validTime / (24 * 3600) : 0);
        } else {
            vo.setVaildDays(0);
        }
    }

}
