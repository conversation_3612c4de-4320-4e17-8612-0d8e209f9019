package com.quhong.service;

import com.quhong.constant.StoreConstant;
import com.quhong.constant.UserHttpCode;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.ResourceLabelConfig;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.data.dto.SetResourcesDTO;
import com.quhong.data.dto.StoreDTO;
import com.quhong.data.dto.StoreGoodsDTO;
import com.quhong.data.vo.GoodsListHomeVO;
import com.quhong.data.vo.impl.PyMyRippleItemVO;
import com.quhong.data.vo.impl.PyStoreRippleItemVO;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.ApiResult;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.feign.DataCenterService;
import com.quhong.feign.DataResourcesService;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.RippleDao;
import com.quhong.mongo.dao.RippleSourceDao;
import com.quhong.mongo.data.RippleData;
import com.quhong.mongo.data.RippleSourceData;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.redis.GoodsListHomeRedis;
import com.quhong.utils.AppVersionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/14
 */
@Service
public class RippleService extends AbstractStoreGoodsService {

    private static final Logger logger = LoggerFactory.getLogger(RippleService.class);

    @Resource
    private RippleSourceDao rippleSourceDao;
    @Resource
    private RippleDao rippleDao;
    @Resource
    private DataResourcesService dataResourcesService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private GoodsListHomeRedis goodsListHomeRedis;

    /**
     * 声波装饰列表
     */
    @Override
    public GoodsListHomeVO getStoreGoodsList(StoreDTO req) {
        GoodsListHomeVO vo = new GoodsListHomeVO();
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        if (actorData == null) {
            logger.error("can not find user data. uid={}", req.getUid());
            throw new CommonException(UserHttpCode.USER_NOT_EXIST);
        }
        int page = req.getPage() != null ? req.getPage() : 1;
        int size = 20;
        int start = (page - 1) * size;
        boolean isV839 = AppVersionUtils.versionCheck(839, req);

        List<GoodsListHomeVO.GoodsItemVO> list = new ArrayList<>();
        List<RippleSourceData> rippleSourcePage = rippleSourceDao.getRippleSourcePage(start, size);
        int qSize = rippleSourcePage.size();
        if (!CollectionUtils.isEmpty(rippleSourcePage)) {
            for (RippleSourceData sourceData : rippleSourcePage) {
                GoodsListHomeVO.GoodsItemVO rippleVO = isV839 ? new GoodsListHomeVO.GoodsItemVO() : new PyStoreRippleItemVO();
                rippleVO.setRes_id(sourceData.getRipple_id());
                rippleVO.setSource_name(req.getSlang() == SLangType.ARABIC ? sourceData.getName_ar() : sourceData.getName());
                rippleVO.setSource_icon(sourceData.getRipple_icon());
                rippleVO.setSource_url(sourceData.getRipple_sources());
                rippleVO.setBuy_type(sourceData.getBuy_type());
                rippleVO.setBeans(sourceData.getBeans());
                rippleVO.setDays(sourceData.getDays());
                list.add(rippleVO);
            }
        }
        vo.setList(list);
        vo.setNextUrl(qSize < size ? "" : String.valueOf(page + 1));
        return vo;
    }

    /**
     * 我的声波资源列表
     */
    @Override
    public GoodsListHomeVO getMyGoodsList(StoreDTO req) {
        GoodsListHomeVO vo = new GoodsListHomeVO();
        int page = req.getPage() != null ? req.getPage() : 1;
        int size = 20;
        int start = (page - 1) * size;
        boolean isV839 = AppVersionUtils.versionCheck(839, req);

        List<GoodsListHomeVO.GoodsItemVO> list = new ArrayList<>();
        if (page <= 1 && !isV839) {
            RippleSourceData defaultRipple = rippleSourceDao.findData(1);
            if (defaultRipple != null) {
                GoodsListHomeVO.GoodsItemVO rippleVO = new PyMyRippleItemVO();
                rippleVO.setSource_icon(defaultRipple.getRipple_icon());
                rippleVO.setRes_id(defaultRipple.getRipple_id());
                rippleVO.setEnd_days(-1);
                rippleVO.setSource_url(defaultRipple.getRipple_sources());
                RippleData rippleData = rippleDao.findDataByStatus(req.getUid(), 1);
                if (rippleData != null && rippleData.getRipple_id() != 1) {
                    rippleVO.setStates(0);
                } else {
                    rippleVO.setStates(1);
                }
                rippleVO.setSource_name("Default");
                rippleVO.setItem_type(0);
                list.add(rippleVO);
            }
        }
        List<RippleData> rippleDaoList = rippleDao.findList(req.getUid(), start, size);
        long currentTime = DateHelper.getNowSeconds();
        if (!CollectionUtils.isEmpty(rippleDaoList)) {
            for (RippleData rippleData : rippleDaoList) {
//                if (rippleData.getRipple_id() == 1) {
//                    continue;
//                }
                RippleSourceData data = rippleSourceDao.findData(rippleData.getRipple_id());
                if (data == null) {
                    logger.error("not find ripple source uid={} rippleId={}", req.getUid(), rippleData.getRipple_id());
                    continue;
                }
                GoodsListHomeVO.GoodsItemVO rippleVO = isV839 ? new GoodsListHomeVO.GoodsItemVO() : new PyMyRippleItemVO();
                rippleVO.setRes_id(rippleData.getRipple_id());
                rippleVO.setStates(rippleData.getStatus());
                rippleVO.setEnd_days(getEndDays(rippleData.getEnd_time()));
                long leftTime = rippleData.getEnd_time() - currentTime;
                if(leftTime < 0){
                    continue;
                }
                rippleVO.setLeftTime(leftTime);
                rippleVO.setSource_icon(data.getRipple_icon());
                rippleVO.setSource_url(data.getRipple_sources());
                rippleVO.setSource_name(req.getSlang() == SLangType.ARABIC ? data.getName_ar() : data.getName());
                rippleVO.setItem_type(data.getItem_type());
                ResourceLabelConfig labelConfig = LABEL_CONFIG_MAP.get(data.getItem_type());
                if (labelConfig != null) {
                    rippleVO.setLabelName(req.getSlang() == SLangType.ENGLISH ? labelConfig.getLabelNameEn() : labelConfig.getLabelNameAr());
                    rippleVO.setLabelColor(labelConfig.getLabelColor());
                }
                if(!ObjectUtils.isEmpty(data.getLabelNameEn()) && !ObjectUtils.isEmpty(data.getLabelNameAr())){
                    rippleVO.setLabelName(req.getSlang() == SLangType.ENGLISH ? data.getLabelNameEn() : data.getLabelNameAr());
                }
                list.add(rippleVO);
            }
        }
        vo.setList(list);
        vo.setNextUrl(rippleDaoList.size() < size ? "" : String.valueOf(page + 1));
        goodsListHomeRedis.removeGetResUidToRedis(req.getUid(), BaseDataResourcesConstant.TYPE_RIPPLE);
        return vo;
    }


    /**
     * 设置声波资源
     */
    public void setRipple(SetResourcesDTO req) {
        if (req.getRipple_id() == null) {
            logger.info("ripple_id is null . uid={} pid={}", req.getUid(), req.getRipple_id());
            throw new CommonException(UserHttpCode.PARAM_ERROR);
        }
        ResourcesDTO resourcesDTO = new ResourcesDTO();
        resourcesDTO.setUid(req.getUid());
        resourcesDTO.setResId(String.valueOf(req.getRipple_id()));
        resourcesDTO.setResType(BaseDataResourcesConstant.TYPE_RIPPLE);
        resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_WEAR);
        resourcesDTO.setDesc("store set ripple");
        resourcesDTO.setmTime(DateHelper.getNowSeconds());
        ApiResult<String> result = dataResourcesService.handleRes(resourcesDTO);
        if (result.isError()) {
            logger.info("set ripple handleRes failure. uid={} ripple_id={} code={} msg={}",
                    req.getUid(), req.getRipple_id(), result.getCode(), result.getCode().getMsg());
            if (result.getCode().getCode() == StoreConstant.NOT_OWN_RESOURCES) {
                throw new CommonException(UserHttpCode.NOT_OWN_RIPPLE_RESOURCES);
            }
            throw new CommonException(UserHttpCode.RIPPLE_SET_FAILED);
        }
    }

    @Override
    public GoodsListHomeVO.GoodsItemVO getGoodsItem(StoreDTO dto, int resId) {
        RippleSourceData sourceData = rippleSourceDao.findStoreData(resId);
        if (sourceData == null) {
            logger.info("return sourceData is null uid={} resId={}", dto.getUid(), resId);
            return null;
        }
        GoodsListHomeVO.GoodsItemVO vo = new GoodsListHomeVO.GoodsItemVO();
        vo.setRes_type(BaseDataResourcesConstant.TYPE_RIPPLE);
        vo.setRes_id(resId);
        vo.setSource_icon(sourceData.getRipple_icon() != null ? sourceData.getRipple_icon() : "");
        vo.setSource_name(dto.getSlang() == SLangType.ARABIC ? sourceData.getName_ar() : sourceData.getName());
        vo.setSource_url(sourceData.getRipple_sources());
        vo.setBeans(sourceData.getBeans());
        vo.setDays(sourceData.getDays());
        vo.setBuy_type(0);
        return vo;
    }

    /**
     * 购买声波装饰
     */
    @Override
    public String buyGoods(StoreGoodsDTO dto) {
        ActorData actorData = actorDao.getActorData(dto.getUid());
        if (actorData == null) {
            logger.error("user not exist. uid={}", dto.getUid());
            throw new CommonException(UserHttpCode.USER_NOT_EXIST);
        }
        Integer resId = dto.getRes_id();
        boolean isNewStore = dto.isFromNewStore();
        if (resId == null) {
            logger.info("rippleId is null . uid={} ", dto.getUid());
            throw new CommonException(UserHttpCode.PARAM_ERROR);
        }
        RippleSourceData sourceData = rippleSourceDao.findData(resId);

        if (sourceData == null) {
            logger.info("sourceData is null  uid={} rippleId={}", dto.getUid(), resId);
            throw new CommonException(UserHttpCode.BUY_RES_NOT_EXIST);
        }
        int cost = sourceData.getBeans();
        int days = sourceData.getDays() <= 0 ? 7 : sourceData.getDays();
        if (cost <= 0) {
            logger.info("sourceData cost={} <=0 . uid={} rippleId={}", cost, dto.getUid(), resId);
            throw new CommonException(UserHttpCode.BUY_RES_NOT_EXIST);
        }

        int buyType = sourceData.getBuy_type();
        if (buyType == 1) {
            if (actorData.getHeartGot() < cost) {
                logger.info("sourceData is null or cost={} <=0 . uid={} rippleId={}", cost, dto.getUid(), resId);
                throw new CommonException(UserHttpCode.COIN_NOT_ENOUGH);
            }
            boolean success = heartRecordDao.changeHeart(dto.getUid(), -cost, StoreConstant.BUY_RIPPLE_TITLE, StoreConstant.BUY_RIPPLE_TITLE);
            if (!success) {
                logger.info("change heart fail cost={} uid={} rippleId={}", cost, dto.getUid(), resId);
                throw new CommonException(HttpCode.SERVER_ERROR);
            }
        } else {
            if (actorData.getBeans() < cost) {
                logger.info("sourceData is null or cost={} <=0 . uid={} rippleId={}", cost, dto.getUid(), resId);
                throw new CommonException(UserHttpCode.DIAMONDS_NOT_ENOUGH);
            }
            MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
            moneyDetailReq.setRandomId();
            moneyDetailReq.setUid(dto.getUid());
            moneyDetailReq.setAtype(StoreConstant.BUY_RIPPLE_TYPE);
            moneyDetailReq.setChanged(-cost);
            moneyDetailReq.setTitle(StoreConstant.BUY_RIPPLE_TITLE);
            moneyDetailReq.setDesc(StoreConstant.BUY_RIPPLE_TITLE);
            ApiResult<String> reduceBeansResult = dataCenterService.reduceBeans(moneyDetailReq);
            if (!reduceBeansResult.isOk()) {
                logger.info("change beans fail cost={} uid={} rippleId={}", cost, dto.getUid(), resId);
                throw new CommonException(HttpCode.SERVER_ERROR);
            }
        }
        goodsListHomeRedis.incrHotGoodsRankingScore(BaseDataResourcesConstant.TYPE_RIPPLE, resId);
        ResourcesDTO resourcesDTO = new ResourcesDTO();
        String toUid = dto.getUid();
        String fromUid = "";
        int getWay = BaseDataResourcesConstant.TYPE_BUY_GET;
        if (!StringUtils.isEmpty(dto.getAid())) {
            fromUid = dto.getUid();
            toUid = dto.getAid();
            getWay = BaseDataResourcesConstant.TYPE_OTHER_BUY_GET;
        }
        resourcesDTO.setUid(toUid);
        resourcesDTO.setResId(String.valueOf(resId));
        resourcesDTO.setResType(BaseDataResourcesConstant.TYPE_RIPPLE);
        resourcesDTO.setDays(days);
        resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_GET);
        resourcesDTO.setDesc(StoreConstant.BUY_RIPPLE_TITLE);
        resourcesDTO.setmTime(DateHelper.getNowSeconds());
        resourcesDTO.setGainType(BaseDataResourcesConstant.GAIN_TYPE_DAY);
        resourcesDTO.setGetWay(getWay);
        resourcesDTO.setEmptyWearType(BaseDataResourcesConstant.EMPTY_WEAR_AUTO);
        resourcesDTO.setFromUid(fromUid);
        ApiResult<String> result = dataResourcesService.handleRes(resourcesDTO);
        if (result.isError()) {
            logger.error("buy bubble failure. cost={} uid={} rippleId={} toUid={} days={} code={} msg={}",
                    cost, dto.getUid(), resId, toUid, days, result.getCode(), result.getCode().getMsg());
            throw new CommonException(UserHttpCode.RES_BUY_FAILED);
        }
        return sourceData.getRipple_icon();
    }

}
