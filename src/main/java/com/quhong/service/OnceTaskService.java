package com.quhong.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quhong.mysql.data.OnceTaskRecord;
import com.quhong.mysql.mapper.ustar_log.OnceTaskMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OnceTaskService extends ServiceImpl<OnceTaskMapper, OnceTaskRecord> {
    private static final Logger logger = LoggerFactory.getLogger(OnceTaskService.class);

    public List<OnceTaskRecord> getByUid(String uid) {
        return lambdaQuery().eq(OnceTaskRecord::getUid, uid).list();
    }

    public OnceTaskRecord getByUidAndTaskId(String uid, int taskId) {
        return lambdaQuery().eq(OnceTaskRecord::getUid, uid).eq(OnceTaskRecord::getTaskId, taskId).orderByDesc(OnceTaskRecord::getcTime).last("limit 1").one();
    }
}
