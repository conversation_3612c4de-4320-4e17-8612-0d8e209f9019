package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.BanLogEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.config.AdminRedisBean;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.*;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.BaseInitData;
import com.quhong.data.dto.*;
import com.quhong.data.vo.*;
import com.quhong.dto.VipV2BuyDTO;
import com.quhong.dto.VipV2ChargeDTO;
import com.quhong.enums.*;
import com.quhong.exception.CommonException;
import com.quhong.exceptions.AdminCommonException;
import com.quhong.feign.DataCenterService;
import com.quhong.feign.DataResourcesService;
import com.quhong.feign.ISundryService;
import com.quhong.feign.IVIPService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.msg.push.UserMonitorPushMsg;
import com.quhong.msg.room.DismissRoomPushMsg;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.*;
import com.quhong.redis.*;
import com.quhong.room.RoomWebSender;
import com.quhong.room.redis.RoomKickRedis;
import com.quhong.userMonitor.UserMonitorRedis;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.RoomUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.quhong.constant.AdminLogTypeEnum.*;

/**
 * <AUTHOR>
 * @date 2022/9/29
 */
@Service
public class AdminService {

    private static final Logger logger = LoggerFactory.getLogger(AdminService.class);
    private static final String CONTENT_EN = "The room has been closed, please go to another room for now. Have fun.❤";
    private static final String CONTENT_AR = "الغرفة مفلق ، يرجى الانتقال إلى الغرفة الأخرى في الوقت الحالي. إستمتع.❤";
    private static final String FREEZE_TITLE = "الحساب محظور";
    private static final String FREEZE_BODY = "حسابك مجمد بسبب انتهاك لائحة اليوستار وشتم الآخرين.\n سيحرر بعد %s";
    private static final String RELEASE_TITLE = "تحديث حالة الحساب";
    private static final String RELEASE_BODY = "تم تحرير الحساب";
    private static final String SYSTEM = "system";

    private final List<String> RECHARGE_POWER_ADMIN = Arrays.asList("59662130f6f320b97acf6f3eg"); // ali

    private final List<AdminLogTypeEnum> ADMIN_LOG_TYPE_LIST = Arrays.asList(ADMIN_LOG_UNBLOCK_MIC_TNID,
            ADMIN_LOG_UNBLOCK_ROOM_FILE_TNID, ADMIN_LOG_UNBLOCK_UPDATE_PROFILE_TNID,
            ADMIN_LOG_UNBLOCK_CREATE_ROOM_TNID, ADMIN_LOG_UNBLOCK_PRIVATE_MSG_TNID,
            ADMIN_LOG_UNBLOCK_ADD_FRIENDS_TNID, ADMIN_LOG_UNBLOCK_DEVICE_TNID);

    private static final List<Integer> QUERY_GAME_TYPE_LIST = Arrays.asList(920, 921, 935, 936, 937, 938, 939, 940, 944, 945, 946, 947, 951, 952);
    private static final List<Integer> CONSUME_ACT_TYPE_LIST = Arrays.asList(40, 41, 51, 52, 53, 61, 62, 100, 103, 203, 204, 301);

    // VIP卡下发允许的天数
    private static final List<Integer> ALLOWED_VIP_DAYS = Arrays.asList(1, 3, 7, 10, 15, 30);

    @Resource
    private AdminUserDao adminUserDao;
    @Resource(name = AdminRedisBean.TOKEN_CACHE)
    private StringRedisTemplate tokenRedis;
    @Resource
    private BadWordDao badWordDao;
    @Resource
    private AdminHandleLogDao adminHandleLogDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private PkGameRedis pkGameRedis;
    @Resource
    private PkGameDao pkGameDao;
    @Resource
    private RoyaltyUserDao royaltyUserDao;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;
    @Resource
    private DataResourcesService dataResourcesService;
    @Resource
    private BanReasonDao banReasonDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private BasePlayerRedis basePlayerRedis;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private OfficialDao officialDao;
    @Resource
    private NoticeNewDao noticeNewDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private AdminAppManageRedis adminAppManageRedis;
    @Resource
    private UserMonitorRedis userMonitorRedis;
    @Resource
    private ChatHallRedis chatHallRedis;
    @Resource
    private UserMonitorDao userMonitorDao;
    @Resource
    private UserMonitorLogDao userMonitorLogDao;
    @Resource
    private AdminCommonMongoDao adminCommonMongoDao;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Resource
    private BlockIpRedis blockIpRedis;
    @Resource
    private BlockRedis blockRedis;
    @Resource
    private AdminTnDeviceLogDao adminTnDeviceLogDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private VipConfigDao vipConfigDao;
    @Resource
    private NewUserHonorDao newUserHonorDao;
    @Resource
    private ISundryService iSundryService;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private GamblingGameRedis gamblingGameRedis;
    @Resource
    private SlaveMoneyDetailDao slaveMoneyDetailDao;
    @Resource
    private UserRegisterDao userRegisterDao;
    @Resource
    private BeautifulRidDao beautifulRidDao;
    @Resource
    private RiskAdminUserRedis riskAdminUserRedis;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private RechargeMoneyDetailDao rechargeMoneyDetailDao;
    @Resource
    private HomeBannerService homeBannerService;
    @Resource
    private ActorConfigDao actorConfigDao;
    @Resource
    private ResourceConfigDao resourceConfigDao;
    @Resource
    private UserResourceDao userResourceDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private IdentifyRedis identifyRedis;
    @Resource
    private IVIPService iVIPService;

    public LoginVO login(LoginDTO dto) {
        AdminUserData adminUser = adminUserDao.findData(dto.getAccount(), dto.getPwd());
        if (null == adminUser || 0 == adminUser.getStatus()) {
            throw new AdminCommonException(AdminHttpCode.ACCOUNT_ERROR);
        }
        if (ServerConfig.isProduct()) {
            if (!StringUtils.isEmpty(dto.getAndroid_id())) {
                if (1 == adminUser.getStatus()) {
                    adminUser.setStatus(2);
                    adminUser.setAndroidId(dto.getAndroid_id());
                    adminUserDao.save(adminUser);
                } else if (!adminUser.getAndroidId().equals(dto.getAndroid_id())) {
                    throw new AdminCommonException(AdminHttpCode.AUTH_ERROR);
                }
                String token = setToken(adminUser.getUid());
                adminHandleLogDao.insert(new AdminHandleLogData("", adminUser.getUid(), 9, "login success"));
                return new LoginVO(adminUser.getUid(), token, adminUser.getLevel());
            }
            if (!StringUtils.isEmpty(dto.getImei())) {
                if (1 == adminUser.getStatus()) {
                    adminUser.setStatus(2);
                    adminUser.setImei(dto.getImei());
                    adminUserDao.save(adminUser);
                } else if (!adminUser.getImei().equals(dto.getImei())) {
                    throw new AdminCommonException(AdminHttpCode.AUTH_ERROR);
                }
                String token = setToken(adminUser.getUid());
                adminHandleLogDao.insert(new AdminHandleLogData("", adminUser.getUid(), ADMIN_LOG_LOGIN_SUCCESS.getCode(), ADMIN_LOG_LOGIN_SUCCESS.getDesc()));
                return new LoginVO(adminUser.getUid(), token, adminUser.getLevel());
            }
        } else {
            String token = setToken(adminUser.getUid());
            if (adminUser.getStatus() == 1 && !StringUtils.isEmpty(dto.getAndroid_id())) {
                adminUser.setStatus(2);
                adminUser.setAndroidId(dto.getAndroid_id());
                adminUserDao.save(adminUser);
            }
            adminHandleLogDao.insert(new AdminHandleLogData("", adminUser.getUid(), ADMIN_LOG_LOGIN_SUCCESS.getCode(), ADMIN_LOG_LOGIN_SUCCESS.getDesc()));
            return new LoginVO(adminUser.getUid(), token, adminUser.getLevel());
        }
        throw new AdminCommonException(AdminHttpCode.AUTH_ERROR);
    }

    private String setToken(String uid) {
        String token = UUID.randomUUID().toString().replace("-", "");
        tokenRedis.opsForValue().set(uid, token, 1, TimeUnit.DAYS);
        return token;
    }

    public void insertBadWord(BadWordDTO dto) {
        if (riskAdminUserRedis.isBlockAdminUser(dto.getUid())) {
            logger.info("risk admin uid:{}", dto.getUid());
            throw new AdminCommonException(AdminHttpCode.TRY_AGAIN_LATER);
        }
        String[] wordList = dto.getWord().split("\\r?\\n");
        List<BadWordData> dataList = new ArrayList<>();
        for (String word : wordList) {
            if (StringUtils.isEmpty(word)) {
                continue;
            }
            BadWordData data = new BadWordData();
            data.setWord(word);
            data.setCtime(DateHelper.getNowSeconds());
            dataList.add(data);
        }
        badWordDao.batchInsert(dataList);
        adminHandleLogDao.insert(new AdminHandleLogData("", dto.getUid(), ADMIN_LOG_ADD_BAD_WORD.getCode(), "add bad word :" + dto.getWord()));
    }

    public void delUserInPk(AdminDTO dto) {
        ActorData actorData = actorDao.getActorByRid(Integer.parseInt(dto.getRid()));
        if (actorData == null) {
            logger.info("can not find actor data. rid={}", dto.getRid());
            throw new AdminCommonException(AdminHttpCode.USER_NOT_EXIST);
        }
        if (!pkGameRedis.isInPkGame(actorData.getUid())) {
            logger.info("this user not in pk. uid={}", actorData.getUid());
            throw new AdminCommonException(AdminHttpCode.USER_NOT_IN_PK);
        }
        pkGameRedis.removeUserInPkGame(actorData.getUid());
        adminHandleLogDao.insert(new AdminHandleLogData(actorData.getUid(), dto.getUid(), ADMIN_LOG_STOP_PK.getCode(), ADMIN_LOG_STOP_PK.getDesc()));
    }

    public void forceDelPk(AdminDTO dto) {
        ActorData actorData = actorDao.getActorByRid(Integer.parseInt(dto.getRid()));
        if (actorData == null) {
            logger.info("can not find actor data. rid={}", dto.getRid());
            throw new AdminCommonException(AdminHttpCode.USER_NOT_EXIST);
        }
        String roomId = RoomUtils.formatRoomId(actorData.getUid());
        PkGame pkGame = pkGameDao.getByRoomId(roomId, 1);
        if (pkGame == null) {
            logger.info("pk game not exist. roomId={}", roomId);
            throw new AdminCommonException(AdminHttpCode.PK_GAME_NOT_EXIST);
        }
        pkGameRedis.addPkGameRunningTime(pkGame.get_id().toString(), 1);
        adminHandleLogDao.insert(new AdminHandleLogData(actorData.getUid(), dto.getUid(), ADMIN_LOG_CLOSE_PK.getCode(), ADMIN_LOG_CLOSE_PK.getDesc()));
    }

    public void addOwnerRoyalty(AdminDTO dto) {
        if (riskAdminUserRedis.isBlockAdminUser(dto.getUid())) {
            logger.info("risk admin uid:{}", dto.getUid());
            throw new AdminCommonException(AdminHttpCode.TRY_AGAIN_LATER);
        }
        int rid = Integer.parseInt(dto.getRid());
        ActorData actorData = actorDao.getActorByRid(rid);
        if (actorData == null) {
            logger.info("can not find actor data. rid={}", dto.getRid());
            throw new AdminCommonException(AdminHttpCode.USER_NOT_EXIST);
        }
        RoyaltyUserData royaltyUserData = royaltyUserDao.findDataByRid(rid);
        if (royaltyUserData != null) {
            logger.info("This user has already get a room commission. rid={}", dto.getRid());
            throw new AdminCommonException(AdminHttpCode.USER_HAS_ALREADY_GET_A_ROOM_COMMISSION);
        }
        royaltyUserDao.save(new RoyaltyUserData(rid, actorData.getUid(), actorData.getName(), actorData.getCountry(), DateHelper.getNowSeconds()));
        try {
            // 写入redis中定时返回房间提成
            clusterTemplate.opsForHash().put(getAllowanceKey(), actorData.getUid(), "1");
        } catch (Exception e) {
            logger.error("add addOwnerRoyalty in redis error. aid={} {}", actorData.getUid(), e.getMessage(), e);
            throw new AdminCommonException(AdminHttpCode.MOVED_TO_THE_ROOM_COMMISSION_LIST_FAILED);
        }
        adminHandleLogDao.insert(new AdminHandleLogData(actorData.getUid(), dto.getUid(), ADMIN_LOG_ROOM_COMMISSION.getCode(), ADMIN_LOG_ROOM_COMMISSION.getDesc()));
    }

    public void delRoomOwnerRoyalty(AdminDTO dto) {
        if (riskAdminUserRedis.isBlockAdminUser(dto.getUid())) {
            logger.info("risk admin uid:{}", dto.getUid());
            throw new AdminCommonException(AdminHttpCode.TRY_AGAIN_LATER);
        }
        int rid = Integer.parseInt(dto.getRid());
        ActorData actorData = actorDao.getActorByRid(rid);
        if (actorData == null) {
            logger.info("can not find actor data. rid={}", dto.getRid());
            throw new AdminCommonException(AdminHttpCode.USER_NOT_EXIST);
        }
        RoyaltyUserData royaltyUserData = royaltyUserDao.findDataByRid(rid);
        if (royaltyUserData == null) {
            logger.info("this user not in room Commission list. rid={}", dto.getRid());
            throw new AdminCommonException(AdminHttpCode.USER_NOT_IN_ROOM_COMMISSION_LIST);
        }
        royaltyUserDao.remove(royaltyUserData);
        try {
            // 从redis中删除房间提成人员
            clusterTemplate.opsForHash().delete(getAllowanceKey(), actorData.getUid());
        } catch (Exception e) {
            logger.error("delete addOwnerRoyalty from redis error. aid={} {}", actorData.getUid(), e.getMessage(), e);
            throw new AdminCommonException(AdminHttpCode.REMOVE_USER_FROM_COMMISSION_LIST_FAILED);
        }
        adminHandleLogDao.insert(new AdminHandleLogData(actorData.getUid(), dto.getUid(), ADMIN_LOG_CANCEL_ROOM_COMMISSION.getCode(), ADMIN_LOG_CANCEL_ROOM_COMMISSION.getDesc()));
    }

    private String getAllowanceKey() {
        return "room_allowance_uid";
    }

    public void setBadge(AdminDTO dto) {
        if (riskAdminUserRedis.isBlockAdminUser(dto.getUid())) {
            logger.info("risk admin uid:{}", dto.getUid());
            throw new AdminCommonException(AdminHttpCode.TRY_AGAIN_LATER);
        }
        int rid = Integer.parseInt(dto.getRid());
        ActorData actorData = actorDao.getActorByRid(rid);
        if (actorData == null) {
            logger.info("can not find actor data. rid={}", dto.getRid());
            throw new AdminCommonException(AdminHttpCode.USER_NOT_EXIST);
        }
        int days = 0;
        try {
            days = Integer.parseInt(dto.getDays());
        } catch (Exception e) {
            logger.info("Please enter a positive number. days={}", dto.getDays());
            throw new AdminCommonException(AdminHttpCode.PLEASE_ENTER_A_POSITIVE_NUMBER);
        }
        if (days <= 0 || days > 999) {
            logger.info("Please enter a positive number. days={}", days);
            throw new AdminCommonException(AdminHttpCode.PLEASE_ENTER_A_POSITIVE_NUMBER);
        }
        ResourcesDTO resourcesDTO = new ResourcesDTO();
        resourcesDTO.setUid(actorData.getUid());
        resourcesDTO.setResId(String.valueOf(dto.getBadge_id()));
        resourcesDTO.setResType(1);
        resourcesDTO.setDesc("add admin badge");
        resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_GET);
        resourcesDTO.setOfficialMsg(1);
        resourcesDTO.setmTime(DateHelper.getNowSeconds());
        resourcesDTO.setGetWay(3);
        resourcesDTO.setNum(1);
        resourcesDTO.setDays(days);
        dataResourcesService.handleRes(resourcesDTO);
        ApiResult<String> result = dataResourcesService.handleRes(resourcesDTO);
        if (result.isError()) {
            logger.error("add admin badge failure. bubbleId={} toUid={} days={} code={} msg={}",
                    dto.getBadge_id(), actorData.getUid(), days, result.getCode(), result.getCode().getMsg());
            throw new AdminCommonException(AdminHttpCode.SET_BADGE_FAILURE);
        }
        adminHandleLogDao.insert(new AdminHandleLogData(actorData.getUid(), dto.getUid(), ADMIN_LOG_WEAR_BADGE.getCode(), ADMIN_LOG_WEAR_BADGE.getDesc()));
    }

    public void delBadge(AdminDTO dto) {
        if (riskAdminUserRedis.isBlockAdminUser(dto.getUid())) {
            logger.info("risk admin uid:{}", dto.getUid());
            throw new AdminCommonException(AdminHttpCode.TRY_AGAIN_LATER);
        }
        int rid = Integer.parseInt(dto.getRid());
        ActorData actorData = actorDao.getActorByRid(rid);
        if (actorData == null) {
            logger.info("can not find actor data. rid={}", dto.getRid());
            throw new AdminCommonException(AdminHttpCode.USER_NOT_EXIST);
        }
        ResourcesDTO resourcesDTO = new ResourcesDTO();
        resourcesDTO.setUid(actorData.getUid());
        resourcesDTO.setResId(String.valueOf(dto.getBadge_id()));
        resourcesDTO.setResType(1);
        resourcesDTO.setDesc("remove admin badge");
        resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_DELETE);
        resourcesDTO.setOfficialMsg(0);
        resourcesDTO.setmTime(DateHelper.getNowSeconds());
        resourcesDTO.setGetWay(3);
        resourcesDTO.setNum(1);
        resourcesDTO.setDays(0);
        ApiResult<String> result = dataResourcesService.handleRes(resourcesDTO);
        if (result.isError()) {
            logger.error("remove admin badge failure. bubbleId={} toUid={} code={} msg={}",
                    dto.getBadge_id(), actorData.getUid(), result.getCode(), result.getCode().getMsg());
            throw new AdminCommonException(AdminHttpCode.DEL_BADGE_FAILURE);
        }
        adminHandleLogDao.insert(new AdminHandleLogData(actorData.getUid(), dto.getUid(), ADMIN_LOG_RM_BADGE.getCode(), ADMIN_LOG_RM_BADGE.getDesc()));
    }


    public void banUser(AdminBanDTO dto) {
        logger.info("banUser dto={}", dto);
        String opUid = dto.getUid();
        String reason = dto.getReason();
        int reasonType = dto.getReason_type();
        int mType = dto.getM_type();
        int blockTerm = dto.getBlock_term();
        int deleteType = dto.getDelete_type();

        int rid = Integer.parseInt(dto.getRid());
        ActorData actorData = actorDao.getActorByRid(rid);
        if (actorData == null) {
            logger.info("can not find actor data. rid={}", dto.getRid());
            throw new AdminCommonException(AdminHttpCode.USER_NOT_EXIST);
        }
        String aid = actorData.getUid();
        long releaseAt = 0;
        String msg = "";
        int now = DateHelper.getNowSeconds();
        int mTypeCode = 0;
        if (mType == 2) {
            throw new AdminCommonException(AdminHttpCode.FUNCTION_CANNOT_USED);
//            mTypeCode = 2;
//            switch (blockTerm) {
//                case 1:
//                    releaseAt = now + 3 * 3600;
//                    msg = "3小时";
//                    break;
//                case 2:
//                    releaseAt = now + 24 * 3600;
//                    msg = "24小时";
//                    break;
//                default:
//                    releaseAt = now + 7 * 24 * 3600;
//                    msg = "7天";
//                    break;
//            }
//            userMonitorRedis.addMonitorUser(aid, releaseAt);
//            adminHandleLogDao.insert(new AdminHandleLogData(aid, opUid,
//                    ADMIN_LOG_FREEZE_USER.getCode(), "冻结用户:" + msg));
//            String timeStr = DateHelper.ARABIAN.formatDateTime(new Date(releaseAt * 1000L));
//            sendOfficialNews(aid, FREEZE_TITLE, String.format(FREEZE_BODY, timeStr));
        } else if (mType == 3) {
            int days = 0;
            mTypeCode = 3;
            switch (blockTerm) {
                case 1:
                    releaseAt = now + 24 * 3600;
                    msg = "1天";
                    days = 1;
                    break;
                case 2:
                    releaseAt = now + 7 * 24 * 3600;
                    msg = "7天";
                    days = 7;
                    break;
                default:
                    releaseAt = Integer.MAX_VALUE;
                    msg = "永久";
                    days = 999;
                    break;
            }
            if (ServerConfig.isProduct() && days == 999 && !RECHARGE_POWER_ADMIN.contains(opUid)
                    && rechargeDailyInfoDao.getRechargeTotalNum(aid) > 2) {
                logger.info("only ali can ban recharge user opUid:{} rid:{} aid:{}", opUid, rid, aid);
                throw new AdminCommonException(AdminHttpCode.BAN_RECHARGE_USER);
            }
            banAccount(opUid, aid, reason, actorData.getTn_id(), actorData.getIp(), msg);
            // 注意要redis整体迁移后才能上
            adminAppManageRedis.addAreaBlackUser(aid);

            if (days != 999) {
                userMonitorRedis.addMonitorUser(aid, releaseAt);
            }
            removeChatHallRecord(aid);
            banLogEvent(opUid, aid, 1, 1, days);

        } else {
            logger.info("mType={} but mType only 2 or 3", mType);
            throw new AdminCommonException(AdminHttpCode.PARAM_ERROR);
        }


        if (deleteType == 1 || deleteType == 3) {
            long dCount = adminCommonMongoDao.removeAllComment(aid);
            logger.info("delete comment success aid={} dCount={}", aid, dCount);
        }
//        if (mType == 2 && (deleteType == 2 || deleteType == 3)) {
//            // 冻结时，把头像和banner改为默认的
//        }

        UserMonitorData userMonitorData = userMonitorDao.findDataByCode(aid, mTypeCode);
        if (userMonitorData == null) {
            userMonitorData = new UserMonitorData();
            userMonitorData.setUid(aid);
            userMonitorData.setCode(mTypeCode);
        }
        userMonitorData.setReason(reason);
        userMonitorData.setReason_type(reasonType);
        userMonitorData.setRelease_at(releaseAt);
        userMonitorData.setBlock_term(String.valueOf(blockTerm));
        userMonitorData.setOpt_time(now);
        userMonitorDao.save(userMonitorData);

        UserMonitorLog log = new UserMonitorLog();
        log.setOperator(opUid);
        log.setUid(aid);
        log.setCode(mTypeCode);
        log.setReason(reason);
        log.setBlock_term(String.valueOf(blockTerm));
        log.setOpt_time(now);
        userMonitorLogDao.save(log);
        sendUserMonitor(aid, mTypeCode, reason, releaseAt, now, blockTerm);
    }

    public void releaseUser(AdminBanDTO dto) {
        logger.info("releaseUser dto={}", dto);
        String opUid = dto.getUid();
        int rid = Integer.parseInt(dto.getRid());
        ActorData actorData = actorDao.getActorByRid(rid);
        if (actorData == null) {
            logger.info("can not find actor data. rid={}", dto.getRid());
            throw new AdminCommonException(AdminHttpCode.USER_NOT_EXIST);
        }
        String aid = actorData.getUid();
        doRelease(opUid, aid);
    }

    private void doRelease(String opUid, String aid) {
        boolean isSystemOp = opUid.equals(SYSTEM);
        int banAction = 2;
        String reason = "release";
        if (isSystemOp) {
            banAction = 3;
            reason = "system expired release";
        }

        cancelBanAccount(opUid, aid, isSystemOp, reason);
        userMonitorDao.removeData(aid);
        userMonitorRedis.delMonitorUser(aid);
        // 注意要redis整体迁移后才能上
        adminAppManageRedis.delAreaBlackUser(aid);

        int now = DateHelper.getNowSeconds();
        UserMonitorLog log = new UserMonitorLog();
        log.setOperator(opUid);
        log.setUid(aid);
        log.setCode(0);
        log.setReason(reason);
        log.setBlock_term("");
        log.setOpt_time(now);
        userMonitorLogDao.save(log);
        sendUserMonitor(aid, 4, "تم تحرير الحساب", 0, now, 0);
        sendOfficialNews(aid, RELEASE_TITLE, RELEASE_BODY);
        banLogEvent(opUid, aid, banAction, 1, null);
    }

    private void banAccount(String opUid, String aid, String reason, String tnId, String ip, String blockMsg) {
        BanReasonData banReasonData = banReasonDao.findData(aid);
        if (banReasonData != null) {
            if (!ObjectUtils.isEmpty(tnId)) {
                banReasonData.setTn_id(tnId);
            }
            if (!ObjectUtils.isEmpty(ip)) {
                banReasonData.setIp(ip);
            }
            banReasonDao.save(banReasonData);
        } else {
            String desc = reason + ":封禁" + (StringUtils.isEmpty(blockMsg) ? "永久" : blockMsg);
            int now = DateHelper.getNowSeconds();
            banReasonData = new BanReasonData();
            banReasonData.setOp_uid(opUid);
            banReasonData.setUid(aid);
            banReasonData.setReason(reason);
            banReasonData.setTn_id(tnId);
            banReasonData.setIp(ip);
            banReasonData.setMtime(now);
            banReasonDao.save(banReasonData);
            adminHandleLogDao.insert(new AdminHandleLogData(aid, opUid, ADMIN_LOG_BAN_ACCOUNT.getCode(), desc));
        }
        actorDao.updateInValid(aid, BaseInitData.SYS_DEFAULT_AVATARS, AdminConfigConstant.BAN_HEAD_LIST);
        basePlayerRedis.removeToken(aid);

        String roomId = RoomUtils.formatRoomId(aid);
        MongoRoomData mongoRoomData = mongoRoomDao.findData(roomId);
        if (mongoRoomData != null) {
            mongoRoomDao.updateRoomDisplay(roomId, 0);
            sendDismissRoom(roomId);
        }
    }

    private void cancelBanAccount(String opUid, String aid, boolean isSystem, String remark) {
        roomKickRedis.delReportLevel(aid);
        actorDao.updateField(aid, "valid", 1);
        String roomId = RoomUtils.formatRoomId(aid);
        mongoRoomDao.updateField(roomId, "display", 1);
        BanReasonData banReasonData = banReasonDao.findData(aid);
        if (banReasonData != null) {
            banReasonDao.deleteData(aid);
            opUid = isSystem ? SYSTEM : opUid;
            adminHandleLogDao.insert(new AdminHandleLogData(aid, opUid, ADMIN_LOG_CANCEL_BAN_ACCOUNT.getCode(), remark));
        }
    }


    private void sendDismissRoom(String roomId) {
        DismissRoomPushMsg msg = new DismissRoomPushMsg();
        msg.setContent_en(CONTENT_EN);
        msg.setContent_ar(CONTENT_AR);
        roomWebSender.sendRoomWebMsg(roomId, null, msg, true);
    }

    private void sendOfficialNews(String uid, String title, String body) {
        OfficialData officialData = new OfficialData(title, body, uid);
        officialDao.save(officialData);
        if (officialData.get_id() != null) {
            noticeNewDao.save(new NoticeNewData(uid, officialData.get_id().toString()));
        }
    }

    private void banLogEvent(String opUid, String uid, int banAction
            , int banType, Integer banDays) {
        banLogEvent(opUid, uid, banAction, banType, banDays, null);
    }

    private void banLogEvent(String opUid, String uid, int banAction
            , int banType, Integer banDays, String ip) {
        BanLogEvent logEvent = new BanLogEvent();
        logEvent.setUid(uid);
        logEvent.setBan_action(banAction);
        logEvent.setBan_type(banType);
        logEvent.setBan_days(banDays);
        String adminAccount;
        if (SYSTEM.equals(opUid)) {
            adminAccount = SYSTEM;
        } else {
            AdminUserData adminUserData = adminUserDao.findData(opUid);
            adminAccount = adminUserData == null ? "unkown" : adminUserData.getAccount();
        }
        logEvent.setOperator(adminAccount);
        logEvent.setIp(ip);
        logEvent.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(logEvent));
    }

    private void removeChatHallRecord(String aid) {
        List<ChatHallMsgVO> allChatHallMsgVO = chatHallRedis.getChatHallList();
        for (ChatHallMsgVO item : allChatHallMsgVO) {
            if (aid.equals(item.getAid())) {
                chatHallRedis.delChatHall(item);
            }
        }
    }

    /**
     * @param aid
     * @param codeType 1 警告 2 冻结 3 封禁 4 释放
     */
    private void sendUserMonitor(String aid, int codeType, String reason, long releaseAt, int now, int blockTerm) {
        UserMonitorPushMsg msg = new UserMonitorPushMsg();
        msg.setType(codeType);
        msg.setReason(reason);
        msg.setReleaseAt(releaseAt);
        msg.setSev_cur_time(now);
        msg.setBlock_term(blockTerm);
        roomWebSender.sendPlayerWebMsg("", aid, aid, msg, true);
    }

    public void systemRelease() {
        int now = DateHelper.getNowSeconds();
        Set<String> allUsers = userMonitorRedis.getAllMonitorUser(now);
        // ip或账号解除
        for (String item : allUsers) {
            if (item.contains(".")) {
                // ip 过期
                long delCount = blockIpRedis.delBlockIp(item);
                userMonitorRedis.delMonitorUser(item);
                banLogEvent(SYSTEM, item, 3, 3, null, item);
                logger.info("del ip:{} delCount:{} success", item, delCount);
            } else {
                UserMonitorLog userMonitorLog = userMonitorLogDao.findData(item, 3);
                if (userMonitorLog != null) {
                    doRelease(SYSTEM, item);
                } else {
                    userMonitorRedis.delMonitorUser(item);
                }
            }
        }

        // 设备解除
        for (AdminLogTypeEnum one : ADMIN_LOG_TYPE_LIST) {
            String tnKey = one.getTnKey();
            int cType = one.getCode();
            String desc = one.getDesc();
            Set<String> allTnId = blockRedis.getAllRangeTnId(tnKey, now);
            for (String tnId : allTnId) {
                long count = blockRedis.delBlockKeyTnId(tnKey, tnId);
                if (count > 0) {
                    AdminTnDeviceLogData log = new AdminTnDeviceLogData();
                    log.setUid("");
                    log.setOp_uid(SYSTEM);
                    log.setTn_id(tnId);
                    log.setC_type(cType);
                    log.setDesc(desc);
                    log.setC_time(DateHelper.getNowSeconds());
                    adminTnDeviceLogDao.insert(log);
                }
            }
        }
    }

    public RelatedAdminVO blockRelated(AdminDTO dto) {
        logger.info("blockRelated dto={}", dto);
        String opUid = dto.getUid();
        int rid = Integer.parseInt(dto.getRid());
        ActorData actorData = actorDao.getActorByRid(rid);
        if (actorData == null) {
            logger.info("can not find actor data. rid={}", dto.getRid());
            throw new AdminCommonException(AdminHttpCode.USER_NOT_EXIST);
        }
        String aid = actorData.getUid();
        String tnId = actorData.getTn_id();
        int valid = actorData.getValid();
        String ip = actorData.getIp();

        RelatedAdminVO vo = new RelatedAdminVO();
        int userStat = 0;
        boolean banDevice = false;
        boolean banIp = false;
        boolean isCharge = false;
        int endTime = DateHelper.getNowSeconds();
        int startTime = endTime - 90 * 86400;

        if (valid > 0) {
            UserMonitorData userMonitorData = userMonitorDao.findData(aid);
            if (userMonitorData != null) {
                userStat = userMonitorData.getCode();
            }
        } else {
            userStat = 3;
        }
        String blockTime = blockRedis.checkBlock(tnId, BlockTnConstant.BLOCK_LOGIN);
        if (!StringUtils.isEmpty(blockTime)) {
            banDevice = true;
        }
        if (blockIpRedis.isBlockIp(ip)) {
            banIp = true;
        }
        if (ServerConfig.isProduct() && !RECHARGE_POWER_ADMIN.contains(opUid)
                && rechargeDailyInfoDao.getRechargeTotalNum(aid) > 2) {
            isCharge = true;
        }
        int last3Charge = rechargeDailyInfoDao.getUserTotalRechargeBean(aid, startTime, endTime);
        vo.setUser_stat(userStat);
        vo.setBan_device(banDevice);
        vo.setBan_ip(banIp);
        vo.setLast_3_charge(last3Charge);
        vo.setIs_charge(isCharge);
        return vo;
    }

    public void warnUser(AdminBanDTO dto) {
        logger.info("warnUser dto={}", dto);
        String opUid = dto.getUid();
        String reason = dto.getReason();
        int reasonType = dto.getReason_type();
        int rid = Integer.parseInt(dto.getRid());
        ActorData actorData = actorDao.getActorByRid(rid);
        if (actorData == null) {
            logger.info("can not find actor data. rid={}", dto.getRid());
            throw new AdminCommonException(AdminHttpCode.USER_NOT_EXIST);
        }
        String aid = actorData.getUid();
        int now = DateHelper.getNowSeconds();
        int mTypeCode = 1;
        UserMonitorData userMonitorData = userMonitorDao.findDataByCode(aid, mTypeCode);
        if (userMonitorData == null) {
            userMonitorData = new UserMonitorData();
            userMonitorData.setUid(aid);
            userMonitorData.setCode(mTypeCode);
        }
        userMonitorData.setReason(reason);
        userMonitorData.setReason_type(reasonType);
        userMonitorData.setOpt_time(now);
        userMonitorDao.save(userMonitorData);

        UserMonitorLog log = new UserMonitorLog();
        log.setOperator(opUid);
        log.setUid(aid);
        log.setCode(mTypeCode);
        log.setReason(reason);
        log.setOpt_time(now);
        userMonitorLogDao.save(log);
        sendUserMonitor(aid, mTypeCode, reason, 0, now, 0);
    }

    public SearchUserVO searchUser(AdminDTO dto) {
        String opUid = dto.getUid();

        ActorData actorData = actorDao.getActorByStrRidFromDb(dto.getRid());
        if (actorData == null) {
            logger.info("can not find actor data. rid={}", dto.getRid());
            throw new AdminCommonException(AdminHttpCode.USER_NOT_EXIST);
        }
        int rid = actorData.getRid();
        String aid = actorData.getUid();
        long lastLoginTime = 0;
        long lastLogoutTime = 0;
        long honorBeans = 0;
        int currentTime = DateHelper.getNowSeconds();
        String vipLeftTime = "";
        int vipL = 0;
        VipInfoData vipInfo = vipInfoDao.findVipInfo(aid);
        if (vipInfo != null) {
            int vipEndTime = (int) (vipInfo.getVipEndTime().getTime() / 1000);
            int leftTime = vipEndTime - currentTime;
            if (leftTime > 0) {
                vipL = vipInfo.getVipLevel();
                vipLeftTime = leftTime > 86400 ? String.format("%s days", leftTime / 86400) : "1 days";
            }
        }
        int vCode = actorData.getVersion_code();
        int registerTime = new ObjectId(aid).getTimestamp();
        String ip = actorData.getIp();
        String ipCountry = getIpCountry(ip);

        if (actorData.getLastLogin() != null) {
            if (actorData.getLastLogin().getLoginTime() != null) {
                lastLoginTime = actorData.getLastLogin().getLoginTime();
            }
            if (actorData.getLastLogin().getLogoutTime() != null) {
                lastLogoutTime = actorData.getLastLogin().getLogoutTime();
            }
        }
        NewUserHonorData newUserHonorData = newUserHonorDao.findData(aid);
        if (newUserHonorData != null) {
            honorBeans = newUserHonorData.getBeans();
        }

        SearchUserVO vo = new SearchUserVO();
        vo.setUid(aid);
        vo.setRid(actorData.getRid() + "");
        vo.setName(actorData.getName());
        vo.setHead(ImageUrlGenerator.generateNormalUrl(actorData.getHead()));
        vo.setUlvl(userLevelDao.getUserLevel(aid));
        vo.setVip_level(vipL);
        vo.setVipLeftTime(vipLeftTime);
        vo.setBeans(actorData.getBeans());
        vo.setGender2(actorData.getFb_gender());
        vo.setAnchor(actorData.getGender());
        vo.setReport(roomKickRedis.getReportLevel(aid));
        vo.setRegister_timestamp(registerTime);
        vo.setIp(ip);
        vo.setIp_country(ipCountry);
        vo.setHonor_beans((int) honorBeans);
        vo.setVersion_code(actorData.getVersion_code());
        vo.setVname(ObjectUtils.isEmpty(actorData.getVersion_name()) ? String.valueOf(actorData.getVersion_code()) : actorData.getVersion_name());
        vo.setLogout_time(lastLogoutTime > 0 ? (int) lastLogoutTime : registerTime);
        vo.setAlpha_rid("");

        vo.setValid(actorData.getValid());
        vo.setGender(actorData.getGender() == 2 ? "host" : "not host");
        vo.setFb_gender(actorData.getFb_gender() == 2 ? "female" : "male");
        vo.setState(actorData.getCountry());

        String vName = userRegisterDao.getVnameByVCodeCache(actorData.getVersion_code());

        vo.setVersionName(ObjectUtils.isEmpty(vName) ? actorData.getVersion_code() + "" : vName);
        vo.setAlphaRid(actorData.getAlphaRid() == null ? "" : actorData.getAlphaRid());
        vo.setAlphaLevel(actorData.getAlphaLevel());
        vo.setOriginalRid(actorData.getOriginalRid());
        vo.setCommission(getRoomAllowanceUser(actorData.getUid()));
        BeautifulRidData data = beautifulRidDao.findData(aid);
        if (data != null) {

            if (data.getEnd_time() == Integer.MAX_VALUE) {
                vo.setLeftValidDays("永久");
                vo.setTotalValidDays("永久");
            } else {
                long leftValidDays = (data.getEnd_time() - DateHelper.getNowSeconds()) / (24 * 60 * 60);
                vo.setLeftValidDays(leftValidDays + "");
                long totalValidDays = (data.getEnd_time() - data.getC_time()) / (24 * 60 * 60);
                vo.setTotalValidDays(totalValidDays + "");
            }
        } else {
            vo.setLeftValidDays("");
            vo.setTotalValidDays("");
        }
        vo.setLogout(actorData.getAccountStatus() == AccountConstant.DELETED ? 1 : 0);
        vo.setBcGameSwitch(homeBannerService.getBCGameSwitch(actorData));
        adminHandleLogDao.insert(new AdminHandleLogData(aid, opUid, ADMIN_LOG_SEARCH_USER.getCode(),
                ADMIN_LOG_SEARCH_USER.getDesc() + rid));
        logger.info("search vo={}", vo);
        return vo;
    }

    public QueryGameVO setGameUserType(GameUserTypeDTO dto) {
        if (riskAdminUserRedis.isBlockAdminUser(dto.getUid())) {
            logger.info("risk admin uid:{}", dto.getUid());
            throw new AdminCommonException(AdminHttpCode.TRY_AGAIN_LATER);
        }
        if (dto.getUserType() != 1 && dto.getUserType() != 2) {
            throw new AdminCommonException(HttpCode.PARAM_ERROR);
        }
        if (dto.getDiamonds() < 0) {
            throw new AdminCommonException(HttpCode.PARAM_ERROR);
        }
        ActorData actorData = paramCheck(dto.getRid());
        if (dto.getUserType() == 1) {
            // 盈利⽩名单⽤⼾
            gamblingGameRedis.addBsGameProfitList(actorData.getUid(), dto.getDiamonds());
            gamblingGameRedis.removeLoss(actorData.getUid());
        } else {
            // 亏损黑名单⽤⼾
            gamblingGameRedis.addBsGameLossList(actorData.getUid(), dto.getDiamonds());
            gamblingGameRedis.removeProfit(actorData.getUid());
        }
        adminHandleLogDao.insert(new AdminHandleLogData(
                actorData.getUid(),
                dto.getUid(),
                SET_USER_GAME_PROFIT_AND_LOSS.getCode(),
                String.format(SET_USER_GAME_PROFIT_AND_LOSS.getTnKey(), dto.getUserType() == 1 ? "profit" : "loss", dto.getRid(), dto.getDiamonds())));
        QueryGameVO vo = new QueryGameVO();
        vo.setProfitAmount(gamblingGameRedis.getUserProfit(actorData.getUid()));
        vo.setLossAmount(gamblingGameRedis.getUserLoss(actorData.getUid()));
        return vo;
    }

    @Cacheable(value = "queryUserGameDataCache", key = "#p0.rid", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public QueryGameVO queryUserGameData(AdminDTO dto) {
        ActorData actorData = paramCheck(dto.getRid());
        int endTime = DateHelper.getNowSeconds();
        int sevenStartTime = endTime - (int) TimeUnit.DAYS.toSeconds(7);
        int thirdStartTime = endTime - (int) TimeUnit.DAYS.toSeconds(30);
        QueryGameVO vo = new QueryGameVO();
        vo.setProfitLoss7(slaveMoneyDetailDao.getTotalBeanByTypeList(actorData.getUid(), QUERY_GAME_TYPE_LIST, sevenStartTime, endTime));
        vo.setProfitLoss30(slaveMoneyDetailDao.getTotalBeanByTypeList(actorData.getUid(), QUERY_GAME_TYPE_LIST, thirdStartTime, endTime));
        vo.setProfitAmount(gamblingGameRedis.getUserProfit(actorData.getUid()));
        vo.setLossAmount(gamblingGameRedis.getUserLoss(actorData.getUid()));
        return vo;
    }

    public ActorData paramCheck(String reqRid) {
        if (!StringUtils.hasLength(reqRid)) {
            logger.error("rid is empty.");
            throw new AdminCommonException(AdminHttpCode.PARAM_ERROR);
        }
        int rid = Integer.parseInt(reqRid);
        ActorData actorData = actorDao.getActorByRid(rid);
        if (actorData == null) {
            logger.info("can not find actor data. rid={}", reqRid);
            throw new AdminCommonException(AdminHttpCode.USER_NOT_EXIST);
        }
        return actorData;
    }

    private String getIpCountry(String ip) {
        QueryCountryByIpDTO qDTO = new QueryCountryByIpDTO();
        qDTO.setIp(ip);
        qDTO.setOutId("");
        QueryCountryVO queryCountryVO = iSundryService.queryCountryByIp(qDTO).getData();
        return queryCountryVO == null ? "" : queryCountryVO.getCountry();
    }

    private int getRoomAllowanceUser(String uid) {
        Object hashValue = clusterTemplate.opsForHash().get(getAllowanceKey(), uid);
        return hashValue == null ? 0 : 1;
    }

    public PageResultVO<MoneyDetailVO> searchBeansCost(BeansCostDTO dto) {
        PageResultVO<MoneyDetailVO> pageResultVO = new PageResultVO<>();
        ActorData actorData = paramCheck(dto.getRid());
        int page = dto.getPage() > 0 ? dto.getPage() : 1;
        int pageSize = 20;
        ApiResult<List<MoneyDetail>> result = dataCenterService.searchUserMoneyDetails(new DataCenterPageDTO(actorData.getUid(), dto.getFilter_beans(), dto.getFilter_type(), page, pageSize));
        List<MoneyDetail> moneyDetailsList = Collections.emptyList();
        if (result.isOk()) {
            moneyDetailsList = result.getData();
        }
        List<MoneyDetailVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(moneyDetailsList)) {
            for (MoneyDetail data : moneyDetailsList) {
                MoneyDetailVO moneyDetailVO = new MoneyDetailVO();
                moneyDetailVO.setAtype(data.getAtype());
                moneyDetailVO.setChanged(data.getChanged());
                moneyDetailVO.setMtime(data.getMtime());
                moneyDetailVO.setTitle(data.getTitle());
                moneyDetailVO.setDesc(data.getDesc());
                list.add(moneyDetailVO);
            }
        }
        pageResultVO.setList(list);
        pageResultVO.setPage(list.size() >= pageSize ? page + 1 : 0);
        return pageResultVO;
    }

    public PageResultVO<MoneyDetailVO> searchMoneyCharge(BeansCostDTO dto) {
        PageResultVO<MoneyDetailVO> pageResultVO = new PageResultVO<>();
        ActorData actorData = paramCheck(dto.getRid());
        int page = dto.getPage() > 0 ? dto.getPage() : 1;
        int pageSize = 20;
        int start = (page - 1) * pageSize;
        List<MoneyDetail> moneyDetailsList = rechargeMoneyDetailDao.selectPage(actorData.getUid(), start, pageSize);
        List<MoneyDetailVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(moneyDetailsList)) {
            for (MoneyDetail data : moneyDetailsList) {
                MoneyDetailVO moneyDetailVO = new MoneyDetailVO();
                moneyDetailVO.setAtype(data.getAtype());
                moneyDetailVO.setChanged(data.getChanged());
                moneyDetailVO.setMtime(data.getMtime());
                moneyDetailVO.setTitle(data.getTitle());
                moneyDetailVO.setDesc(data.getDesc());
                list.add(moneyDetailVO);
            }
        }
        pageResultVO.setList(list);
        pageResultVO.setPage(list.size() >= pageSize ? page + 1 : 0);
        return pageResultVO;
    }

    public JSONObject allRecharge(String rid) {
        ActorData actorData = paramCheck(rid);
        JSONObject jsonObject = new JSONObject();
        int endTime = DateHelper.getNowSeconds();
        int startTime = endTime - (int) TimeUnit.DAYS.toSeconds(30);
        jsonObject.put("all_recharge", rechargeMoneyDetailDao.getTotalRechargeBeans(actorData.getUid(), startTime, endTime));
        return jsonObject;
    }

    public PageResultVO<MoneyDetailVO> searchNoHonorCharge(BeansCostDTO dto) {
        PageResultVO<MoneyDetailVO> pageResultVO = new PageResultVO<>();
        ActorData actorData = paramCheck(dto.getRid());
        int page = dto.getPage() > 0 ? dto.getPage() : 1;
        int pageSize = 20;
        DataCenterPageDTO searchDto = new DataCenterPageDTO();
        searchDto.setUid(actorData.getUid());
        searchDto.setaTypeList(Arrays.asList(5, 72, 104, 108, 200, 202, 401, 402, 501, 502, 901, 905, 922, 962, 908, 913, 965));
        searchDto.setFilterType(1);
        searchDto.setPage(page);
        searchDto.setSize(pageSize);
        ApiResult<List<MoneyDetail>> apiResult = dataCenterService.searchUserMoneyDetails(searchDto);
        List<MoneyDetail> moneyDetailsList = apiResult.isOk() ? apiResult.getData() : Collections.emptyList();
        List<MoneyDetailVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(moneyDetailsList)) {
            for (MoneyDetail data : moneyDetailsList) {
                MoneyDetailVO moneyDetailVO = new MoneyDetailVO();
                moneyDetailVO.setAtype(data.getAtype());
                moneyDetailVO.setChanged(data.getChanged());
                moneyDetailVO.setMtime(data.getMtime());
                moneyDetailVO.setTitle(data.getTitle());
                moneyDetailVO.setDesc(data.getDesc());
                list.add(moneyDetailVO);
            }
        }
        pageResultVO.setList(list);
        pageResultVO.setPage(list.size() >= pageSize ? page + 1 : 0);
        logger.info("searchNoHonorCharge--> nextPage:{} list.size:{}", page, list.size());
        return pageResultVO;
    }

    public ConsumeTotalVO searchConsumeTotal(BeansCostDTO dto) {
        ActorData actorData = paramCheck(dto.getRid());
        int nowSeconds = DateHelper.getNowSeconds();
        MoneyTypeDTO moneyTypeDTO = new MoneyTypeDTO();
        moneyTypeDTO.setUid(actorData.getUid());
        moneyTypeDTO.setType(-1);
        moneyTypeDTO.setaTypeList(CONSUME_ACT_TYPE_LIST);
        moneyTypeDTO.setStartTime(nowSeconds - (int) TimeUnit.DAYS.toSeconds(2));
        moneyTypeDTO.setEndTime(nowSeconds);
        ApiResult<Long> apiResult = dataCenterService.userTotalBeans(moneyTypeDTO);
        long costBean = apiResult.isOk() ? apiResult.getData() : 0L;
        ConsumeTotalVO.Data data = new ConsumeTotalVO.Data();
        data.setActor_id(actorData.getUid());
        data.setDiamonds(costBean);
        data.setBouns(getBouns(costBean));
        return new ConsumeTotalVO(data);
    }

    private float getBouns(long diamonds) {
        int[] breakpoints = new int[]{20000, 40000, 60000, 100000, 150000, 200000, 250000, 300000, 500000, 1000000, 1500000};
        float[] grades = new float[]{0.1f, 0.12f, 0.15f, 0.2f, 0.23f, 0.25f, 0.28f, 0.3f, 0.35f, 0.4f, 0.45f};
        float bouns = 0f;
        for (int i = breakpoints.length - 1; i >= 0; i--) {
            if (diamonds >= breakpoints[i]) {
                bouns = grades[i];
                break;
            }
        }
        return bouns;
    }

    /**
     * 设置房间配置开关
     */
    public void setRoomConfigSwitch(AdminV2DTO req) {
        String uid = req.getUid();
        String configName = req.getConfigName();
        String aid = req.getAid();
        if (ObjectUtils.isEmpty(configName) || ObjectUtils.isEmpty(aid)) {
            throw new AdminCommonException(AdminHttpCode.PARAM_ERROR);
        }

        int configValue = req.getConfigValue();
        actorConfigDao.updateRoomConfig(aid, configName, configValue);
    }

    /**
     * 修改性别
     */
    public void modifyGender(AdminV2DTO req) {
        if (riskAdminUserRedis.isBlockAdminUser(req.getUid())) {
            logger.info("risk admin uid:{}", req.getUid());
            throw new AdminCommonException(AdminHttpCode.TRY_AGAIN_LATER);
        }

        String opUid = req.getUid();
        String aid = req.getAid();
        Integer newGender = req.getGender(); // 使用configValue作为新性别值

        // 参数验证
        if (ObjectUtils.isEmpty(aid)) {
            logger.error("aid is empty");
            throw new AdminCommonException(AdminHttpCode.PARAM_ERROR);
        }
        if (newGender == null || (newGender != 1 && newGender != 2)) {
            logger.error("gender is invalid: {}, must be 1(male) or 2(female)", newGender);
            throw new AdminCommonException(AdminHttpCode.PARAM_ERROR);
        }

        // 查找用户
        ActorData actorData = actorDao.getActorData(aid);
        if (actorData == null) {
            logger.error("can not find actor data. aid={}", aid);
            throw new AdminCommonException(AdminHttpCode.USER_NOT_EXIST);
        }

        int currentGender = actorData.getFb_gender();
        if (currentGender == newGender) {
            logger.info("user gender is already {}, no need to change. aid={}", newGender, aid);
            return; // 性别相同，无需修改
        }

        // 更新用户性别
        actorDao.updateField(aid, "fb_gender", newGender);

        // 检查并处理VIP相关的性别限制
        VipV2BuyDTO vipV2BuyDTO = new VipV2BuyDTO();
        vipV2BuyDTO.setUid(aid);
        vipV2BuyDTO.setNewGender(newGender);
        iVIPService.vipChange(vipV2BuyDTO);

        // 记录管理员操作日志
        String logDesc = String.format("change gender from %s to %s", currentGender, newGender);
        adminHandleLogDao.insert(new AdminHandleLogData(aid, opUid, ADMIN_LOG_MODIFY_GENDER.getCode(), logDesc));
        logger.info("modify gender success. opUid={} aid={} oldGender={} newGender={}", opUid, aid, currentGender, newGender);
    }

    /**
     * 赠送vip卡
     */
    public void sendVipCard(SendVipCardDTO req) {
        if (riskAdminUserRedis.isBlockAdminUser(req.getUid())) {
            logger.info("risk admin uid:{}", req.getUid());
            throw new AdminCommonException(AdminHttpCode.TRY_AGAIN_LATER);
        }

        String opUid = req.getUid();
        String aid = req.getAid();
        int vipLevel = req.getVipLevel();
        int vipDay = req.getVipDay();

        // 参数验证
        if (ObjectUtils.isEmpty(aid)) {
            logger.error("aid is empty");
            throw new AdminCommonException(AdminHttpCode.PARAM_ERROR);
        }
        if (vipLevel <= 0 || vipLevel > 10) {
            logger.error("vipLevel is invalid: {}", vipLevel);
            throw new AdminCommonException(AdminHttpCode.PARAM_ERROR);
        }

        // VIP天数限制：只允许1、3、7、10、15、30天
        if (!ALLOWED_VIP_DAYS.contains(vipDay)) {
            logger.error("vipDay is invalid: {}, allowed values: {}", vipDay, ALLOWED_VIP_DAYS);
            throw new AdminCommonException(AdminHttpCode.PARAM_ERROR);
        }

        // 查找用户
        ActorData actorData = actorDao.getActorData(aid);
        if (actorData == null) {
            logger.error("can not find actor data. aid={}", aid);
            throw new AdminCommonException(AdminHttpCode.USER_NOT_EXIST);
        }

        // 查找对应VIP等级的VIP卡资源ID
        // 这里需要根据VIP等级找到对应的VIP卡资源ID
        // 假设VIP卡资源ID的规则是: vipLevel + 1000 (这个需要根据实际的资源配置来确定)
        int vipCardResourceId = getVipCardResourceId(vipLevel);
        if (vipCardResourceId == 0) {
            logger.error("can not find vip card resource for vipLevel: {}", vipLevel);
            throw new AdminCommonException(AdminHttpCode.PARAM_ERROR);
        }

        // 使用资源服务下发VIP卡
        ResourcesDTO resourcesDTO = new ResourcesDTO();
        resourcesDTO.setUid(aid);
        resourcesDTO.setResId(String.valueOf(vipCardResourceId));
        resourcesDTO.setResType(BaseDataResourcesConstant.TYPE_VIP_CARD);
        resourcesDTO.setDesc("admin send vip card");
        resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_GET);
        resourcesDTO.setOfficialMsg(1);
        resourcesDTO.setmTime(DateHelper.getNowSeconds());
        resourcesDTO.setGetWay(BaseDataResourcesConstant.TYPE_ADMIN_GET);
        resourcesDTO.setNum(1);
        resourcesDTO.setDays(vipDay);

        ApiResult<String> result = dataResourcesService.handleRes(resourcesDTO);
        if (result.isError()) {
            logger.error("send vip card failure. vipLevel={} vipDay={} toUid={} code={} msg={}", vipLevel, vipDay, aid, result.getCode(), result.getCode().getMsg());
            throw new AdminCommonException(AdminHttpCode.SERVER_ERROR);
        }

        // 记录管理员操作日志
        String logDesc = String.format("发送VIP%d卡 %d天 给用户 %s", vipLevel, vipDay, actorData.getStrRid());
        adminHandleLogDao.insert(new AdminHandleLogData(aid, opUid, ADMIN_LOG_SEND_VIP_CARD.getCode(), logDesc));
        logger.info("send vip card success. opUid={} aid={} vipLevel={} vipDay={}", opUid, aid, vipLevel, vipDay);
    }

    /**
     * 根据VIP等级获取对应的VIP卡资源ID
     * 这个方法从数据库中动态获取VIP卡资源配置
     */
    private int getVipCardResourceId(int vipLevel) {
        try {
            // 获取所有VIP卡类型的资源配置
            List<ResourceConfigData> vipCardConfigs = resourceConfigDao.getResourceAllListFromDb(BaseDataResourcesConstant.TYPE_VIP_CARD);

            if (CollectionUtils.isEmpty(vipCardConfigs)) {
                logger.error("No VIP card resource config found in database");
                return 0;
            }

            // 在VIP卡配置中查找对应VIP等级的资源
            // redundantField字段存储VIP等级
            for (ResourceConfigData config : vipCardConfigs) {
                if (config.getRedundantField() == vipLevel && config.getStatus() == 1) {
                    logger.debug("Found VIP card resource: vipLevel={}, resourceId={}", vipLevel, config.getResourceId());
                    return config.getResourceId();
                }
            }

            logger.warn("No VIP card resource found for vipLevel: {}", vipLevel);
            return 0;
        } catch (Exception e) {
            logger.error("Error getting VIP card resource ID for vipLevel: {}", vipLevel, e);
            return 0;
        }
    }

    /**
     * 分页查询VIP信息、vipCard信息
     */
    public VipCardInfoPageVO vipInfoAndVipCard(VipCardInfoDTO req) {
        String opUid = req.getUid();
        String aid = req.getAid();
        int page = req.getPage() > 0 ? req.getPage() : 1;
        int type = req.getType(); // 0: 查询vip  1: 查询可激活的vipCard  2: 查询已激活的vipCard 3: 查询已过期的vipCard
        int pageSize = 20;
        logger.info("vipInfoAndVipCard aid={} type={} page={} opUid={}", aid, type, page, opUid);
        // 参数校验
        if (ObjectUtils.isEmpty(aid)) {
            throw new AdminCommonException(AdminHttpCode.PARAM_ERROR.getCode(), "aid不能为空");
        }

        // 验证用户是否存在
        ActorData actorData = actorDao.getActorData(aid);
        if (actorData == null) {
            logger.error("can not find actor data. aid={}", aid);
            throw new AdminCommonException(AdminHttpCode.USER_NOT_EXIST);
        }
        VipCardInfoPageVO result = new VipCardInfoPageVO();
        // 1. 查询用户当前VIP信息
        if (type == 0) {
            // 只查询VIP信息，不查询VIP卡
            UserVipInfoVO userVipInfo = getUserVipInfo(aid);
            result.setUserVipInfo(userVipInfo);
            result.setVipCardList(Collections.emptyList());
            result.setPage(0);
        } else {
            // 查询VIP卡信息
            List<VipCardInfoVO> vipCardList = getVipCardList(aid, type, getVipCardStatus(type), page, pageSize);
            // 设置分页信息
            result.setVipCardList(vipCardList);
            result.setPage(!CollectionUtils.isEmpty(vipCardList) && vipCardList.size() >= pageSize ? page + 1 : 0);
        }
        // 记录管理员操作日志
        if (page == 1) {
            String logDesc = String.format("查询用户 %s 的VIP及VIP卡信息", actorData.getStrRid());
            adminHandleLogDao.insert(new AdminHandleLogData(aid, opUid, ADMIN_LOG_QUERY_VIP_INFO.getCode(), logDesc));
        }
        logger.info("vipInfoAndVipCard success aid={} type={} page={} resultSize={}", aid, type, page, result.getVipCardList().size());
        return result;
    }

    private int getVipCardStatus(int type) {
        return type - 1;
    }

    /**
     * 获取用户VIP信息
     */
    private UserVipInfoVO getUserVipInfo(String aid) {
        UserVipInfoVO userVipInfo = new UserVipInfoVO();
        userVipInfo.setUid(aid);
        VipUserInfoData vipInfo = vipInfoDao.findVipInfoByUid(aid);
        int currentTime = DateHelper.getNowSeconds();
        if (vipInfo != null) {
            int vipBuyTime = (int) (vipInfo.getVipBuyTime().getTime() / 1000);
            int vipEndTime = (int) (vipInfo.getVipEndTime().getTime() / 1000);
            if (vipEndTime > currentTime) {
                userVipInfo.setVipLevel(vipInfo.getVipLevel());
                userVipInfo.setVipBuyTime(vipBuyTime);
                userVipInfo.setVipEndTime(vipEndTime);
                userVipInfo.setVipSource(vipInfo.getVipSource());
                userVipInfo.setVipDay((vipEndTime - vipBuyTime) / 86400);
                return userVipInfo;
            }
        }
        return null;
    }

    /**
     * 获取VIP卡列表
     */
    private List<VipCardInfoVO> getVipCardList(String aid, int type, int status, int page, int pageSize) {
        int start = (page - 1) * pageSize;
        List<UserResourceData> vipCardList = userResourceDao.selectPageByStatus(aid, BaseDataResourcesConstant.TYPE_VIP_CARD, status, start, pageSize);
        if (CollectionUtils.isEmpty(vipCardList)) {
            return Collections.emptyList();
        }

        // 获取VIP卡配置信息映射
        List<ResourceConfigData> resConfigDataList = resourceConfigDao.getResourceAllListFromDb(BaseDataResourcesConstant.TYPE_VIP_CARD);
        Map<Integer, ResourceConfigData> resConfigDataMap = resConfigDataList.stream().collect(Collectors.toMap(ResourceConfigData::getResourceId, Function.identity()));
        List<VipCardInfoVO> result = new ArrayList<>();
        int currentTime = DateHelper.getNowSeconds();
        for (UserResourceData vipCard : vipCardList) {
            // 根据类型过滤
            if (!isVipCardMatchType(vipCard, type, currentTime)) {
                continue;
            }

            ResourceConfigData resConfig = resConfigDataMap.get(vipCard.getResourceId());
            if (resConfig == null) {
                logger.error("Resource config not found for resourceId: {}", vipCard.getResourceId());
                return null;
            }

            VipCardInfoVO item = new VipCardInfoVO();
            item.setId(vipCard.getId());
            item.setResourceId(vipCard.getResourceId());
            item.setVipLevel(resConfig.getRedundantField());
            item.setVipCardName(resConfig.getName());
            item.setVipCardDay(vipCard.getResourceNumber());
            item.setStatus(vipCard.getStatus());
            item.setExpireTime((int) vipCard.getEndTime());
            item.setResourceOrigin(vipCard.getResourceOrigin());
            item.setGetTime(vipCard.getCtime());
            result.add(item);
        }
        return result;
    }

    /**
     * 判断VIP卡是否匹配查询类型
     */
    private boolean isVipCardMatchType(UserResourceData vipCard, int type, int currentTime) {
        switch (type) {
            case 1: // 查询可激活的vipCard
                return vipCard.getStatus() == 0 && vipCard.getEndTime() > currentTime;
            case 2: // 查询已激活的vipCard
                return vipCard.getStatus() == 1;
            case 3: // 查询已过期的vipCard
                return vipCard.getStatus() == 2 || (vipCard.getStatus() == 0 && vipCard.getEndTime() <= currentTime);
            default:
                return true; // 默认返回所有
        }
    }

    /**
     * 移除用户VIP
     */
    public void removeVip(AdminDTO req) {
        if (riskAdminUserRedis.isBlockAdminUser(req.getUid())) {
            logger.info("risk admin uid:{}", req.getUid());
            throw new AdminCommonException(AdminHttpCode.TRY_AGAIN_LATER);
        }

        String opUid = req.getUid();
        String aid = req.getAid();

        // 参数验证
        if (ObjectUtils.isEmpty(aid)) {
            logger.error("aid is empty");
            throw new AdminCommonException(AdminHttpCode.PARAM_ERROR);
        }

        // 查找用户
        ActorData actorData = actorDao.getActorDataFromCache(aid);
        if (actorData == null) {
            logger.error("can not find actor data. aid={}", aid);
            throw new AdminCommonException(AdminHttpCode.USER_NOT_EXIST);
        }

        // 查询当前VIP信息
        VipUserInfoData vipInfo = vipInfoDao.findVipInfoByUid(aid);
        if (vipInfo == null) {
            logger.error("user has no vip info aid={}", aid);
            throw new AdminCommonException(AdminHttpCode.PARAM_ERROR.getCode(), "User has no vip");
        }

        // 检查并处理VIP相关的性别限制
        VipV2BuyDTO vipV2BuyDTO = new VipV2BuyDTO();
        vipV2BuyDTO.setUid(aid);
        iVIPService.vipRemove(vipV2BuyDTO);
        // 记录管理员操作日志
        String logDesc = String.format("移除用户 %s 的VIP%d权限", actorData.getStrRid(), vipInfo.getVipLevel());
        adminHandleLogDao.insert(new AdminHandleLogData(aid, opUid, ADMIN_LOG_REMOVE_VIP.getCode(), logDesc));
        logger.info("remove vip success. opUid={} aid={} vipLevel={}", opUid, aid, vipInfo.getVipLevel());
    }

    /**
     * 赠送用户VIP
     */
    public void chargeVip(AdminDTO req) {
        if (riskAdminUserRedis.isBlockAdminUser(req.getUid())) {
            logger.info("risk admin uid:{}", req.getUid());
            throw new AdminCommonException(AdminHttpCode.TRY_AGAIN_LATER);
        }

        String opUid = req.getUid();
        String aid = req.getAid();
        int vipLevel = req.getVipLevel();
        Integer freeDiamond = req.getFreeDiamond();

        // 参数验证
        if (ObjectUtils.isEmpty(aid)) {
            throw new AdminCommonException(AdminHttpCode.PARAM_ERROR.getCode(), "aid is null");
        }

        if (vipLevel <= 0) {
            throw new AdminCommonException(AdminHttpCode.PARAM_ERROR.getCode(), "vipLevel can not be null or less than 0");
        }
        if (freeDiamond == null || freeDiamond < 0) {
            throw new AdminCommonException(AdminHttpCode.PARAM_ERROR.getCode(), "freeDiamond can not be null or less than 0");
        }

        // 查找用户
        ActorData actorData = actorDao.getActorData(aid);
        if (actorData == null) {
            throw new AdminCommonException(AdminHttpCode.USER_NOT_EXIST);
        }

        if (vipLevel == VipFeatureConstant.VIP_LEVEL_QUEEN && actorData.getFb_gender() == 1) {
            throw new AdminCommonException(AdminHttpCode.PARAM_ERROR.getCode(), "male user can not buy queen vip");
        }
        VipUserInfoData vipInfo = vipInfoDao.findVipInfoByUid(aid);
        int currentTime = DateHelper.getNowSeconds();
        if (vipInfo != null){
            int vipEndTime = (int) (vipInfo.getVipEndTime().getTime() / 1000);
            int leftTime = vipEndTime - currentTime;
            if (leftTime > 0) {
                int currentVipLevel = vipInfo.getVipLevel();
                if ((vipLevel <= VipFeatureConstant.VIP_LEVEL_6 && vipLevel < currentVipLevel) || (currentVipLevel == VipFeatureConstant.VIP_LEVEL_QUEEN && vipLevel <= VipFeatureConstant.VIP_LEVEL_4)) {
                    String message = String.format("You have activated VIP %s, you cannot purchase downgrade VIP", currentVipLevel);
                    throw new AdminCommonException(AdminHttpCode.PARAM_ERROR.getCode(), message);
                }
            }
        }
        long vipUpgrade = actorConfigDao.getLongUserConfig(aid, ActorConfigDao.VIP_UPGRADE, 0);
        VipV2ChargeDTO vipV2ChargeDTO = new VipV2ChargeDTO();
        vipV2ChargeDTO.setUid(aid);
        vipV2ChargeDTO.setVipLevel(vipLevel);
        vipV2ChargeDTO.setFreeDiamond(freeDiamond);
        if (vipUpgrade > 0) {
            ApiResult<VipV2BuyVO> result = iVIPService.vipCharge(vipV2ChargeDTO);
            logger.info("chargeVip vip result={}", JSON.toJSONString(result));
            if (result.isError()) {
                throw new AdminCommonException(result.getCode());
            }
        }else {
            ApiResult<?> result = iVIPService.vipChargeOld(vipV2ChargeDTO);
            logger.info("chargeVipOld vip result={}", JSON.toJSONString(result));
            if (result.isError()) {
                throw new AdminCommonException(result.getCode());
            }
        }

        // 检查并处理VIP相关的性别限制

        // 记录管理员操作日志
        String logDesc = String.format("charge vip %s", vipLevel);
        adminHandleLogDao.insert(new AdminHandleLogData(aid, opUid, ADMIN_LOG_GIVE_VIP.getCode(), logDesc));
        logger.info("chargeVip vip success. opUid={} aid={} vipLevel={}", opUid, aid, vipLevel);
    }

    /**
     * 删除用户VIP卡资源
     */
    public void deleteVipCard(DeleteVipCardDTO req) {
        if (riskAdminUserRedis.isBlockAdminUser(req.getUid())) {
            logger.info("risk admin uid:{}", req.getUid());
            throw new AdminCommonException(AdminHttpCode.TRY_AGAIN_LATER);
        }

        String opUid = req.getUid();
        String aid = req.getAid();
        Integer id = req.getId();
        Integer resourceId = req.getResourceId();

        // 参数验证
        if (ObjectUtils.isEmpty(aid)) {
            logger.error("aid is empty");
            throw new AdminCommonException(AdminHttpCode.PARAM_ERROR);
        }

        if (id == null || id <= 0) {
            logger.error("id is invalid: {}", id);
            throw new AdminCommonException(AdminHttpCode.PARAM_ERROR);
        }

        if (resourceId == null || resourceId <= 0) {
            logger.error("resourceId is invalid: {}", resourceId);
            throw new AdminCommonException(AdminHttpCode.PARAM_ERROR);
        }

        // 查找用户
        ActorData actorData = actorDao.getActorData(aid);
        if (actorData == null) {
            logger.error("can not find actor data. aid={}", aid);
            throw new AdminCommonException(AdminHttpCode.USER_NOT_EXIST);
        }

        // 查询用户是否拥有该VIP卡资源
        UserResourceData vipCard = userResourceDao.selectUserResourceById(aid, id);
        if (vipCard == null || vipCard.getResourceType() != BaseDataResourcesConstant.TYPE_VIP_CARD) {
            logger.error("user does not have this vip card resource. aid={}, resourceId={}", aid, resourceId);
            throw new AdminCommonException(AdminHttpCode.PARAM_ERROR.getCode(), "用户不拥有该VIP卡资源");
        }

        // 获取VIP卡配置信息
        ResourceConfigData resConfig = resourceConfigDao.getResourceDataFromCache(resourceId, BaseDataResourcesConstant.TYPE_VIP_CARD);
        if (resConfig == null) {
            logger.error("VIP card config not found for resourceId: {}", resourceId);
            throw new AdminCommonException(AdminHttpCode.PARAM_ERROR.getCode(), "VIP卡配置不存在");
        }

        // 删除用户VIP卡资源
        ResourcesDTO resourcesDTO = new ResourcesDTO();
        resourcesDTO.setUid(aid);
        resourcesDTO.setRecordId(String.valueOf(id));
        resourcesDTO.setResId(String.valueOf(resourceId));
        resourcesDTO.setResType(BaseDataResourcesConstant.TYPE_VIP_CARD);
        resourcesDTO.setDesc("admin send vip card");
        resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_DELETE);
        resourcesDTO.setOfficialMsg(1);
        resourcesDTO.setmTime(DateHelper.getNowSeconds());
        resourcesDTO.setGetWay(BaseDataResourcesConstant.TYPE_ADMIN_GET);
        resourcesDTO.setNum(1);
        resourcesDTO.setDays(0);
        ApiResult<String> result = dataResourcesService.handleRes(resourcesDTO);
        if (result.isError()) {
            throw new AdminCommonException(AdminHttpCode.SERVER_ERROR);
        }

        // 记录管理员操作日志
        String logDesc = String.format("删除用户 %s 的VIP%d卡资源(资源ID:%d)", actorData.getStrRid(), resConfig.getRedundantField(), resourceId);
        adminHandleLogDao.insert(new AdminHandleLogData(aid, opUid, ADMIN_LOG_DELETE_VIP_CARD.getCode(), logDesc));

        logger.info("delete vip card success. opUid={} aid={} resourceId={} vipLevel={}", opUid, aid, resourceId, resConfig.getRedundantField());
    }

}
