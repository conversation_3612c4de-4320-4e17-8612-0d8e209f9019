package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.quhong.analysis.EventReport;
import com.quhong.config.AsyncConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.OfficialDeleteData;
import com.quhong.enums.SLangType;
import com.quhong.feign.IMsgService;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.msg.chat.OfficialPushMsg;
import com.quhong.mysql.dao.ActorPayExternalDao;
import com.quhong.mysql.dao.DAUDao;
import com.quhong.redis.PlayerStatusRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.service.redis.RechargeRedis;
import com.quhong.utils.ActorUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class NoticePushService {
    private static final Logger logger = LoggerFactory.getLogger(NoticePushService.class);

    @Resource
    private IMsgService iMsgService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private ActorPayExternalDao actorPayExternalDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private PlayerStatusRedis playerStatusRedis;
    @Resource
    private OfficialPushDao officialPushDao;
    @Resource
    private OfficialDao officialDao;
    @Resource
    private NoticeNewDao noticeNewDao;
    @Resource
    protected RoomWebSender roomWebSender;
    @Resource
    private RechargeRedis rechargeRedis;
    @Resource
    private DAUDao dAUDao;
    @Resource
    private HomeBannerService homeBannerService;
    @Resource(name = AsyncConfig.ASYNC_TASK)
    private Executor executor;

    @PostConstruct
    public void postInit() {

    }

    public void officialMsgPush() {
        int currentTime = DateHelper.getNowSeconds();
        List<OfficialPushData> officialPushDataList = officialPushDao.findOfficialAliveData(currentTime);
        logger.info("officialMsgPush-->officialPushDataList size:{}",officialPushDataList.size() );
        if (!CollectionUtils.isEmpty(officialPushDataList)) {
            for (OfficialPushData item : officialPushDataList) {
                item.setFinish(1);
                officialPushDao.save(item);
                int push_user = item.getPush_user();
                switch (push_user) {
                    case 0:
                        executor.execute(() -> {
                            int pushCount = pushMsgPayUser(item);
                            logger.info("OfficialPushData item->{} pushCount:{}", JSON.toJSONString(item), pushCount);
                        });
                        break;
                    case 1:
                        executor.execute(() -> {
                            int pushCount = pushMsgNotPayUser(item);
                            logger.info("OfficialPushData item->{} pushCount:{}", JSON.toJSONString(item), pushCount);
                        });
                        break;
                    case 2:
                        executor.execute(() -> {
                            int pushCount = pushMsgFemaleUser(item);
                            logger.info("OfficialPushData item->{} pushCount:{}", JSON.toJSONString(item), pushCount);
                        });
                        break;
                    case 3:
                        executor.execute(() -> {
                            int pushCount = pushMsgDailyRegister(item, 1);
                            logger.info("OfficialPushData item->{} pushCount:{}", JSON.toJSONString(item), pushCount);
                        });
                        break;
                    case 4:
                        executor.execute(() -> {
                            int pushCount = pushMsgDailyRegister(item, 7);
                            logger.info("OfficialPushData item->{} pushCount:{}", JSON.toJSONString(item), pushCount);
                        });
                        break;
                    case 5:
                        executor.execute(() -> {
                            int pushCount =  pushMsgDailyAlive(item, 7);
                            logger.info("OfficialPushData item->{} pushCount:{}", JSON.toJSONString(item), pushCount);
                        });
                        break;
                    case 6:
                        executor.execute(() -> {
                            int pushCount = pushMsgDailyAlive(item, 30);
                            logger.info("OfficialPushData item->{} pushCount:{}", JSON.toJSONString(item), pushCount);
                        });
                        break;
                    case 7:
                        executor.execute(() -> {
                            int pushCount = pushMsgToUser(item);
                            logger.info("OfficialPushData item->{} pushCount:{}", JSON.toJSONString(item), pushCount);
                        });
                        break;
                    case 8:
                        executor.execute(() -> {
                            int pushCount = pushMsgToBCUser(item);
                            logger.info("OfficialPushData item->{} pushCount:{}", JSON.toJSONString(item), pushCount);
                        });
                        break;
                    case 9:
                        executor.execute(() -> {
                            int pushCount = pushMsgToCountryUsers(item);
                            logger.info("OfficialPushData item->{} pushCount:{}", JSON.toJSONString(item), pushCount);
                        });
                        break;
                }

            }
        }
    }

    private int pushMsgPayUser(OfficialPushData officialPushData) {
        int pushCount = 0;
        Set<String> allUsers = rechargeRedis.getAllRechargeUser();
        int nowTime = DateHelper.getNowSeconds();
        int thirdTime = nowTime - (int) TimeUnit.DAYS.toSeconds(30);
        for (String uid : allUsers) {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (actorData != null) {
                long lastLogoutTime = 0;
                if (actorData.getLastLogin() != null) {
                    if (actorData.getLastLogin().getLogoutTime() != null) {
                        lastLogoutTime = actorData.getLastLogin().getLogoutTime();
                    }
                }
                if (lastLogoutTime > thirdTime) {
                    writeOfficialMsg(officialPushData, uid, actorData.getSlang(), String.valueOf(actorData.getRid()));
                    pushCount++;
                }
            }
        }
        return pushCount;
    }

    private int pushMsgNotPayUser(OfficialPushData officialPushData) {
        int pushCount = 0;
        Set<String> allUsers = dAUDao.getActiveUserSet(30);
        for (String uid : allUsers) {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (actorData != null) {
                // 只有系统机器人不发，测试账号也得发
                if (actorData.getRobot() == 1 || rechargeRedis.isRechargeUserByCache(uid)) {
                    continue;
                }
                writeOfficialMsg(officialPushData, uid, actorData.getSlang(), String.valueOf(actorData.getRid()));
                pushCount++;
            }
        }
        allUsers.clear();
        return pushCount;
    }

    private int pushMsgFemaleUser(OfficialPushData officialPushData) {
        int pushCount = 0;
        Set<String> allUsers = dAUDao.getActiveUserSet(30);
        for (String uid : allUsers) {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (actorData != null) {
                // 只有系统机器人不发，测试账号也得发
                if (actorData.getRobot() == 1 || actorData.getFb_gender() == 1) {
                    continue;
                }
                writeOfficialMsg(officialPushData, uid, actorData.getSlang(), String.valueOf(actorData.getRid()));
                pushCount++;
            }
        }
        allUsers.clear();
        return pushCount;
    }

    private int pushMsgDailyRegister(OfficialPushData officialPushData, int day) {
        int pushCount = 0;
        List<LightweightActorData> actorDataList = actorDao.getLightweightNewUserActorListByPage(day, 0, -1,0, 20000);
        for (LightweightActorData actorData : actorDataList) {
            String uid = actorData.get_id().toString();
            writeOfficialMsg(officialPushData, uid, actorData.getSlang(), String.valueOf(actorData.getRid()));
            pushCount++;
        }
        actorDataList.clear();
        return pushCount;
    }

    private int pushMsgDailyAlive(OfficialPushData officialPushData, int day) {
        int pushCount = 0;
        day = Math.min(day, 30);
        Set<String> allUsers = dAUDao.getActiveUserSet(day);
        for (String uid : allUsers) {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (actorData != null) {
                // 只有系统机器人不发，测试账号也得发
                if (actorData.getRobot() == 1) {
                    continue;
                }
                writeOfficialMsg(officialPushData, uid, actorData.getSlang(), String.valueOf(actorData.getRid()));
                pushCount++;
            }
        }
        allUsers.clear();
        return pushCount;
    }

    private int pushMsgToUser(OfficialPushData officialPushData) {
        int pushCount = 0;
        String userRidListStr = officialPushData.getUser_rid();
        try {
            if (!StringUtils.isEmpty(userRidListStr)) {
                List<String> userRidList = Arrays.asList(userRidListStr.split(","));
                for (String rid : userRidList) {
                    ActorData actorData = actorDao.getActorByStrRid(rid);
                    if (actorData != null) {
                        writeOfficialMsg(officialPushData, actorData.getUid(), actorData.getSlang(), String.valueOf(actorData.getRid()));
                        pushCount++;
                    }
                }
            }
        } catch (Exception e) {
            logger.error("pushMsgToUser error: {}", e.getMessage(), e);
        }
        return pushCount;
    }

    private int pushMsgToBCUser(OfficialPushData officialPushData) {
        int pushCount = 0;
        Set<String> allUsers = dAUDao.getActiveUserSet(7);
        for (String uid : allUsers) {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (actorData != null) {
                // 只有系统机器人不发，测试账号也得发
                if (actorData.getRobot() == 1 || homeBannerService.getBCGameSwitch(actorData) == 0) {
                    continue;
                }
                writeOfficialMsg(officialPushData, uid, actorData.getSlang(), String.valueOf(actorData.getRid()));
                pushCount++;
            }
        }
        allUsers.clear();
        return pushCount;
    }

    private int pushMsgToCountryUsers(OfficialPushData officialPushData) {
        int pushCount = 0;
        Set<String> allUsers = dAUDao.getActiveUserSet(7);
        String filterItem = officialPushData.getUser_rid(); // 使用user_rid字段存储国家码列表
        for (String uid : allUsers) {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (actorData != null) {
                // 只有系统机器人不发，测试账号也得发
                // if (actorData.getRobot() == 1) {
                //     continue;
                // }
                // 过滤国家 - 如果过滤条件为空或用户国家为空则不推送
                if (StringUtils.isEmpty(filterItem) || StringUtils.isEmpty(actorData.getCountry())) {
                    continue;
                }
                String[] countryArray = filterItem.split(",");
                List<String> countryList = Arrays.asList(countryArray);
                String countryCode = ActorUtils.getUpperCaseCountryCode(actorData.getCountry());
                if (StringUtils.isEmpty(countryCode) || !countryList.contains(countryCode)) {
                    continue;
                }
                writeOfficialMsg(officialPushData, uid, actorData.getSlang(), String.valueOf(actorData.getRid()));
                pushCount++;
            }
        }
        allUsers.clear();
        return pushCount;
    }


    private void writeOfficialMsg(OfficialPushData officialPushData, String uid, int slang, String rid) {
        int nowTime = DateHelper.getNowSeconds();
        String officialPushId = officialPushData.get_id().toString();
        OfficialData officialData = officialDao.findByOfficialPushIdData(officialPushId, uid);
        if (officialData == null) {
            int newsType = officialPushData.getNews_type() == 4 ? 4 : 0;
            String pushUrl = !StringUtils.isEmpty(officialPushData.getUrl()) ? officialPushData.getUrl() : "";
            pushUrl = pushUrl.replace("require_rid", rid);
            String title = officialPushData.getTitle_ar();
            String body = officialPushData.getBody_ar();
            String picture = officialPushData.getPicture_ar();
            String actText = officialPushData.getAct_ar();
            if (slang == SLangType.ENGLISH) {
                title = officialPushData.getTitle();
                body = officialPushData.getBody();
                picture = officialPushData.getPicture();
                actText = officialPushData.getAct();
            }

            officialData = new OfficialData();
            officialData.setTitle(title);
            officialData.setBody(body);
            officialData.setPicture(picture);
            officialData.setWidth(officialPushData.getWidth());
            officialData.setHeight(officialPushData.getHeight());
            officialData.setSubTitle(officialPushData.getSubtitle());
            officialData.setAct(actText);
            officialData.setUrl(pushUrl);
            officialData.setTo_uid(uid);
            officialData.setCtime(nowTime);
            officialData.setOfficial_push_id(officialPushId);
            officialData.setAtype(officialPushData.getAtype());
            officialData.setNews_type(newsType);
            officialData.setRoom_id(officialPushData.getRoom_id());
            officialData.setNtype(officialPushData.getNtype());
            officialData.setValid(1);
            officialDao.save(officialData);

            if (officialData.get_id() != null) {
                NoticeNewData noticeNewData = new NoticeNewData();
                noticeNewData.setAid(uid);
                noticeNewData.setOfficial_id(officialData.get_id().toString());
                noticeNewData.setCtime(nowTime);
                noticeNewData.setStatus(1);
                noticeNewData.setNtype(officialPushData.getNtype());
                noticeNewDao.save(noticeNewData);
                OfficialPushMsg msg = new OfficialPushMsg();
                msg.setTitle(title);
                msg.setBody(body);
                msg.setIcon(picture);
                msg.setMsg_type(officialPushData.getNtype());
                roomWebSender.sendPlayerWebMsg("", uid, uid, msg, false);
            }
        }
    }


    public void onClearOfficialPushTick2() {
        long millis = System.currentTimeMillis();
        int endTime = DateHelper.getNowSeconds() - 15 * 86400;

        // 分批处理，避免一次性加载大量数据
        final int BATCH_SIZE = 2000; // 每批处理2000条
        int processedCount = 0;
        int totalProcessed = 0;

        logger.info("onClearOfficialPushTick start.");
        int count = 0;
        do {
            // 分批获取数据
            List<OfficialDeleteData> officialDataList = officialDao.getLastDayOfficialDataList(endTime, BATCH_SIZE);
            if (CollectionUtils.isEmpty(officialDataList)) {
                break;
            }
            processedCount = officialDataList.size();
            totalProcessed += processedCount;

            logger.info("Processing batch: {} records", processedCount);

            // 使用Stream避免创建中间List，减少内存占用
            try {
                if (count >= 10000) {
                    logger.info("break onClearOfficialPushTick count={}", count);
                    break;
                }
                List<String> objIdList = officialDataList.parallelStream()
                        .map(item -> item.get_id().toString())
                        .collect(Collectors.toList());
                // 批量删除
                officialDao.clearOfficialMsg(objIdList);
                noticeNewDao.clearNoticeNewMsg(objIdList);
                // 显式清理引用，帮助GC
                objIdList.clear();
                officialDataList.clear();
                count++;
            } catch (Exception e) {
                logger.error("Error processing batch, endTime={}, batchSize={}, error={}",
                        endTime, processedCount, e.getMessage(), e);
                count++;
                // 继续处理下一批，不因单批失败而中断整个流程
            }

        } while (processedCount == BATCH_SIZE); // 如果返回的数据少于BATCH_SIZE，说明已经处理完了
        logger.info("onClearOfficialPushTick completed. totalProcessed={}, timeMillis={}", totalProcessed, System.currentTimeMillis() - millis);
    }
}
