package com.quhong.service;

import com.quhong.core.utils.DateHelper;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.FamilyDevoteDao;
import com.quhong.mysql.dao.FamilyDao;
import com.quhong.mysql.dao.FamilyMemberDao;
import com.quhong.mysql.data.FamilyData;
import com.quhong.mysql.data.FamilyMemberData;
import com.quhong.redis.FamilyTaskRedis;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.utils.MatchUtils;
import com.quhong.vo.FamilyRankingVO;
import com.quhong.vo.MemberRankingVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;

@Lazy
@Service
public class FamilyRankService {

    private static final Logger logger = LoggerFactory.getLogger(FamilyRankService.class);
    private static final String DEFAULT_ANNOUNCE = "This family has not written anything.";
    private static final String DEFAULT_ANNOUNCE_AR = "هذه العائلة لم تكتب أي شيء.";

    @Resource
    private FamilyDao familyDao;
    @Resource
    private FamilyMemberDao familyMemberDao;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private FamilyDevoteDao familyDevoteDao;
    @Resource
    private FamilyTaskRedis familyTaskRedis;

    private String getAnnounce(String announce, int slang) {
        if (!ObjectUtils.isEmpty(announce)) {
            return announce;
        }
        return SLangType.ENGLISH == slang ? DEFAULT_ANNOUNCE : DEFAULT_ANNOUNCE_AR;
    }

    public FamilyRankingVO familyRanking(String uid, String startDate, String endDate, int slang) {
        FamilyRankingVO vo = new FamilyRankingVO();
        FamilyMemberData familyMemberData = familyMemberDao.selectByUid(uid);
        List<FamilyDevoteDao.DevoteRanking> devoteRanking = familyDevoteDao.getFamilyDevoteRanking(startDate, endDate);
        for (int i = 0; i < devoteRanking.size(); i++) {
            FamilyDevoteDao.DevoteRanking ranking = devoteRanking.get(i);
            FamilyRankingVO.RankingVO rankingVO = getFamilyRankingVO(ranking.getFamilyId(), String.valueOf(i + 1), ranking.getTotalDevote(), slang);
            if (null != familyMemberData && familyMemberData.getFamilyId().equals(ranking.getFamilyId())) {
                vo.setMyRanking(rankingVO);
            }
            vo.getRankingList().add(rankingVO);
        }
        if (null == vo.getMyRanking() && null != familyMemberData) {
            int devote = familyDevoteDao.getFamilyDevote(familyMemberData.getFamilyId(), startDate, endDate);
            vo.setMyRanking(getFamilyRankingVO(familyMemberData.getFamilyId(), "30+", devote, slang));
        }
        // 家族任务，查看家族战力榜
        if (familyMemberDao.getAllMemberFromCache().getOrDefault(uid, 0) > 0) {
            familyTaskRedis.familyDevoteCheck(uid);
        }
        return vo;
    }


    private FamilyRankingVO.RankingVO getFamilyRankingVO(int familyId, String rank, int devote, int slang) {
        FamilyData familyData = familyDao.selectByIdFromCache(familyId);
        FamilyRankingVO.RankingVO rankingVO = new FamilyRankingVO.RankingVO();
        rankingVO.setRank(rank);
        rankingVO.setRid(familyData.getRid());
        rankingVO.setHead(ImageUrlGenerator.generateRoomUserUrl(familyData.getHead()));
        rankingVO.setName(familyData.getName());
        rankingVO.setAnnounce(getAnnounce(familyData.getAnnounce(), slang));
        rankingVO.setDevote(MatchUtils.formatDevotes(devote, RoundingMode.FLOOR));
        int members = familyMemberDao.selectMemberCountFromCache(familyId);
        rankingVO.setMemberNum(String.format("%s/%s", members, FamilyService.MAX_MEMBER));
        return rankingVO;
    }


    private MemberRankingVO.RankingVO getRankingVO(String aid, String rank, int devote) {
        RoomActorDetailData data = roomActorCache.getData(null, aid, false);
        FamilyMemberData memberData = familyMemberDao.selectByUidFromCache(data.getAid());
        MemberRankingVO.RankingVO rankingVO = new MemberRankingVO.RankingVO();
        rankingVO.setRank(rank);
        rankingVO.setRidData(data.getRidData());
        rankingVO.setAid(data.getAid());
        rankingVO.setAge(data.getAge());
        rankingVO.setHead(data.getHead());
        rankingVO.setName(data.getName());
        rankingVO.setGender(data.getGender());
        rankingVO.setDevote(MatchUtils.formatDevotes(devote, RoundingMode.FLOOR));
        rankingVO.setVipLevel(data.getVipLevel());
        rankingVO.setLevel(data.getLevel());
        rankingVO.setRole(memberData == null ? 0 : memberData.getRole());
        return rankingVO;
    }

    private String[] getStartEndDate(Integer rankType) {
        switch (rankType) {
            case 0:
                String currentDate = DateHelper.ARABIAN.formatDateInDay();
                return new String[]{currentDate, currentDate};
            case 1:
                String weekStartDate = DateHelper.ARABIAN.getWeekStartDate();
                String weekEndDate = DateHelper.ARABIAN.getWeekEndDate();
                return new String[]{weekStartDate, weekEndDate};
            case 2:
                LocalDate today = LocalDate.now(ZoneId.of("+3"));
                LocalDate monthStart = today.withDayOfMonth(1);
                String todayStr = today.toString();
                String monthStartStr = monthStart.toString();
                return new String[]{monthStartStr, todayStr};
            default:
                return new String[]{"-1", "-1"};
        }
    }

    public MemberRankingVO memberRanking(String uid, Integer familyRid, Integer rankType) {
        MemberRankingVO vo = new MemberRankingVO();
        FamilyData familyData = familyDao.selectByFamilyRid(familyRid);
        if (null == familyData) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        String[] startEndDateArray = getStartEndDate(rankType);
        String startDate = startEndDateArray[0];
        String endDate = startEndDateArray[1];
        logger.info("memberRanking startDate:{}, endDate: {}", startDate, endDate);

        List<FamilyDevoteDao.DevoteRanking> devoteRanking = familyDevoteDao.getMemberDevoteRanking(familyData.getId(), startDate, endDate);
        for (int i = 0; i < devoteRanking.size(); i++) {
            FamilyDevoteDao.DevoteRanking ranking = devoteRanking.get(i);
            MemberRankingVO.RankingVO rankingVO = getRankingVO(ranking.getUid(), String.valueOf(i + 1), ranking.getTotalDevote());
            if (uid.equals(ranking.getUid())) {
                if (i > 0) {
                    FamilyDevoteDao.DevoteRanking lastRanking = devoteRanking.get(i - 1);
                    int differ = lastRanking.getTotalDevote() - ranking.getTotalDevote();
                    rankingVO.setPreviousDiffer(MatchUtils.formatDevotes(differ, RoundingMode.FLOOR));
                }
                vo.setMyRanking(rankingVO);
            }
            vo.getRankingList().add(rankingVO);
        }

        FamilyMemberData familyMemberData = familyMemberDao.selectByUidFromCache(uid);
        if (null == vo.getMyRanking() && familyMemberData != null && Objects.equals(familyData.getId(), familyMemberData.getFamilyId())) {
            int devote;
            if ("-1".equals(startDate) && "-1".equals(endDate)) {
                devote = familyMemberData.getDevote().intValue();
            } else {
                devote = familyDevoteDao.getMemberDevote(uid, familyData.getId(), startDate, endDate);
            }
            MemberRankingVO.RankingVO myRankingVO = getRankingVO(uid, "30+", devote);
            if (!CollectionUtils.isEmpty(devoteRanking)) {
                FamilyDevoteDao.DevoteRanking lastRanking = devoteRanking.get(devoteRanking.size() - 1);
                if (lastRanking != null) {
                    int differ = lastRanking.getTotalDevote() - devote;
                    myRankingVO.setPreviousDiffer(MatchUtils.formatDevotes(differ, RoundingMode.FLOOR));
                }
            }
            vo.setMyRanking(myRankingVO);
        }
        return vo;
    }

}
