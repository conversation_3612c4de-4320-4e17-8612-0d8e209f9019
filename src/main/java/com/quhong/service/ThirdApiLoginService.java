package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken;
import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier;
import com.google.api.client.http.apache.v2.ApacheHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseToken;
import com.quhong.cache.CacheMap;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.LoginConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.web.HttpResponseData;
import com.quhong.core.web.WebClient;
import com.quhong.data.*;
import com.quhong.data.dto.ShuMeiDeviceDTO;
import com.quhong.datas.HttpResult;
import com.quhong.enums.PKGConstant;
import com.quhong.http.AsyncHttpClient;
import com.quhong.http.HttpCallBack;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.dao.TnMsgCacheDao;
import com.quhong.mysql.data.TnMsgCacheData;
import com.quhong.utils.AesUtilWithBase64;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.RequestUtils;
import com.quhong.utils.TnHttpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.ThreadLocalRandom;

@Service
public class ThirdApiLoginService {
    private static final Logger logger = LoggerFactory.getLogger(ThirdApiLoginService.class);
    public static final String FACEBOOK_TOKEN_URL = "https://graph.facebook.com/debug_token"; // https://graph.facebook.com/v14.0/debug_token
    public static final String FACE_BOOK_APP_ID = "1419033481465170";
    public static final String FACE_BOOK_KEY = "1a989224a726c484a9ff0a862e90c354";
    public static final String LITE_FACE_BOOK_APP_ID = "351801119475719";
    public static final String LITE_FACE_BOOK_KEY = "1f73f6f15e0b583546afbfc4c8e3d437";
    public static final String MEETPARTY_FACE_BOOK_APP_ID = "2850991048527310";
    public static final String MEETPARTY_FACE_BOOK_KEY = "a6e0b81c9287424e23f681874bd4e5ec";
    public static final String WALAA_FACE_BOOK_APP_ID = "545876646906140";
    public static final String WALAA_FACE_BOOK_KEY = "cb317590eed6e8d3170a5866b1d15ad6";
    public static final String LOGIN_WARN_NAME = "javaLogin";

    public static final String GOOGLE_TOKEN_URL = "https://www.googleapis.com/oauth2/v3/tokeninfo";
    public static final String HUAWEI_TOKEN_URL = "https://oauth-login.cloud.huawei.com/oauth2/v3/token";
    public static final String HUAWEI_INFO_URL = "https://oauth-api.cloud.huawei.com/rest.php?nsp_fmt=JSON&nsp_svc=huawei.oauth2.user.getTokenInfo";
    public static final String HUAWEI_APP_ID = "103185171";
    public static final String HUAWEI_APP_SECRET = "8e6f5617bc57260c6661019de1ae0b738d24f7f9d568122613cf3d7f76126240";

    public static final String TN_POST_URL = "https://www.turingfraud.net:30016/data/1943/forward";// http://119.28.244.210:30017/data/1943/forward  http://124.156.67.110:30017/data/1943/forward
    public static final String TN_POST_URL2 = "http://124.156.67.110:30017/data/1943/forward";// http://43.134.149.203:30017/data/1943/forward
    public static final String TN_APP_ID = "100004";
    public static final String TN_APP_SECRET = "i4Nxlw3HEj7iu3i3zDvPvCTr8VC4Auki";
    public static final String TN_APP_SIGN_KEY = "0eIEyUaO96rumdGG9Fgoel7cvvGMzgNg";

//    public static final String NEW_TN_APP_ID = "1306690798"; //waho渠道号1306690798
    // SecretId: IKIDZ730thS4eBgoLuSsKqCTfnxAYWNzeHl7
    // SecretKey: JSTUQWG3unmnpstmdN1fO7mIh5PvPp2E
//    public static final String NEW_TN_SECRET_ID = "IKIDKWTBfuvhKjvsa8TXP4UkmZ6RZWFnGZkj";
//    public static final String NEW_TN_SECRET_KEY = "pTxHQO9f441mJJ8mFXeYHpDMGwRluqb9";

    // 图灵顿 低配版
    public static final String NEW_TN_APP_ID = "1328627554"; // 渠道号1328627554
    public static final String NEW_TN_SECRET_ID = "IKIDmhheWEuQmv54rQo24weCfz60LXXMGDGQ";
    public static final String NEW_TN_SECRET_KEY = "FMIG3d1DCIahFeL7jMtGidPyMZcFi0nf";

    public static final String APPLE_AUTH_KEYS_URL = "https://appleid.apple.com/auth/keys";

    private static final long CACHE_TIME_MILLIS = 6 * 60 * 60 * 1000L;
    private static final int ALL = 1;

    public static final String SM_ACCESS_KEY = "iq7dSwA3HZaJ94jc9U2I";
    public static final String SM_ACCESS_URL = "http://api-tianxiang-eur.fengkongcloud.com/tianxiang/v4";

    private static final long CACHE_NOTICE_TIME_MILLIS = 10 * 60 * 1000L;

    @Autowired
    private MonitorSender monitorSender;
    @Autowired
    private WebClient webClient;
    @javax.annotation.Resource
    private AsyncHttpClient asyncHttpClient;
    @Autowired
    private TnHttpUtils tnHttpUtils;
    @javax.annotation.Resource
    private CacheLoginService cacheLoginService;
    @Autowired
    private TnMsgCacheDao tnMsgCacheDao;
    @javax.annotation.Resource
    protected LoginDataCountService loginDataCountService;


    private GoogleIdTokenVerifier localVerifier;

    private CacheMap<Integer, List<ApplePublicKeyData>> cacheMap;


    public ThirdApiLoginService() {
        cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    @PostConstruct
    public void postInit() {
        try {
            ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resolver.getResources("classpath:server_account.json");
            Resource resource = resources[0];
            FirebaseOptions options = FirebaseOptions.builder()
                    .setCredentials(GoogleCredentials.fromStream(resource.getInputStream()))
                    .build();
            FirebaseApp.initializeApp(options);

            localVerifier = new GoogleIdTokenVerifier.Builder(new ApacheHttpTransport(), new GsonFactory()).build();
            cacheMap.start();
        } catch (Exception e) {
            logger.error("error={}", e.getMessage(), e);
        }

    }

    private String getFbAccessToken(String pkgName) {
        String appId = FACE_BOOK_APP_ID;
        String appKey = FACE_BOOK_KEY;
        if (PKGConstant.ANDROID_YOUSTAR_LITE.equals(pkgName)) {
            appId = LITE_FACE_BOOK_APP_ID;
            appKey = LITE_FACE_BOOK_KEY;
        } else if (PKGConstant.ANDROID_YOUSTAR_MEET.equals(pkgName)) {
            appId = MEETPARTY_FACE_BOOK_APP_ID;
            appKey = MEETPARTY_FACE_BOOK_KEY;
        } else if (PKGConstant.IOS_WALAA.equals(pkgName)) {
            appId = WALAA_FACE_BOOK_APP_ID;
            appKey = WALAA_FACE_BOOK_KEY;
        }
        return appId + "%7C" + appKey;
    }

    public String getFaceBookUid(RegisterOrLoginContext context) {
        String path = "?access_token=" + getFbAccessToken(context.getApp_package_name()) + "&input_token=" + context.getLoginToken();
        String url = FACEBOOK_TOKEN_URL + path;
        HttpResponseData<String> responseData;
        try {
            HashMap<String, String> headerMap = new HashMap<>();
            headerMap.put("Connection", "close");
            responseData = webClient.sendGet(url, headerMap);
            if (responseData != null) {
                String body = responseData.getBody();
                if (responseData.getStatus() != WebClient.STATUS_OK) {
//                    monitorSender.info(LOGIN_WARN_NAME, "调用" + FACEBOOK_TOKEN_URL + "失败",
//                            "status=" + responseData.getStatus() + " body=" + responseData.getBody() +
//                                    " req=" + context);
                    logger.error("status is not ok. url={} status={} body={} req={}",
                            url, responseData.getStatus(), responseData.getBody(), context);
                    checkNoticeWarn(LoginConstant.FACE_BOOK_TYPE, context.getDistinct_id(), FACEBOOK_TOKEN_URL);
                    return null;
                } else {
                    if (StringUtils.isEmpty(body)) {
//                        monitorSender.info(LOGIN_WARN_NAME, "调用" + FACEBOOK_TOKEN_URL + "失败",
//                                "status=" + responseData.getStatus() + " body=" + responseData.getBody() +
//                                        " req=" + context);
                        logger.error("body is empty. url={} status={} body={} req={}",
                                url, responseData.getStatus(), responseData.getBody(), context);
                        checkNoticeWarn(LoginConstant.FACE_BOOK_TYPE, context.getDistinct_id(), FACEBOOK_TOKEN_URL);
                        return null;
                    }
                    HttpResult<FaceBookLoginVerifyData> result = JSON.parseObject(body, new TypeReference<HttpResult<FaceBookLoginVerifyData>>() {
                    });
                    FaceBookLoginVerifyData data = result.getData();
                    logger.info("info---, ioskey={} android_id={} FaceBookLoginVerifyData={}", context.getP3(), context.getAndroidid(), data);
                    String fbUid = data.getUser_id();
                    if (StringUtils.isEmpty(fbUid)) {
//                        monitorSender.info(LOGIN_WARN_NAME, "调用" + FACEBOOK_TOKEN_URL + "失败",
//                                "msg=fbUid is empty" + " body=" + responseData.getBody() +
//                                        " req=" + context);
                        logger.error("fbUid is empty. url={}  body={} req={}",
                                url, responseData.getBody(), context);
                    }
                    return fbUid;

                }
            } else {
                logger.error("send get form get. url={} context={} responseData is null", url, context);
//                monitorSender.info(LOGIN_WARN_NAME, "调用" + FACEBOOK_TOKEN_URL + "失败",
//                        "responseData is null url=" + url + " context=" + context);
                checkNoticeWarn(LoginConstant.FACE_BOOK_TYPE, context.getDistinct_id(), FACEBOOK_TOKEN_URL);
                return null;
            }

        } catch (Exception e) {
            logger.error("getFaceBookUid error, req={} error msg={}", context, e.getMessage(), e);
//            monitorSender.info(LOGIN_WARN_NAME, "调用" + FACEBOOK_TOKEN_URL + "失败",
//                    "msg=" + e.getMessage() + " context=" + context);
            checkNoticeWarn(LoginConstant.FACE_BOOK_TYPE, context.getDistinct_id(), FACEBOOK_TOKEN_URL);
            return null;
        }
    }

    public String getGoogleUid(RegisterOrLoginContext context) {
        String path = "?id_token=" + context.getLoginToken();
        String url = GOOGLE_TOKEN_URL + path;
        HttpResponseData<String> responseData;
        try {
            responseData = webClient.sendGet(url, null);
            if (responseData != null) {
                String body = responseData.getBody();
                if (responseData.getStatus() != WebClient.STATUS_OK) {
//                    monitorSender.info(LOGIN_WARN_NAME, "调用" + GOOGLE_TOKEN_URL + "失败",
//                            "status=" + responseData.getStatus() + " body=" + responseData.getBody() +
//                                    " req=" + context);
                    logger.error("status is not ok. url={} status={} body={} req={}",
                            url, responseData.getStatus(), responseData.getBody(), context);
                    checkNoticeWarn(LoginConstant.GOOGLE_TYPE, context.getDistinct_id(), GOOGLE_TOKEN_URL);
                    return null;
                } else {
                    if (StringUtils.isEmpty(body)) {
//                        monitorSender.info(LOGIN_WARN_NAME, "调用" + GOOGLE_TOKEN_URL + "失败",
//                                "status=" + responseData.getStatus() + " body=" + responseData.getBody() +
//                                        " req=" + context);
                        logger.error("body is empty. url={} status={} body={} req={}",
                                url, responseData.getStatus(), responseData.getBody(), context);
                        checkNoticeWarn(LoginConstant.GOOGLE_TYPE, context.getDistinct_id(), GOOGLE_TOKEN_URL);
                        return null;
                    }
                    GoogleLoginVerifyData data = JSON.parseObject(body, GoogleLoginVerifyData.class);
                    logger.info("info--, ioskey={} android_id={} GoogleLoginVerifyData={}", context.getP3(), context.getAndroidid(), data);
                    String gpUid = data.getSub();
                    if (StringUtils.isEmpty(gpUid)) {
//                        monitorSender.info(LOGIN_WARN_NAME, "调用" + GOOGLE_TOKEN_URL + "失败",
//                                "msg=gpUid is empty" + " body=" + responseData.getBody() +
//                                        " req=" + context);
                        logger.error("gpUid is empty. url={}  body={} req={}",
                                url, responseData.getBody(), context);
                    }
                    return gpUid;

                }
            } else {
                logger.error("send get form get. url={} context={} responseData is null", url, context);
//                monitorSender.info(LOGIN_WARN_NAME, "调用" + GOOGLE_TOKEN_URL + "失败",
//                        "responseData is null" + " req=" + url + " context=" + context);
                checkNoticeWarn(LoginConstant.GOOGLE_TYPE, context.getDistinct_id(), GOOGLE_TOKEN_URL);
                return null;
            }

        } catch (Exception e) {
            logger.error("getGoogleUid error, req={} error msg={}", context, e.getMessage(), e);
//            monitorSender.info(LOGIN_WARN_NAME, "调用" + GOOGLE_TOKEN_URL + "失败",
//                    "msg=" + e.getMessage() + " req=" + context);
            checkNoticeWarn(LoginConstant.GOOGLE_TYPE, context.getDistinct_id(), GOOGLE_TOKEN_URL);
            return null;
        }
    }

    public String getHuaWeiUid(RegisterOrLoginContext context) {
        HttpResponseData<String> responseData;
        try {
            Map<String, String> dataMap = new HashMap<>();
            dataMap.put("grant_type", "authorization_code");
            dataMap.put("client_id", HUAWEI_APP_ID);
            dataMap.put("client_secret", HUAWEI_APP_SECRET);
            dataMap.put("code", context.getLoginToken());
            dataMap.put("redirect_uri", "hms://redirect_url");
            responseData = webClient.sendPostWithHttpResp(HUAWEI_TOKEN_URL, dataMap, 1);
            if (responseData == null) {
                logger.error("send post. url={} dataMap={} context={}", HUAWEI_TOKEN_URL, dataMap, context);
                monitorSender.info(LOGIN_WARN_NAME, "调用" + HUAWEI_TOKEN_URL + "失败",
                        "responseData is null" + " dataMap=" + dataMap + " context=" + context);
                checkNoticeWarn(LoginConstant.HUAWEI_TYPE, context.getDistinct_id(), HUAWEI_TOKEN_URL);
                return null;
            }

            if (responseData.getStatus() != WebClient.STATUS_OK) {
                monitorSender.info(LOGIN_WARN_NAME, "调用" + HUAWEI_TOKEN_URL + "失败",
                        "status=" + responseData.getStatus() + " body=" + responseData.getBody() +
                                " req=" + context);
                logger.error("status is not ok. url={} status={} body={} req={}",
                        HUAWEI_TOKEN_URL, responseData.getStatus(), responseData.getBody(), context);
                checkNoticeWarn(LoginConstant.HUAWEI_TYPE, context.getDistinct_id(), HUAWEI_TOKEN_URL);
                return null;
            } else {
                String body = responseData.getBody();
                if (StringUtils.isEmpty(body)) {
                    monitorSender.info(LOGIN_WARN_NAME, "调用" + HUAWEI_TOKEN_URL + "失败",
                            "status=" + responseData.getStatus() + " body=" + responseData.getBody() +
                                    " req=" + context);
                    logger.error("body is empty. url={} status={} body={} req={}",
                            HUAWEI_TOKEN_URL, responseData.getStatus(), responseData.getBody(), context);
                    checkNoticeWarn(LoginConstant.HUAWEI_TYPE, context.getDistinct_id(), HUAWEI_TOKEN_URL);
                    return null;
                }
                JSONObject tokenResult = JSON.parseObject(body);
                String accessToken = tokenResult.getString("access_token");
                return getHuaWeiOpenId(context, accessToken);
            }

        } catch (Exception e) {
            logger.error("getHuaWeiUid error, req={} error msg={}", context, e.getMessage(), e);
            monitorSender.info(LOGIN_WARN_NAME, "调用" + HUAWEI_TOKEN_URL + "异常",
                    "msg=" + e.getMessage() +
                            " context=" + context);
            checkNoticeWarn(LoginConstant.HUAWEI_TYPE, context.getDistinct_id(), HUAWEI_TOKEN_URL);
            return null;
        }
    }

    private String getHuaWeiOpenId(RegisterOrLoginContext context, String access_token) {
        HttpResponseData<String> responseData;
        try {
            Map<String, String> dataMap = new HashMap<>();
            dataMap.put("access_token", access_token);
            dataMap.put("open_id", "OPENID");
            responseData = webClient.sendPostWithHttpResp(HUAWEI_INFO_URL, dataMap, 1);
            if (responseData == null) {
                logger.error("send post. url={} dataMap={} context={}", HUAWEI_INFO_URL, dataMap, context);
                monitorSender.info(LOGIN_WARN_NAME, "调用" + HUAWEI_INFO_URL + "失败",
                        "responseData is null" + " dataMap=" + dataMap + " context=" + context);
                checkNoticeWarn(LoginConstant.HUAWEI_TYPE, context.getDistinct_id(), HUAWEI_INFO_URL);
                return null;
            }
            if (responseData.getStatus() != WebClient.STATUS_OK) {
                monitorSender.info(LOGIN_WARN_NAME, "调用" + HUAWEI_INFO_URL + "失败",
                        "status=" + responseData.getStatus() + " body=" + responseData.getBody() +
                                " req=" + context);
                logger.error("status is not ok. url={} status={} body={} req={}",
                        HUAWEI_INFO_URL, responseData.getStatus(), responseData.getBody(), context);
                checkNoticeWarn(LoginConstant.HUAWEI_TYPE, context.getDistinct_id(), HUAWEI_INFO_URL);
                return null;
            } else {
                String body = responseData.getBody();
                if (StringUtils.isEmpty(body)) {
                    monitorSender.info(LOGIN_WARN_NAME, "调用" + HUAWEI_INFO_URL + "失败",
                            "status=" + responseData.getStatus() + " body=" + responseData.getBody() +
                                    " req=" + context);
                    logger.error("body is empty. url={} status={} body={} req={}",
                            HUAWEI_INFO_URL, responseData.getStatus(), responseData.getBody(), context);
                    checkNoticeWarn(LoginConstant.HUAWEI_TYPE, context.getDistinct_id(), HUAWEI_INFO_URL);
                    return null;
                }
                HuaWeiLoginVerifyData data = JSON.parseObject(body, HuaWeiLoginVerifyData.class);
                logger.info("HuaWeiLoginVerifyData info, reqId={} huaiweiData={}", context.getRequestId(), data);
                String huaWeiUid = data.getOpen_id();
                if (StringUtils.isEmpty(huaWeiUid)) {
                    monitorSender.info(LOGIN_WARN_NAME, "调用" + HUAWEI_INFO_URL + "失败",
                            "msg=gpUid is empty" + " body=" + responseData.getBody() +
                                    " req=" + context);
                    logger.error("huaWeiUid is empty. url={}  body={} req={}",
                            HUAWEI_INFO_URL, responseData.getBody(), context);
                }
                return huaWeiUid;
            }

        } catch (Exception e) {
            logger.error("getHuaWeiUid error, req={} error msg={}", context, e.getMessage(), e);
            monitorSender.info(LOGIN_WARN_NAME, "调用" + HUAWEI_INFO_URL + "异常",
                    "msg=" + e.getMessage() +
                            " context=" + context);
            checkNoticeWarn(LoginConstant.HUAWEI_TYPE, context.getDistinct_id(), HUAWEI_INFO_URL);
            return null;
        }
    }

    public FireBaseLoginVerifyData getFireBase(RegisterOrLoginContext context) {
        FireBaseLoginVerifyData data = new FireBaseLoginVerifyData();
        try {
            FirebaseToken res = FirebaseAuth.getInstance().verifyIdToken(context.getLoginToken());
            Map<String, Object> claims = res.getClaims();
            logger.info("getFireBaseUid. reqId={} claims={}", context.getRequestId(), claims);
            String uid = (String) claims.get("user_id");
            data.setUser_id(uid);
            if (LoginConstant.FIRE_BASE_MAIL_TYPE == context.getType()) {
                String email = res.getEmail();
                data.setEmail(email);
            } else if (LoginConstant.FIRE_BASE_PHONE_TYPE == context.getType()) {
                String phoneNumber = (String) claims.get("phone_number");
                data.setPhone_number(phoneNumber);
            }
            if (StringUtils.isEmpty(uid)) {
                monitorSender.info(LOGIN_WARN_NAME, "调用firebase失败",
                        "msg=uid is empty" + " body=" + claims +
                                " req=" + context);
                logger.error("uid is empty. body={} req={}", claims, context);
                checkNoticeWarn(LoginConstant.FIRE_BASE_PHONE_TYPE, context.getDistinct_id(), "fireBase auth");
            }
            return data;

        } catch (Exception e) {
            logger.error("getFireBaseUid error, context={} error msg={}", context, e.getMessage(), e);
            monitorSender.info(LOGIN_WARN_NAME, "调用firebase失败",
                    "msg=" + e.getMessage() + " context=" + context);
            checkNoticeWarn(LoginConstant.FIRE_BASE_PHONE_TYPE, context.getDistinct_id(), "fireBase auth");
            return data;
        }
    }

    /**
     * https://cloud.google.com/java/docs/reference/google-api-client/latest/com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier
     *
     * @param context
     * @return
     */
    public String getGoogleIdBySDK(RegisterOrLoginContext context) {
        try {
            GoogleIdToken idToken = localVerifier.verify(context.getLoginToken());
            if (idToken != null) {
                GoogleIdToken.Payload payload = idToken.getPayload();
                String gpUid = payload.getSubject();
                if (StringUtils.isEmpty(gpUid)) {
                    monitorSender.info(LOGIN_WARN_NAME, "调用google sdk 验证失败",
                            "msg=gpUid is empty" + " body=" + payload +
                                    " req=" + context);
                    logger.error("gpUid is empty. body={} req={}",
                            payload, context);
                }
                return gpUid;
            }
            monitorSender.info(LOGIN_WARN_NAME, "调用google sdk 验证失败",
                    "idToken is null gpUid is empty verify fail" +
                            " req=" + context);
            logger.error("idToken is null gpUid is empty verify fail.  req={}", context);
            return null;
        } catch (Exception e) {
            logger.error("getGoogleIdBySDK error, req={} error msg={}", context, e.getMessage(), e);
            monitorSender.info(LOGIN_WARN_NAME, "调用google sdk 验证失败",
                    "gpUid verify fail" +
                            " req=" + context);
            return null;
        }
    }


    public TnRespondsData getTnDeviceData(RegisterOrLoginContext context) {
        HttpResponseData<String> responseData;
        String errorBody = "";
        try {
            JSONObject data = new JSONObject();
            data.put("appid", TN_APP_ID);
            data.put("appSecret", TN_APP_SECRET);
            data.put("msgBlock", context.getTn_msg());
            JSONObject req = new JSONObject();
            req.put("req", data);

            String randStr = "" + ThreadLocalRandom.current().nextLong(*********, *********);
            String preSg = req.toJSONString() + TN_APP_SIGN_KEY + randStr;
            String sg = DigestUtils.md5DigestAsHex(preSg.getBytes(StandardCharsets.UTF_8));
            String jc = String.format("rnd=%s; sg=%s", randStr, sg);
            Map<String, String> dataMap = new HashMap<>();
            dataMap.put("Content-type", "application/json");
            dataMap.put("JPrx-Ctx", jc);
            dataMap.put("Ts-ClientIP", context.getIp());
            dataMap.put("Ts-Account", !StringUtils.isEmpty(context.getUid()) ? context.getUid() : "");
            long startMillis = System.currentTimeMillis();
            responseData = webClient.sendRestfulPost(TN_POST_URL, req.toJSONString(), dataMap);

//            long startMillis = System.currentTimeMillis();
//            asyncHttpClient.post(TN_POST_URL2, req.toJSONString(), dataMap, respData -> {
//                int asyStatus = respData.getStatus();
//                String asyData = respData.getBody();
//                logger.info("async tn task reqId={} asyStatus={} asyData={} timeMillis={}", context.getRequestId(), asyStatus, asyData, System.currentTimeMillis() - startMillis);
//            });
            long cost = System.currentTimeMillis() - startMillis;
            if (cost > 3000) {
                monitorSender.info(LoginConstant.USTAR_JAVA_EXCEPTION_WARN_NAME, "调用" + TN_POST_URL + "响应时间过长大于3000毫秒",
                        "responseData is null" + " req=" + req.toJSONString() + " context=" + context + " cost=" + cost);
            }
            if (responseData == null) {
                logger.error("send post. url={} req={} context={}", TN_POST_URL, req.toJSONString(), context);
                monitorSender.info(LoginConstant.USTAR_JAVA_EXCEPTION_WARN_NAME, "调用" + TN_POST_URL + "失败",
                        "responseData is null" + " req=" + req.toJSONString() + " context=" + context);
                checkNoticeWarn(99, context.getDistinct_id(), TN_POST_URL);
                return null;
            }
            if (responseData.getStatus() != WebClient.STATUS_OK) {
                monitorSender.info(LoginConstant.USTAR_JAVA_EXCEPTION_WARN_NAME, "调用" + TN_POST_URL + "失败",
                        "status=" + responseData.getStatus() + " response=" + responseData.getBody() +
                                " req=" + req.toJSONString());
                logger.error("status is not ok. url={} status={} body={} req={} context={}",
                        TN_POST_URL, responseData.getStatus(), responseData.getBody(), req.toJSONString(), context);
                checkNoticeWarn(99, context.getDistinct_id(), TN_POST_URL);
                return null;
            } else {
                String body = responseData.getBody();
                errorBody = body;
                if (StringUtils.isEmpty(body)) {
                    monitorSender.info(LoginConstant.USTAR_JAVA_EXCEPTION_WARN_NAME, "调用" + TN_POST_URL + "失败",
                            "status=" + responseData.getStatus() + " response is empty" +
                                    " req=" + req.toJSONString());
                    logger.error("body is empty. url={} status={} body={} req={} context={}",
                            TN_POST_URL, responseData.getStatus(), responseData.getBody(), req.toJSONString(), context);
                    checkNoticeWarn(99, context.getDistinct_id(), TN_POST_URL);
                    return null;
                }
                JSONObject rdata = JSON.parseObject(body).getJSONObject("data");
                TnRespondsData tnData = rdata.getObject("rsp", TnRespondsData.class);
                logger.info("tn info, uid={} android_id={} tnData={}", context.getUid(), context.getAndroidid(), tnData);
                if (StringUtils.isEmpty(tnData.getOpenId())) {
                    monitorSender.info(LoginConstant.USTAR_JAVA_EXCEPTION_WARN_NAME, "调用" + TN_POST_URL +
                                    "失败，没有找到图灵id",
                            "status=" + responseData.getStatus() + " response=" + body +
                                    " req=" + req.toJSONString());
                    logger.error("not find tn id. url={} status={} req={} response={} context={}",
                            TN_POST_URL, responseData.getStatus(), req.toJSONString(), body, context);
                    checkNoticeWarn(99, context.getDistinct_id(), TN_POST_URL);
                }
                if (tnData.getRet() != 0) {
                    monitorSender.info(LoginConstant.USTAR_JAVA_EXCEPTION_WARN_NAME, "图灵顿tn msg验证失败",
                            "ret=" + tnData.getRet() + " tnData=" + tnData + " context=" + context);
                    logger.error("tn msg ret is not ok. ret={} tnData={} context={}",
                            tnData.getRet(), tnData, context);
                    checkNoticeWarn(99, context.getDistinct_id(), TN_POST_URL);
                }
                return tnData;
            }

        } catch (Exception e) {
            monitorSender.info(LoginConstant.USTAR_JAVA_EXCEPTION_WARN_NAME, "调用" + TN_POST_URL + "异常失败",
                    "Tn_msg=" + context.getTn_msg() + " errorBody=" + errorBody +
                            " error msg=" + e.getMessage());
            logger.error("getTn error, context={} error body={}  msg={}", context, errorBody, e.getMessage(), e);
            checkNoticeWarn(99, context.getDistinct_id(), TN_POST_URL);
            return null;
        }
    }


    public List<ApplePublicKeyData> getApplePublicKeys(boolean useCache, String sub) {
        HttpResponseData<String> responseData;
        try {
            List<ApplePublicKeyData> cacheAll = cacheMap.getData(ALL);
            if (!CollectionUtils.isEmpty(cacheAll) && useCache) {
                return cacheAll;
            }
            responseData = webClient.sendGet(APPLE_AUTH_KEYS_URL, null);
            if (responseData != null) {
                String body = responseData.getBody();
                if (responseData.getStatus() != WebClient.STATUS_OK) {
                    monitorSender.info(LOGIN_WARN_NAME, "调用" + APPLE_AUTH_KEYS_URL + "失败",
                            "status=" + responseData.getStatus() + " body=" + responseData.getBody());
                    logger.error("status is not ok. url={} status={} body={}",
                            APPLE_AUTH_KEYS_URL, responseData.getStatus(), responseData.getBody());
                    checkNoticeWarn(LoginConstant.APPLE_TYPE, sub, APPLE_AUTH_KEYS_URL);
                    return null;
                } else {
                    if (StringUtils.isEmpty(body)) {
                        monitorSender.info(LOGIN_WARN_NAME, "调用" + APPLE_AUTH_KEYS_URL + "失败",
                                "status=" + responseData.getStatus() + " body=" + responseData.getBody());
                        logger.error("body is empty. url={} status={} body={}",
                                APPLE_AUTH_KEYS_URL, responseData.getStatus(), responseData.getBody());
                        checkNoticeWarn(LoginConstant.APPLE_TYPE, sub, APPLE_AUTH_KEYS_URL);
                        return null;
                    }

                    JSONObject data = JSON.parseObject(body);
                    JSONArray jsonArray = data.getJSONArray("keys");
                    List<ApplePublicKeyData> all = jsonArray.toJavaList(ApplePublicKeyData.class);
                    if (!CollectionUtils.isEmpty(all)) {
                        cacheMap.cacheData(ALL, all);
                    }
                    return all;

                }
            } else {
                logger.error("send get form get. url={} responseData is null", APPLE_AUTH_KEYS_URL);
                monitorSender.info(LOGIN_WARN_NAME, "调用" + APPLE_AUTH_KEYS_URL + "失败",
                        "msg=responseData is null");
                checkNoticeWarn(LoginConstant.APPLE_TYPE, sub, APPLE_AUTH_KEYS_URL);
                return null;
            }

        } catch (Exception e) {
            logger.error("getApplePublicKeys error, req={} error msg={}", e.getMessage(), e);
            monitorSender.info(LOGIN_WARN_NAME, "调用" + APPLE_AUTH_KEYS_URL + "失败",
                    "msg=" + e.getMessage());
            checkNoticeWarn(LoginConstant.APPLE_TYPE, sub, APPLE_AUTH_KEYS_URL);
            return null;
        }
    }


    public TnRespondsData getTnDeviceDataNew(RegisterOrLoginContext context) {
        try {
            String tnMsgMd5 = DigestUtils.md5DigestAsHex(context.getTn_msg().getBytes(StandardCharsets.UTF_8));
            TnRespondsData tnData = tnMsgCacheDao.getTnRespondsDataCache(tnMsgMd5);
            if (tnData != null) {
                context.setUseCacheTn(true);
                logger.info("get tn info from mysql cache, tnMsgMd5={} tnData={}", tnMsgMd5, tnData);
                return tnData;
            }
            context.setUseCacheTn(false);
            JSONObject bizData = new JSONObject();
            bizData.put("SceneCode", "e_turingfraud_4661084745"); // 场景 code 值，可以根据自己的业务场景自定义名称
            bizData.put("PostTime", System.currentTimeMillis() / 1000);
            bizData.put("AccountType", "2");
            bizData.put("UserIp", context.getIp());
            bizData.put("UserPhone", context.getDbPhoneAccount());
            bizData.put("Mode", "2"); // 腾讯天御集成模式
            bizData.put("PlatformName", "app"); // app/web
            bizData.put("OSName", context.getOs() == 1 ? "IOS" : "Android"); // Android/iOS/H5/
            bizData.put("DeviceToken", context.getTn_msg());
            bizData.put("UserType", 9);
            bizData.put("UserId", !StringUtils.isEmpty(context.getUid()) ? context.getUid() : "");
            String cryptoContent = AesUtilWithBase64.encodeBody(String.valueOf(bizData), NEW_TN_APP_ID);
            Map<String, String> bizCryptoData = new HashMap<>();
            bizCryptoData.put("BizCryptoData.IsAuthorized", "1");
            bizCryptoData.put("BizCryptoData.CryptoType", "1");
            bizCryptoData.put("BizCryptoData.CryptoContent", cryptoContent);
            logger.info("bizData={}", JSONObject.toJSONString(bizData));
            SortedMap<String, String> params = new TreeMap<>(bizCryptoData);
            params.put("Nonce", String.valueOf(new Random().nextInt(Integer.MAX_VALUE)));
            params.put("Timestamp", String.valueOf(System.currentTimeMillis() / 1000));
            params.put("SecretId", NEW_TN_SECRET_ID);
            params.put("Action", "DescribeRiskAssessment");
            params.put("Version", "2020-11-03");
            params.put("Region", "ap-singapore");// eu-frankfurt
            // params.put("Limit", 20 + "");
            // params.put("Offset", 0 + "");
            // params.put("InstanceIds.0", "ins-09dx96dg");
            params.put("Signature", tnHttpUtils.sign(tnHttpUtils.getStringToSign(params), NEW_TN_SECRET_KEY, "HmacSHA1"));
            HttpResponseData<String> responseData = tnHttpUtils.sendPost(params);
            logger.info("tn info, uid={} android_id={} response={}", context.getUid(), context.getAndroidid(), JSONObject.toJSONString(responseData));
            if (responseData == null) {
                monitorSender.info(LOGIN_WARN_NAME, "调用图灵盾失败", "response is null" + " req=" + JSONObject.toJSONString(params));
                logger.error("responseData is null. req={} context={}", JSONObject.toJSONString(params), context);
                checkNoticeWarn(99, context.getDistinct_id(), "https://rce.tencentcloudapi.com/");
                return null;
            }
            if (responseData.getStatus() != WebClient.STATUS_OK || !StringUtils.hasLength(responseData.getBody())) {
                monitorSender.info(LOGIN_WARN_NAME, "调用图灵盾失败", "status=" + responseData.getStatus() + " body=" + responseData.getBody() + " req=" + JSONObject.toJSONString(params));
                logger.error("send post fail. req={} context={} response={}", JSONObject.toJSONString(params), context, JSONObject.toJSONString(responseData));
                checkNoticeWarn(99, context.getDistinct_id(), "https://rce.tencentcloudapi.com/");
                return null;
            }
            JSONObject jsonObject = JSONObject.parseObject(responseData.getBody());
            TuringShieldResponse response = jsonObject != null ? JSON.parseObject(jsonObject.getString("Response"), TuringShieldResponse.class) : null;
            if (response == null) {
                logger.error("send post fail.req={} responseData={} context={}", JSONObject.toJSONString(params), JSONObject.toJSONString(responseData), context);
                monitorSender.info(LOGIN_WARN_NAME, "调用图灵盾失败", "responseData is null" + " req=" + JSONObject.toJSONString(params) + " context=" + context);
                checkNoticeWarn(99, context.getDistinct_id(), "https://rce.tencentcloudapi.com/");
                return null;
            }
            if (response.getError() != null && response.getError().getCode() != 0) {
                monitorSender.info(LOGIN_WARN_NAME, "调用图灵盾失败",
                        "status=" + response.getError().getCode() + " errorMsg=" + response.getError().getMessage() +
                                " req=" + JSONObject.toJSONString(params));
                logger.error("status is not ok. errorMsg={} response={} req={} context={}", response.getError().getMessage(), jsonObject.getString("Response"), JSONObject.toJSONString(params), context);
                checkNoticeWarn(99, context.getDistinct_id(), "https://rce.tencentcloudapi.com/");
                return null;
            } else {
                tnData = toTnRespondsData(response);
                if (StringUtils.isEmpty(tnData.getOpenid())) {
                    monitorSender.info(LOGIN_WARN_NAME, "调用图灵盾失败，没有找到图灵id",
                            "status=" + tnData.getRet() + " response=" + JSONObject.toJSONString(response) +
                                    " req=" + JSONObject.toJSONString(params));
                    logger.error("not find tn id.status={} req={} response={} context={}", tnData.getRet(), JSONObject.toJSONString(params), JSONObject.toJSONString(responseData), context);
                    checkNoticeWarn(99, context.getDistinct_id(), "https://rce.tencentcloudapi.com/");
                } else if (tnData.getRet() != 0) {
                    monitorSender.info(LOGIN_WARN_NAME, "图灵顿tn msg验证失败", "ret=" + tnData.getRet() + " tnData=" + tnData + " context=" + context);
                    logger.error("tn msg ret is not ok. ret={} tnData={} context={}", tnData.getRet(), tnData, context);
                    checkNoticeWarn(99, context.getDistinct_id(), "https://rce.tencentcloudapi.com/");
                } else {
                    // 只记录成功的情况
                    recordAsyncTnMsg(context.getTn_msg(), tnData);
                }
                return tnData;
            }
        } catch (Exception e) {
            monitorSender.info(LOGIN_WARN_NAME, "调用图灵盾异常失败", "Tn_msg=" + context.getTn_msg() + " error msg=" + e.getMessage());
            logger.error("getTn error, context={} error msg={}", context, e.getMessage(), e);
            checkNoticeWarn(99, context.getDistinct_id(), "https://rce.tencentcloudapi.com/");
        }
        return null;
    }

    private TnRespondsData toTnRespondsData(TuringShieldResponse response) {
        TnRespondsData data = new TnRespondsData();
        if (response.getData() == null && response.getError() != null) {
            data.setRet(response.getError().getCode());
            return data;
        }
        TuringShieldResponse.ResponseData.ValueData valueData = response.getData().getValue();
        data.setOpenid(valueData.getDeviceId());
        data.setTimestamp(valueData.getTokenTime());
        data.setRet(response.getData().getCode());
        data.setRiskObj(valueData.getRiskType());
        Map<String, Object> extraInfoMap = new HashMap<>(16);
        if (!CollectionUtils.isEmpty(valueData.getExtraInfo())) {
            for (Map<String, Object> extraInfo : valueData.getExtraInfo()) {
                extraInfoMap.put((String) extraInfo.get("Key"), extraInfo.get("Value"));
            }
        }
        data.setExtraInfo(extraInfoMap);
        return data;
    }

    private void recordAsyncTnMsg(String tnMsg, TnRespondsData tnData) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                TnMsgCacheData data = new TnMsgCacheData();
                String textMd5 = DigestUtils.md5DigestAsHex(tnMsg.getBytes(StandardCharsets.UTF_8));
                data.setTnMsgMd5(textMd5);
                data.setTnMsg(tnMsg);
                data.setTnRespondsData(JSONObject.toJSONString(tnData));
                data.setCtime(DateHelper.getNowSeconds());
                tnMsgCacheDao.add(data);
                logger.info("record mysql TnMsgCacheData success id:{} textMd5:{} openId:{} success ",data.getId(), textMd5, tnData.getOpenid());
                loginDataCountService.tnCheckTask(tnData);
            }
        });
    }

    /**
     * https://developers.google.cn/app-conversion-tracking/api/request-response-specs?hl=zh-cn
     *
     * @param adsId
     * @param gclid
     * @param vName
     * @return
     */
    private String getGoogleConversionUrl(String adsId, String gclid, String vName) {
        String devToken = ""; //
        String linkId = ""; //
        // rdid  设备ID缺失"00000000-0000-0000-0000-000000000000"
        int lat = 0; // 0 用户未选择限制广告跟踪 1 用户已选择限制广告跟踪
        int timestamp = DateHelper.getNowSeconds();
        if (lat == 0) {
            // 有广告id ，深层链接会话
            return "https://www.googleadservices.com/pagead/conversion/app/1.0" + "?dev_token=" + devToken + "&link_id=" + linkId
                    + "&app_event_type=session_start&rdid=" + adsId + "&id_type=advertisingid" + "&lat=" + lat + "&gclid=" + gclid
                    + "&timestamp=" + timestamp + "&app_version=" + vName + "&os_version=" + "&sdk_version=" + vName;
        } else {
            // 广告id不可用，深层链接会话
            return "https://www.googleadservices.com/pagead/conversion/app/1.0" + "?dev_token=" + devToken + "&link_id=" + linkId
                    + "&app_event_type=session_start" + "&rdid=00000000-0000-0000-0000-000000000000" + "&id_type=advertisingid"
                    + "&lat=" + lat + "&gclid=" + gclid + "&gclid_only_request=1"
                    + "&timestamp=" + timestamp + "&app_version=" + vName + "&os_version=" + "&sdk_version=" + vName;
        }
    }


    /**
     * {
     * "ad_events": [{
     * "ad_event_id": "Q2owS0VRancwZHk0QlJDdXVMX2U1TQ",
     * "conversion_metric": "conversion",
     * "interaction_type": "engagement",
     * "campaign_type": "ACI",
     * "campaign_id": 123456789,
     * "campaign_name": "My App Campaign",
     * "ad_type": "ClickToDownload",
     * "external_customer_id": 123456789,
     * "location": 21144,
     * "network_type": "Search",
     * "network_subtype": "GoogleSearch",
     * "video_id": null,
     * "keyword": null,
     * "match_type": null,
     * "placement": null,
     * "ad_group_id": null,
     * "ad_group_name": "",
     * "creative_id": null,
     * "timestamp": 1432681913.123456
     * }],
     * "errors": [],
     * "attributed": true
     * }
     * <p>
     * <p>
     * {
     * "ad_events": [],
     * "errors": ["INVALID_CURRENCY_CODE"],
     * "attributed": false
     * }
     *
     * @param context
     */
    private void tt(RegisterOrLoginContext context) {
        long startMillis = System.currentTimeMillis();
        parseGoogleInstall(context, respData -> {
            int asyStatus = respData.getStatus();
            String asyData = respData.getBody();
            logger.info("async tn task reqId={} asyStatus={} asyData={} timeMillis={}", context.getRequestId(), asyStatus, asyData, System.currentTimeMillis() - startMillis);
        });
    }

    public void parseGoogleInstall(RegisterOrLoginContext context, HttpCallBack<String> httpCallBack) {
        String gclid = context.getInstallReferrer().replace("gclid=", "");
        String conUrl = getGoogleConversionUrl(context.getAndroidid(), gclid, context.getVname());
        asyncHttpClient.post(conUrl, "", null, httpCallBack);
    }


    public SMDeviceRespondsData getShuMeiDeviceData(ShuMeiDeviceDTO dto) {
        HttpResponseData<String> responseData;
        String deviceId = dto.getShuMeiMsg();
        String reqId = dto.getRequestId();
        String baseInfo = String.format("reqId:%s fromInterface:%s appPackageName:%s" +
                        " versioncode:%s vname:%s os:%s uid:%s",
                reqId, dto.getFromInterface(), dto.getApp_package_name(),
                dto.getVersioncode(), dto.getVname(), dto.getOs(), dto.getUid());
        long startMillis = System.currentTimeMillis();
        try {
            JSONObject data = new JSONObject();
            data.put("deviceId", deviceId);
            JSONObject req = new JSONObject();
            req.put("accessKey", SM_ACCESS_KEY);
            req.put("data", data);

            responseData = webClient.sendRestfulPost(SM_ACCESS_URL, req.toJSONString(), null);
            if (responseData == null) {
                logger.error("send post. url={} req={} ", SM_ACCESS_URL, req.toJSONString());
                monitorSender.info(LOGIN_WARN_NAME, "调用" + SM_ACCESS_URL + "失败",
                        "responseData is null" + " req=" + req.toJSONString() + " baseInfo=" + baseInfo);
                return null;
            }
            if (responseData.getStatus() != WebClient.STATUS_OK) {
                monitorSender.info(LOGIN_WARN_NAME, "调用" + SM_ACCESS_URL + "失败",
                        "status=" + responseData.getStatus() + " response=" + responseData.getBody() +
                                " req=" + req.toJSONString() + " baseInfo=" + baseInfo);
                logger.error("status is not ok. url={} status={} body={} req={} ",
                        SM_ACCESS_URL, responseData.getStatus(), responseData.getBody(), req.toJSONString());
                return null;
            } else {
                String body = responseData.getBody();
                long cost = System.currentTimeMillis() - startMillis;
                if (cost > 3000) {
                    monitorSender.info(LOGIN_WARN_NAME, "调用" + SM_ACCESS_URL + "响应时间过长大于3秒",
                            "status=" + responseData.getStatus() + " response is " + body +
                                    " req=" + req.toJSONString() + " baseInfo=" + baseInfo);
                    logger.error("cost long time gt 3s. url={} status={} body={} req={} ",
                            SM_ACCESS_URL, responseData.getStatus(), body, req.toJSONString());

                }
                if (StringUtils.isEmpty(body)) {
                    monitorSender.info(LOGIN_WARN_NAME, "调用" + SM_ACCESS_URL + "失败",
                            "status=" + responseData.getStatus() + " response is empty" +
                                    " req=" + req.toJSONString() + " baseInfo=" + baseInfo);
                    logger.error("body is empty. url={} status={} body={} req={} ",
                            SM_ACCESS_URL, responseData.getStatus(), responseData.getBody(), req.toJSONString());
                    return null;
                }
                logger.info("shumei device info, req deviceId={} baseInfo={} response shuMeiData body={} ",
                        deviceId, baseInfo, body);
                SMDeviceRespondsData shuMeiData = JSON.parseObject(body, SMDeviceRespondsData.class);
                SMDeviceRespondsData.DeviceLabels deviceLabels = shuMeiData.getDeviceLabels();
                String openId = deviceLabels.getId();
                if (!StringUtils.hasLength(openId)) {
                    if (AppVersionUtils.versionCheck(8572, dto)) {
                        monitorSender.info(LOGIN_WARN_NAME, "调用" + SM_ACCESS_URL +
                                        "失败，没有找到数美id",
                                "status=" + responseData.getStatus() + " response=" + body +
                                        " req=" + req.toJSONString() + " baseInfo=" + baseInfo);
                    }
                    logger.info("not find shumei device id. url={} status={} req={} response={} baseInfo={}",
                            SM_ACCESS_URL, responseData.getStatus(), req.toJSONString(), body, baseInfo);

                }
                if (shuMeiData.getCode() != 1100) {
                    monitorSender.info(LOGIN_WARN_NAME, "数美 msg验证失败",
                            "response=" + body + " deviceId=" + deviceId
                                    + " baseInfo=" + baseInfo);
                    logger.error("shumei msg ret is not ok. ret={} shuMeiData={} deviceId={}",
                            shuMeiData.getCode(), shuMeiData, deviceId);
                }
                shuMeiData.setJsonText(body);
                return shuMeiData;
            }

        } catch (Exception e) {
            monitorSender.info(LOGIN_WARN_NAME, "调用" + SM_ACCESS_URL + "异常失败",
                    "deviceId=" + deviceId + " baseInfo=" + baseInfo +
                            " error msg=" + e.getMessage());
            logger.error("getShuMei error, deviceId={} error msg={}", deviceId, e.getMessage(), e);
            return null;
        }
    }


    private void checkNoticeWarn(Integer loginType, String distinctId, String url) {
        try {
            Set<String> loginSet = cacheLoginService.getWarnLoginCache(loginType);
            distinctId = StringUtils.hasLength(distinctId) ? distinctId : "empty";
            loginSet.add(distinctId);
            int num = loginSet.size();
            logger.info("checkNoticeWarn loginType:{} num:{} distinctId:{}", loginType, num, distinctId);
            if (loginType == LoginConstant.GOOGLE_TYPE || loginType == LoginConstant.FACE_BOOK_TYPE) {
                if (num == 80) {
                    logger.error("10分钟内有超过80个用户调用第三方接口url:{}失败，请关注 loginType:{} num:{}",
                            url, loginType, num);
                    monitorSender.info(LOGIN_WARN_NAME,
                            "10分钟内有超过80个用户调用第三方接口" + url + "失败，请关注",
                            "loginType=" + loginType + " num=" + num);
                }
            } else {
                if (num == 50) {
                    monitorSender.infoWithPhone(LoginConstant.USTAR_JAVA_EXCEPTION_WARN_NAME,
                            "10分钟内有超过50个用户调用第三方接口" + url + "失败，请关注",
                            "loginType=" + loginType + " num=" + num);
                }
            }
        } catch (Exception e) {
            logger.error("checkNoticeWarn msg:{}", e.getMessage(), e);
        }
    }

}
