package com.quhong.service;

import com.quhong.config.UserExpConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.*;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.SLangType;
import com.quhong.enums.UserLevelConstant;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.OfficialData;
import com.quhong.mongo.data.UserLevelData;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.obj.LevelRewardObject;
import com.quhong.msg.room.NewUserExpToastMsg;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.msg.room.UserLevelUpMsg;
import com.quhong.mysql.data.UserExpDetailData;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

@Service
public class UserLevelService {
    private static final Logger logger = LoggerFactory.getLogger(UserLevelService.class);
    private static final String PICTURE_URL = "https://cdn3.qmovies.tv/youstar/office_level_up.png";

    private static final String OFFICIAL_TITLE = "Congratulations on your level up.";
    private static final String OFFICIAL_TITLE_AR = "تهانينا على ترقية مستواك.";
    private static final String OFFICIAL_ACT = "View Level Benefits";
    private static final String OFFICIAL_ACT_AR = "عرض مزايا المستوى";

    private static final String OFFICIAL_BODY = "Congratulations on your upgrade to Lv.%d and thank you for being with us all the way. it's been %d days since we met on Youstar. You have sent %d gifts and gained %d followers. Your rank is No.%d among your friends, let's keep working together!";
    private static final String OFFICIAL_BODY_AR = "تهانينا على الترقية إلى Lv.%d ونشكرك على تواجدك معنا طوال الطريق. لقد مضي %d يومًا منذ أن التقينا على اليوستار. لقد أرسلت %d هدايا واكتسبت %d متابع. ترتيبك هو مركز %d بين أصدقائك، فلنواصل العمل معًا!";

    private static final String ROOM_MSG = "Congratulations to %s user level upgrade to Lv.%d!";
    private static final String ROOM_MSG_AR = "\u202bتهانينا على %s ترقية مستوى مستخدم إلى المستوى Lv.%d!";

    private static final String HIGHLIGHT_COLOR = "#F7D704";
    private static final String DONE = "Done";
    private static final String DONE_AR = "تم";

    private static final String EXP = "Exp +%d";
    private static final String EXP_AR = "+%d خبرة";

    private static final String DIAMOND = "diamond";
    private static final String BADGE = "badge";
    private static final String BUBBLE = "buddle";
    private static final String MIC = "mic";

    private static final Integer[] LEVEL_REWARD = new Integer[]{1, 2, 20, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130, 140, 150, 160, 170, 190, 200};

    private static final String TITLE = "Level Reward";
    private static final int ACT_TYPE = 913;

    public static List<Integer> LEVEL_BADGE_LIST = Arrays.asList(20, 40, 60, 80, 100, 120, 140, 160);
    public static List<Integer> ID_BADGE_LIST = Arrays.asList(1147, 1148, 1149, 1150, 1151, 1152, 1153, 1154);

    @Resource
    private UserExpConfig userExpConfig;
    @Resource
    private ActorDao actorDao;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private UserExpDetailDao userExpDetailDao;
    @Resource
    private GiftSendNumDao giftSendNumDao;
    @Resource
    private FollowDao followDao;
    @Resource
    private UserFriendsDao userFriendsDao;
    @Resource
    private ResourceService resourceService;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private BadgeService badgeService;
    @Resource
    private BadgeDao badgeDao;

    @PostConstruct
    public void postInit() {
        if (ServerConfig.isNotProduct()) {
            ID_BADGE_LIST = Arrays.asList(907, 908, 909, 910, 911, 912, 913, 914);
        }
    }

    public int getLevelByExp(long exp) {
        List<Long> expList = userExpConfig.getExpList();
        if (expList.contains(exp)) {
            return expList.indexOf(exp);
        }
        expList.add(exp);
        expList.sort(Long::compare);
        return expList.indexOf(exp) - 1;
    }

    public long getExpByLevel(int level) {
        List<Long> expList = userExpConfig.getUnsafeExpList();
        if (level >= expList.size()) {
            return expList.size();
        }
        return expList.get(level);
    }

    public RoomNotificationMsg getRoomMessage(String uid, int level) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (null == actorData) {
            logger.error("can not find actor data. uid={}", uid);
            return null;
        }
        RoomNotificationMsg msg = new RoomNotificationMsg();
        msg.setUid(uid);
        msg.setUser_head(actorData.getHead());
        msg.setText(String.format(ROOM_MSG, actorData.getName(), level));
        msg.setText_ar(String.format(ROOM_MSG_AR, actorData.getName(), level));
        List<HighlightTextObject> list = new ArrayList<>();
        HighlightTextObject object = new HighlightTextObject();
        object.setText(actorData.getName());
        object.setHighlightColor(HIGHLIGHT_COLOR);
        list.add(object);
        object = new HighlightTextObject();
        object.setText("Lv." + level);
        object.setHighlightColor(HIGHLIGHT_COLOR);
        list.add(object);
        msg.setHighlight_text(list);

        list = new ArrayList<>();
        object = new HighlightTextObject();
        object.setText(actorData.getName());
        object.setHighlightColor(HIGHLIGHT_COLOR);
        list.add(object);
        object = new HighlightTextObject();
        object.setHighlightColor(HIGHLIGHT_COLOR);
        object.setText("Lv." + level);
        list.add(object);
        msg.setHighlight_text_ar(list);
        return msg;
    }

    public OfficialData getOfficialMessage(String uid, int level) {
        OfficialData officialData = new OfficialData();
        officialData.setTo_uid(uid);
        officialData.setValid(1);
        officialData.setAtype(2);
        officialData.setType(2);
        officialData.setPicture(PICTURE_URL);
        officialData.setCtime(DateHelper.getNowSeconds());
        int slang = getSlang(uid);
        String body;
        if (slang == SLangType.ARABIC) {
            officialData.setTitle(OFFICIAL_TITLE_AR);
            officialData.setAct(OFFICIAL_ACT_AR);
            body = OFFICIAL_BODY_AR;
        } else {
            officialData.setTitle(OFFICIAL_TITLE);
            officialData.setAct(OFFICIAL_ACT);
            body = OFFICIAL_BODY;
        }
        body = String.format(body, level, getMeetDay(uid), getSendGifts(uid), getFollows(uid), getLevelRank(uid));
        officialData.setBody(body);
        return officialData;
    }

    public int getSlang(String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (null == actorData || actorData.getSlang() == 0) {
            return SLangType.ARABIC;
        } else {
            return actorData.getSlang();
        }
    }

    public int getMeetDay(String uid) {
        int day = (int) ((System.currentTimeMillis() - new ObjectId(uid).getTimestamp() * 1000L) / (1000 * 3600 * 24));
        return day == 0 ? 1 : day;
    }

    private int getSendGifts(String uid) {
        return giftSendNumDao.getSendGiftNum(uid);
    }

    private int getFollows(String uid) {
        return followDao.getFollowsCount(uid);
    }

    private int getLevelRank(String uid) {
        return userFriendsDao.getFriendsRank(uid);
    }

    public UserLevelUpMsg getUserLevelUpMsg(String uid, int level, int oldLevel) {
        UserLevelUpMsg msg = new UserLevelUpMsg();
        msg.setLevel(level);
        msg.setLevelRank(getLevelRank(uid));
        msg.setFollows(getFollows(uid));
        msg.setMeetDay(getMeetDay(uid));
        msg.setSendGifts(getSendGifts(uid));
        checkRewards(uid, msg, oldLevel);
        return msg;
    }

    /**
     * 处理升级奖励，及下发奖励
     */
    public void checkRewards(String uid, UserLevelUpMsg msg, int oldLevel) {
        if (msg.getLevel() - oldLevel == 1) {
            fullRewards(uid, msg, msg.getLevel());
        } else {
            // 跨级，处理特殊等级奖励
            if (oldLevel <= 2) {
                for (int i = oldLevel + 1; i <= 2; i++) {
                    fullRewards(uid, msg, i);
                }
            }
            for (int i = oldLevel + 1; i <= msg.getLevel(); i++) {
                if (i % 10 == 0) {
                    fullRewards(uid, msg, i);
                }
            }
        }
    }

    /**
     * 填充等级奖励列表
     */
    public void fullRewards(String uid, UserLevelUpMsg msg, int level) {
        if (Arrays.stream(LEVEL_REWARD).anyMatch(i -> i == level)) {
            levelReward(uid, msg, level);
        }

    }

    /**
     * 等级奖励
     */
    public void levelReward(String uid, UserLevelUpMsg msg, int level) {
        int slang = getSlang(uid);
        List<ResourceConfigData> resourceConfigDataList = userExpConfig.getLevelRewards().get(level);
        List<LevelRewardObject> rewardList = new ArrayList<>();
        for (ResourceConfigData data : resourceConfigDataList) {
            LevelRewardObject object = new LevelRewardObject();
            if (slang == SLangType.ARABIC) {
                object.setName(data.getNameAr());
            } else {
                object.setName(data.getName());
            }
            object.setUrl(data.getLink());
            rewardList.add(object);
            // 下发资源
            if (data.getType().equals(DIAMOND)) {
                MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
                moneyDetailReq.setRandomId();
                moneyDetailReq.setUid(uid);
                moneyDetailReq.setRoomId("");
                moneyDetailReq.setAtype(ACT_TYPE);
                moneyDetailReq.setChanged(data.getDiamond());
                moneyDetailReq.setTitle(TITLE);
                moneyDetailReq.setDesc(data.getName());
                moneyDetailReq.setMtime(DateHelper.getNowSeconds());
                mqSenderService.asyncChargeDiamonds(moneyDetailReq);
//                resourceService.sendResourceToMq(new DiamondMqData(uid, data.getDiamond(), data.getName()));
            } else if (data.getType().equals(BADGE)) {
//                resourceService.sendResourceToMq(new ResourceMqData(uid, msg.getLevel()));
                dealLevelBadge(uid, msg.getLevel());
            } else {
                if (!data.getType().equals("bg")) {
                    mqSenderService.asyncHandleResources(getResDTO(uid, data.getType(), ServerConfig.isProduct() ? data.getSourceId() : data.getSourceIdTest(), data.getDay()));
//                    resourceService.sendResourceToMq(new ResourceMqData(uid, data.getType(), ServerConfig.isProduct() ? data.getSourceId() : data.getSourceIdTest(), data.getDay()));
                }
            }
        }
        // 跨级时下发最高等级的奖励
        msg.setRewardList(rewardList);
    }

    private void dealLevelBadge(String uid, int userLevel) {
        int nextLevelIndex = badgeService.getLevelFromSoredList(LEVEL_BADGE_LIST, userLevel);  // 获取下一等级的索引 <20 0  >=20 <40 1
        int nowLevelIndex = nextLevelIndex - 1;

        Set<Integer> badgeIdToDel = new HashSet<>();
        if (nextLevelIndex >= 2) {
            for (int i = 0; i <= nextLevelIndex - 2; i++) {
                badgeIdToDel.add(ID_BADGE_LIST.get(i));
            }
        }
        if (!CollectionUtils.isEmpty(badgeIdToDel)) {
            badgeDao.removeBadges(uid, badgeIdToDel);
        }
        if (nowLevelIndex >= 0) {
            badgeService.giveBadgeToUser(uid, ID_BADGE_LIST.get(nowLevelIndex), userLevel);
        }

    }


    private ResourcesDTO getResDTO(String uid, String resTypeStr, int resId, int rewardSum) {
        ResourcesDTO dto = new ResourcesDTO();
        dto.setUid(uid);
        dto.setResId(resId + "");
        int resType = 0;
        if (BUBBLE.equals(resTypeStr)) {
            resType = BaseDataResourcesConstant.TYPE_BUDDLE;
        } else if (MIC.equals(resTypeStr)) {
            resType = BaseDataResourcesConstant.TYPE_MIC;
        } else {
            logger.error("getResDTO resTypeStr:{}  resId:{} error ", resTypeStr, resId);
        }
        dto.setResType(resType);
        dto.setDays(rewardSum);
        dto.setActionType(BaseDataResourcesConstant.ACTION_GET);
        dto.setmTime(DateHelper.getNowSeconds());
        dto.setGainType(BaseDataResourcesConstant.GAIN_TYPE_DAY);
        return dto;
    }

    public NewUserExpToastMsg getNewUserExpToastMsg(String uid, int score) {
        int slang = getSlang(uid);
        NewUserExpToastMsg msg = new NewUserExpToastMsg();
        if (slang == SLangType.ARABIC) {
            msg.setToast(String.format(EXP_AR, score));
        } else {
            msg.setToast(String.format(EXP, score));
        }
        return msg;
    }

    public Map<String, List<UpdateLevelData>> getUpdateLevel(String uid, int slang) {
        String dateStr = DateHelper.DEFAULT.formatDateInDay(new Date());
        Map<String, List<UpdateLevelData>> result = slang == SLangType.ARABIC ?
                userExpConfig.getUpdateLevelMapDataAr() : userExpConfig.getUpdateLevelMapData();
        String done = slang == SLangType.ARABIC ? DONE_AR : DONE;
        UserLevelData userLevelData = userLevelDao.getUserLevelData(uid);
        if (null == userLevelData) {
            userLevelData = new UserLevelData();
        }
        UserExpDetailData detail = userExpDetailDao.getUserExpDetail(uid, dateStr);
        if (null == detail) {
            detail = new UserExpDetailData();
        }
        for (List<UpdateLevelData> list : result.values()) {
            for (UpdateLevelData data : list) {
                switch (data.getKey()) {
                    case UserLevelConstant.LOGIN:
                        if (detail.getLogin() == UserLevelConstant.LOGIN_POINT) {
                            data.setExp(done);
                        }
                        break;
                    case UserLevelConstant.STAY_ROOM:
                        if (detail.getStay_room() == UserLevelConstant.STAY_ROOM_LIMIT) {
                            data.setExp(done);
                        }
                        break;
                    case UserLevelConstant.UP_MIC:
                        if (detail.getUp_mic() == UserLevelConstant.UP_MIC_LIMIT) {
                            data.setExp(done);
                        }
                        break;
                    case UserLevelConstant.POST_MOMENT:
                        if (detail.getPost_moment() == UserLevelConstant.POST_MOMENT_LIMIT) {
                            data.setExp(done);
                        }
                        break;
                    case UserLevelConstant.SEND_MSG:
                        if (detail.getSend_msg() == UserLevelConstant.SEND_MSG_LIMIT) {
                            data.setExp(done);
                        }
                        break;
                    case UserLevelConstant.SEND_GIFT:
                        if (detail.getSend_gift() == UserLevelConstant.SEND_GIFT_LIMIT) {
                            data.setExp(done);
                        }
                        break;
                    case UserLevelConstant.LIKE_MOMENT:
                        if (detail.getLike_moment() == UserLevelConstant.LIKE_MOMENT_LIMIT) {
                            data.setExp(done);
                        }
                        break;
                    case UserLevelConstant.UPLOAD_PICTURE:
                        if (userLevelData.getReached().contains(UserLevelConstant.UPLOAD_PICTURE)) {
                            data.setExp(done);
                        }
                        break;
                    case UserLevelConstant.FILL_HOBBIES:
                        if (userLevelData.getReached().contains(UserLevelConstant.FILL_HOBBIES)) {
                            data.setExp(done);
                        }
                        break;
                    case UserLevelConstant.FOLLOWED:
                        if (detail.getFollowed() == UserLevelConstant.FOLLOWED_LIMIT) {
                            data.setExp(done);
                        }
                        break;
                    case UserLevelConstant.BECOME_FRIENDS:
                        if (detail.getBecome_friends() == UserLevelConstant.BECOME_FRIENDS_LIMIT) {
                            data.setExp(done);
                        }
                        break;
                    case UserLevelConstant.HOMEPAGE_VIEWED:
                        if (detail.getHomepage_viewed() == UserLevelConstant.HOMEPAGE_VIEWED_LIMIT) {
                            data.setExp(done);
                        }
                        break;
                    case UserLevelConstant.MOMENT_LIKED:
                        if (detail.getMoment_liked() == UserLevelConstant.MOMENT_LIKED_LIMIT) {
                            data.setExp(done);
                        }
                        break;
                    case UserLevelConstant.MOMENT_COMMENT:
                        if (detail.getMoment_comment() == UserLevelConstant.MOMENT_COMMENT_LIMIT) {
                            data.setExp(done);
                        }
                        break;
                    case UserLevelConstant.FIRST_FRIEND:
                        if (userLevelData.getReached().contains(UserLevelConstant.FIRST_FRIEND)) {
                            data.setExp(done);
                        }
                        break;
                    case UserLevelConstant.MORE_FRIENDS:
                        if (userLevelData.getReached().contains(UserLevelConstant.MORE_FRIENDS)) {
                            data.setExp(done);
                        }
                        break;
                    case UserLevelConstant.CHECK_IN:
                        if (detail.getCheck_in() == UserLevelConstant.CHECK_IN_POINT) {
                            data.setExp(done);
                        }
                        break;
                    case UserLevelConstant.FIRST_RECHARGE:
                        if (userLevelData.getReached().contains(UserLevelConstant.FIRST_RECHARGE)) {
                            data.setExp(done);
                        }
                        break;
                    case UserLevelConstant.SEND_ROOM_MSG:
                        if (detail.getSend_room_msg() == UserLevelConstant.SEND_ROOM_MSG_LIMIT) {
                            data.setExp(done);
                        }
                        break;
                    case UserLevelConstant.SEND_FIRST_GIFT:
                        if (userLevelData.getReached().contains(UserLevelConstant.SEND_FIRST_GIFT)) {
                            data.setExp(done);
                        }
                        break;
                    default:
                        break;
                }
            }
        }
        return result;
    }
}
