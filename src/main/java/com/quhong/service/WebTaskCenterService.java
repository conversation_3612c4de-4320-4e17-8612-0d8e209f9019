package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.*;
import com.quhong.config.UserTaskV2Config;
import com.quhong.constant.FriendConstant;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.ResourceMetaData;
import com.quhong.data.TaskInfo;
import com.quhong.dto.DailyTaskDTO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.enums.UserHttpCode;
import com.quhong.exception.CommonException;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.msg.obj.RoomMicInfoObject;
import com.quhong.msg.obj.RoomMicUserObject;
import com.quhong.msg.room.UserWebTaskCompletionMsg;
import com.quhong.msg.room.UserWebTaskNoRewardMsg;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.mysql.dao.NewcomerTaskDao;
import com.quhong.mysql.dao.UserTaskDao;
import com.quhong.mysql.data.NewcomerTaskData;
import com.quhong.mysql.data.UserTaskData;
import com.quhong.redis.RoomMicRedis;
import com.quhong.redis.UserTaskRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.CollectionUtil;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.*;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.quhong.utils.LockKeyUtils.getLockKey;


/**
 * web版任务中心
 */
@Component
public class WebTaskCenterService {

    private static final Logger logger = LoggerFactory.getLogger(WebTaskCenterService.class);
    private static final Interner<String> stringPool = Interners.newWeakInterner();
    public static final String WEB_USER_TASK_LOCK_KEY = "web_user_task_handle_";
    public static final String WEB_ROOKIE_TASK_LOCK_KEY = "web_rookie_task_handle_";

    private static final int A_TYPE = 72;
    private static final String TITLE = "Daily task award";

    public static final int NEWCOMER_TASK = 0; //新人任务
    public static final int DAILY_TASK = 1; // 日常任务

    public final Map<String, TaskInfo> TASK_MAP = new HashMap<>();

    // 特殊新人任务
    private static final String NEW_COMER_STAY_IN_ROOM = "stay_in_the_room";
    private static final String DAILY_TASK_ADD_FRIEND_KEY = "web_daily_add_new_friend";
    private static final List<String> ON_MIC_SHOW_TIME_TASK = Arrays.asList("web_rookie_on_mic_time_5_minute", "web_daily_on_mic_time_10_minute");
    private static final List<String> NEW_DEVICE_ACCOUNT_RES_KEY = Arrays.asList("Otherstay", "DZstay", "AEstay", "OMstay", "EGstay", "PLstay", "BHstay", "DEstay", "FRstay", "CAstay", "QAstay", "LBstay", "LYstay", "USstay", "MAstay", "SAstay", "SDstay", "TNstay", "TRstay", "ESstay", "SYstay", "YEstay", "IQstay", "UKstay", "JOstay");

    @Resource
    private ActorDao actorDao;
    @Resource
    private UserTaskV2Config userTaskV2Config;
    @Resource
    private UserTaskDao userTaskDao;
    @Resource
    private NewcomerTaskDao newcomerTaskDao;
    @Resource
    private UserTaskRedis userTaskRedis;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private EventReport eventReport;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private RoomMicRedis roomMicRedis;

    @PostConstruct
    private void init() {
        userTaskV2Config.getRookieTasks().forEach(taskInfo -> {
            taskInfo.setType(NEWCOMER_TASK);
            TASK_MAP.put(taskInfo.getKey(), taskInfo);
        });
        userTaskV2Config.getDailyTasks().forEach(taskInfo -> {
            taskInfo.setType(DAILY_TASK);
            TASK_MAP.put(taskInfo.getKey(), taskInfo);
        });
    }

    /**
     * web版任务列表（8621）
     */
    public TaskListVO webTaskList(DailyTaskDTO dto) {
        String uid = dto.getUid();
        int slang = dto.getSlang();
        ActorData actorData = actorDao.getActorData(uid);
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", uid);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        actorData.setSlang(slang);
        TaskListVO vo = new TaskListVO();
        vo.setInRoom(roomPlayerRedis.getActorRoomStatus(uid));
        vo.setCountry(actorData.getCountry());
        vo.setCoinBalance((long) actorData.getHeartGot());
        // 新人任务
        fillRookieTasks(vo, actorData);
        String strDate = DateHelper.ARABIAN.formatDateInDay();
        int taskDate = Integer.parseInt(strDate.replace("-", ""));
        List<UserTaskData> userTaskList = userTaskDao.getTaskListByUid(uid, taskDate);
        Map<String, UserTaskData> userTaskMap = CollectionUtil.listToKeyMap(userTaskList, UserTaskData::getTaskKey);
        // 日常任务
        fillDailyTask(actorData, userTaskMap, vo);
        return vo;
    }

    private void fillRookieTasks(TaskListVO vo, ActorData actorData) {
        String uid = actorData.getUid();
        if (!ActorUtils.isNewRegisterActor(uid, 7)) {
            // 非新用户没有新人任务
            return;
        }
        TaskInfoVO taskInfo = new TaskInfoVO();
        taskInfo.setEndTime(getEndTime(uid, true));
        // 新人任务
        List<TaskInfo> rookieTasks = userTaskV2Config.getRookieTasks();
        // 任务完成情况
        List<NewcomerTaskData> rookieTaskList = newcomerTaskDao.getListByUid(uid);
        List<UserTaskData> userDataTaskList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(rookieTaskList)) {
            for (NewcomerTaskData newcomerTaskData : rookieTaskList) {
                UserTaskData userTaskData = new UserTaskData();
                BeanUtils.copyProperties(newcomerTaskData, userTaskData);
                userDataTaskList.add(userTaskData);
            }
        }
        Map<String, UserTaskData> userDataTaskMap = CollectionUtil.listToKeyMap(userDataTaskList, UserTaskData::getTaskKey);
        taskInfo.setCommonTasks(getCommonTaskList(vo, actorData, rookieTasks, userDataTaskMap));
        if (checkAllFinished(taskInfo)) {
            // 新人任务全部完成后不显示
            return;
        }
        vo.setNewcomerTasks(taskInfo);
    }

    private boolean checkAllFinished(TaskInfoVO taskInfo) {
        for (CommonTaskVO taskVO : taskInfo.getCommonTasks()) {
            if (taskVO.getStatus() != 2) {
                return false;
            }
        }
        return true;
    }

    private List<StageTaskVO> getStageTaskList(ActorData actorData, List<TaskInfo> infoList, Map<String, UserTaskData> userTaskMap) {
        int slang = actorData.getSlang();
        if (CollectionUtils.isEmpty(infoList)) {
            return Collections.emptyList();
        }
        List<TaskInfo> stageTaskList = infoList.stream().filter(k -> k.getIsStageTask() == 1).collect(Collectors.toList());
        Map<String, List<TaskInfo>> stageTaskMap = CollectionUtil.listToKeyListMap(stageTaskList, TaskInfo::getStageTaskKey);
        List<StageTaskVO> stageTasks = new ArrayList<>();
        for (Map.Entry<String, List<TaskInfo>> entry : stageTaskMap.entrySet()) {
            StageTaskVO stageTask = new StageTaskVO();
            stageTask.setName(slang == SLangType.ENGLISH ? entry.getValue().get(0).getName() : entry.getValue().get(0).getNameAr());
            stageTask.setRewards(getStageTaskRewards(entry.getValue(), userTaskMap));
            stageTask.setNum(getMaxTaskNum(entry.getKey(), userTaskMap));
            stageTask.setWebJumpCall(entry.getValue().get(0).getWebJumpCall());
            stageTasks.add(stageTask);
        }
        return stageTasks;
    }

    private int getMaxTaskNum(String key, Map<String, UserTaskData> userTaskMap) {
        int maxTaskNum = 0;
        for (int i = 1; i <= 4; i++) {
            UserTaskData userTaskData = userTaskMap.get(key + "_" + i);
            if (userTaskData != null) {
                maxTaskNum = Math.max(maxTaskNum, userTaskData.getTaskNum());
            }
        }
        return maxTaskNum;
    }

    private List<CommonTaskVO> getCommonTaskList(TaskListVO vo, ActorData actorData, List<TaskInfo> infoList, Map<String, UserTaskData> userTaskMap) {
        int slang = actorData.getSlang();
        if (CollectionUtils.isEmpty(infoList)) {
            return Collections.emptyList();
        }
        List<CommonTaskVO> commonTasks = new ArrayList<>();;
        boolean rookieFlag = ActorUtils.isNewRegisterActor(actorData.getUid(), 7);
        for (TaskInfo info : infoList) {
            if (info.getIsStageTask() != 0) {
                continue;
            }
            // 按国家填充资源key
            if(NEW_COMER_STAY_IN_ROOM.equals(info.getKey())){
                fillNewComerSpecialKey(info, actorData);
            }
            // 加好友每日
            if (DAILY_TASK_ADD_FRIEND_KEY.equals(info.getKey()) && rookieFlag){
                continue;
            }

            UserTaskData userTaskData = userTaskMap.get(info.getKey());
            CommonTaskVO commonTask = new CommonTaskVO();
            commonTask.setKey(info.getKey());
            commonTask.setName(slang == SLangType.ENGLISH ? info.getName() : info.getNameAr());
            commonTask.setWebIcon(info.getWebIcon());
            commonTask.setNum(userTaskData == null ? 0 : userTaskData.getTaskNum());
            commonTask.setLimit(info.getLimit());
            commonTask.setResourceKeyConfigData(resourceKeyHandlerService.getConfigData(ObjectUtils.isEmpty(actorData.getFirstTnId()) ? info.getResourceKeyOther() : info.getResourceKey()));
            commonTask.setStatus(userTaskData == null ? 0 : userTaskData.getStatus());
            commonTask.setWebJumpCall(info.getWebJumpCall());

            // 持续上麦任务倒计时
            if(commonTask.getStatus() <= 0 && ON_MIC_SHOW_TIME_TASK.contains(info.getKey())){
                fillOnMicEndTime(vo, commonTask, actorData);
            }
            commonTasks.add(commonTask);
        }
        // 未领取 > 未完成 > 已领取
        commonTasks.sort(Comparator.comparingInt(o -> o.getStatus() == 1 ? 0 : o.getStatus() == 0 ? 1 : 2));
        return commonTasks;
    }

    /**
     * 设置特殊新手任务奖励key
     */
    private void fillNewComerSpecialKey(TaskInfo info, ActorData actorData){
        String countryCode = ActorUtils.getUpperCaseCountryCode(actorData.getCountry());
        String resKey = String.format("%sstay", countryCode);
        info.setResourceKey(NEW_DEVICE_ACCOUNT_RES_KEY.contains(resKey) ? resKey : NEW_DEVICE_ACCOUNT_RES_KEY.get(0));
    }


    /**
     * 在麦倒计时
     */
    private void fillOnMicEndTime(TaskListVO vo, CommonTaskVO taskVO, ActorData actorData){
        String uid = actorData.getUid();
        int currentTime = DateHelper.getNowSeconds();
        int needTime = taskVO.getKey().equals(ON_MIC_SHOW_TIME_TASK.get(0)) ? 300 : 600;
        // 先判断是否在房间
        String roomId = vo.getInRoom();
        if(ObjectUtils.isEmpty(roomId)){
            return;
        }

        // 判断麦位是否有该用户
        RoomMicListVo roomMicListVo = roomMicRedis.getRoomMicFromRedis(roomId);
        if (roomMicListVo == null) {
            return;
        }
        for (RoomMicInfoObject micObj : roomMicListVo.getList()) {
            RoomMicUserObject micObjUser = micObj.getUser();
            if (ObjectUtils.isEmpty(micObjUser) || StringUtils.isEmpty(micObjUser.getAid())) {
                continue;
            }

            if(micObjUser.getAid().equals(uid)){
                int delta = currentTime - micObjUser.getUpMicTime();
                int leftTime = needTime - delta;
                taskVO.setCountdownTime(leftTime > 0 ? currentTime + leftTime : currentTime + needTime);
                break;
            }
        }
    }

    private List<StageTaskVO.Reward> getStageTaskRewards(List<TaskInfo> taskList, Map<String, UserTaskData> userTaskMap) {
        List<StageTaskVO.Reward> rewardList = new ArrayList<>();
        for (TaskInfo taskInfo : taskList) {
            UserTaskData userTaskData = userTaskMap.get(taskInfo.getKey());
            rewardList.add(new StageTaskVO.Reward(taskInfo.getKey(), taskInfo.getLimit(), userTaskData == null ? 0 : userTaskData.getStatus()));
        }
        return rewardList;
    }

    private void fillDailyTask(ActorData actorData, Map<String, UserTaskData> userTaskMap, TaskListVO vo) {
        String uid = actorData.getUid();
        TaskInfoVO taskInfo = new TaskInfoVO();
        List<TaskInfo> dailyTasks = userTaskV2Config.getDailyTasks();
        taskInfo.setEndTime(getEndTime(uid, false));
        taskInfo.setCommonTasks(getCommonTaskList(vo, actorData, dailyTasks, userTaskMap));
        vo.setDailyTasks(taskInfo);
    }

    private int getEndTime(String uid, boolean isNewcomerTask) {
        if (isNewcomerTask) {
            return 7 * 24 * 60 * 60 + new ObjectId(uid).getTimestamp();
        } else {
            return (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000) + 24 * 60 * 60 - 1;
        }
    }

    public TaskRewardVO getTaskReward(DailyTaskDTO dto) {
        ActorData actorData = actorDao.getActorData(dto.getUid());
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", dto.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        TaskInfo taskInfo = TASK_MAP.get(dto.getTaskKey());
        if (StringUtils.isEmpty(dto.getTaskKey()) || null == taskInfo) {
            logger.error("get task reward param error. uid={} taskKey={}", dto.getUid(), dto.getTaskKey());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        TaskRewardVO vo = new TaskRewardVO();
        try (DistributeLock lock = new DistributeLock(getLockKey(dto.getUid()))) {
            lock.lock();
            if (taskInfo.getType() == NEWCOMER_TASK) {
                if (!ActorUtils.isNewRegisterActor(dto.getUid(), 7)) {
                    logger.info("Non-new users cannot receive newcomer tasks rewards. uid={} taskKey={}", dto.getUid(), dto.getTaskKey());
                    throw new CommonException(UserHttpCode.NO_NEW_USERS_CANNOT_RECEIVE);
                }
                NewcomerTaskData newcomerTask = newcomerTaskDao.getByUidAndTaskKey(dto.getUid(), dto.getTaskKey());
                if (NEW_COMER_STAY_IN_ROOM.equals(dto.getTaskKey())){
                    if (null != newcomerTask) {
                        logger.info("This reward has been gotten.");
                        throw new CommonException(UserHttpCode.THIS_REWARD_HAS_BEEN_GOTTEN);
                    }
                    int currentTime = DateHelper.getNowSeconds();
                    NewcomerTaskData newRecord = new NewcomerTaskData();
                    newRecord.setUid(dto.getUid());
                    newRecord.setTaskKey(taskInfo.getKey());
                    newRecord.setTaskNum(1);
                    newRecord.setTaskValue("");
                    newRecord.setStatus(2);
                    newRecord.setMtime(currentTime);
                    newRecord.setCtime(currentTime);
                    newcomerTaskDao.insert(newRecord);
                    sendReward(actorData, taskInfo, vo);
                    userTaskRedis.setUserStayInRoomStatus(dto.getUid());
                    sendNoRewardMsg(dto.getUid());
                    doNewComerTaskRecordEvent(dto.getUid(), taskInfo, 1);
                }else{
                    if (null == newcomerTask || newcomerTask.getTaskNum() < taskInfo.getLimit()) {
                        logger.info("The task has not been completed and the reward cannot be received. uid={} taskKey={}", dto.getUid(), dto.getTaskKey());
                        throw new CommonException(UserHttpCode.NOT_COMPLETED_AND_CANNOT_RECEIVE);
                    }
                    checkStatus(newcomerTask.getStatus());
                    sendReward(actorData, taskInfo, vo);
                    newcomerTask.setStatus(2);
                    newcomerTaskDao.update(newcomerTask);
                    sendNoRewardMsg(dto.getUid());
                    doNewComerTaskRecordEvent(dto.getUid(), taskInfo, newcomerTask.getTaskNum());
                }
            } else {
                int taskDate = Integer.parseInt(DateHelper.ARABIAN.formatDateInDay2());
                UserTaskData userTask = userTaskDao.getTaskByTaskKey(dto.getUid(), taskDate, dto.getTaskKey());
                if (null == userTask || userTask.getTaskNum() < taskInfo.getLimit()) {
                    logger.info("The task has not been completed and the reward cannot be received. uid={} taskKey={}", dto.getUid(), dto.getTaskKey());
                    throw new CommonException(UserHttpCode.NOT_COMPLETED_AND_CANNOT_RECEIVE);
                }
                checkStatus(userTask.getStatus());
                sendReward(actorData, taskInfo, vo);
                userTask.setStatus(2);
                userTaskDao.update(userTask);
                userTaskRedis.incWebUserHasRewardCount(dto.getUid(), -1);
                sendNoRewardMsg(dto.getUid());
            }
        }
        ActorData afterActorData = actorDao.getActorData(dto.getUid());
        vo.setCoinBalance((long) afterActorData.getHeartGot());
        userTaskRedis.saveTnGetRewardRecord(dto.getTaskKey(), actorData.getTn_id());
        return vo;
    }

    /**
     * 如果全部奖励已领取，发送消息将状态
     */
    private void sendNoRewardMsg(String uid) {
        int hasRewardCount = userTaskRedis.getWebUserHasRewardCount(uid);
        boolean isNewRegister = ActorUtils.isNewRegisterActor(uid, 7);
        hasRewardCount = isNewRegister ? newcomerTaskDao.selectHasRewardCount(uid, 2) : hasRewardCount;
        if (hasRewardCount == 0){
            UserWebTaskNoRewardMsg msg = new UserWebTaskNoRewardMsg();
            roomWebSender.sendPlayerWebMsg("", uid, uid, msg, false);
        }
    }


    private void sendReward(ActorData actorData, TaskInfo taskInfo, TaskRewardVO vo) {
        String key = taskInfo.getKey();
        if (NEW_COMER_STAY_IN_ROOM.equals(key)){
            fillNewComerSpecialKey(taskInfo, actorData);
        }
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyHandlerService.getConfigData(ObjectUtils.isEmpty(actorData.getFirstTnId()) ? taskInfo.getResourceKeyOther() : taskInfo.getResourceKey());
        if (resourceKeyConfigData == null){
            return;
        }
        List<ResourceMetaData> rewardConfigList = new ArrayList<>();
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : resourceKeyConfigData.getResourceMetaList()) {
            resourceKeyHandlerService.sendOneResourceDataNoBroadcast(actorData, resourceMeta, A_TYPE, TITLE, TITLE, 3);
            ResourceMetaData resVO = new ResourceMetaData();
            BeanUtils.copyProperties(resourceMeta, resVO);
            rewardConfigList.add(resVO);
        }
        vo.setRewardConfigList(rewardConfigList);
    }

    private void checkStatus(int status) {
        if (status == 1) {
            return;
        }
        if (status == 0) {
            logger.info("The task has not been completed and the reward cannot be received.");
            throw new CommonException(UserHttpCode.NOT_COMPLETED_AND_CANNOT_RECEIVE);
        }
        if (status == 2) {
            logger.info("This reward has been gotten.");
            throw new CommonException(UserHttpCode.THIS_REWARD_HAS_BEEN_GOTTEN);
        }
    }

    public Map<String, TaskInfo> getTaskInfoMap() {
        return TASK_MAP;
    }


    /**
     * 任务处理
     */
    public void handleWebTaskMqData(CommonMqTopicData mqData) {
        if (mqData == null || StringUtils.isEmpty(mqData.getUid())) {
            return;
        }
        Map<String, TaskInfo> taskMap = TASK_MAP;
        int num = mqData.getValue() != 0 ? mqData.getValue() : 1;
        int taskDate = Integer.parseInt(mqData.getDateStr().replace("-", ""));
        String handleId = mqData.getHandleId();
        switch (mqData.getItem()) {
            case CommonMqTaskConstant.ON_MIC_TIME_5_MINUTE:
                handleNewcomerTask(taskMap.get("web_rookie_on_mic_time_5_minute"), mqData.getUid(), num, handleId, false);
                break;
            case CommonMqTaskConstant.ONE_CLICK_GREETING:
                handleNewcomerTask(taskMap.get("web_one_click_greeting"), mqData.getUid(), num, handleId, false);
                break;
            case CommonMqTaskConstant.UPDATE_PERSONAL_INFO:
                handleNewcomerTask(taskMap.get("web_rookie_update_personal_info"), mqData.getUid(), num, handleId, false);
                break;
            case CommonMqTaskConstant.SEND_ROOM_MSG:
            case CommonMqTaskConstant.SEND_HALL_MSG:
                if (RoomUtils.isGameRoom(mqData.getRoomId())) {
                    break;
                }
                handleNewcomerTask(taskMap.get("web_rookie_send_room_msg"), mqData.getUid(), num, handleId, false);
                break;
            case CommonMqTaskConstant.SEND_ROOM_GIFT:
                if (RoomUtils.isGameRoom(mqData.getRoomId())) {
                    break;
                }
                handleNewcomerTask(taskMap.get("web_rookie_send_room_gift"), mqData.getUid(), 1, handleId, false);
                handleUserTask(taskMap.get("web_daily_send_room_gift"), mqData.getUid(), taskDate, 1, handleId, false);
                break;
            case CommonMqTaskConstant.ADD_FRIEND:
                handleNewcomerTask(taskMap.get("web_rookie_add_friend"), mqData.getUid(), num, handleId, true);
                if (!isNewUser(mqData.getUid())) {
                    handleUserTask(taskMap.get("web_daily_add_new_friend"), mqData.getUid(), taskDate, num, handleId, true);
                }
                if (!ObjectUtils.isEmpty(mqData.getJsonData())){
                    CommonMqTopicData.FriendInfo friendInfo = JSONObject.parseObject(mqData.getJsonData(), CommonMqTopicData.FriendInfo.class);
                    if (Objects.equals(friendInfo.getFriendSource(), FriendConstant.SOURCE_SAY_HELLO)) {
                        handleUserTask(taskMap.get("web_greeting_become_friend"), mqData.getUid(), taskDate, 1, handleId, true);
                    }
                }
                break;
            case CommonMqTaskConstant.FOLLOW_ROOM:
                handleNewcomerTask(taskMap.get("web_rookie_follow_room"), mqData.getUid(), num, handleId, true);
                handleUserTask(taskMap.get("web_daily_follow_room"), mqData.getUid(), taskDate, num, handleId, true);
                break;
            case CommonMqTaskConstant.JOIN_ROOM_MEMBER:
                handleNewcomerTask(taskMap.get("web_rookie_join_room_member"), mqData.getUid(), num, handleId, true);
                break;
            case CommonMqTaskConstant.LIKE_MOMENT:
            case CommonMqTaskConstant.COMMENT_MOMENT:
                handleNewcomerTask(taskMap.get("web_rookie_like_or_comment_moment"), mqData.getUid(), num, handleId, true);
                handleUserTask(taskMap.get("web_daily_like_or_comment_moment"), mqData.getUid(), taskDate, num, handleId, true);
                break;
            case CommonMqTaskConstant.ON_MIC_TIME_10_MINUTE:
                handleUserTask(taskMap.get("web_daily_on_mic_time_10_minute"), mqData.getUid(), taskDate, num, handleId, false);
                break;
            case CommonMqTaskConstant.INVITE_USER_ON_MIC:
                int upMicType = roomMicRedis.getUpMicType(mqData.getAid());
                if (upMicType == 2) {
                    handleUserTask(taskMap.get("web_daily_invite_new_users_on_mic"), mqData.getUid(), taskDate, num, handleId, true);
                }
                break;
            case CommonMqTaskConstant.POST_MOMENT:
                handleUserTask(taskMap.get("web_daily_post_moment"), mqData.getUid(), taskDate, num, handleId, true);
                break;
            case CommonMqTaskConstant.PLAY_LUDO:
                handleUserTask(taskMap.get("web_daily_play_ludo"), mqData.getUid(), taskDate, num, handleId, false);
                break;
            case CommonMqTaskConstant.PLAY_UMO:
                handleUserTask(taskMap.get("web_daily_play_umo"), mqData.getUid(), taskDate, num, handleId, false);
                break;
            case CommonMqTaskConstant.PLAY_MONSTER_CRUSH:
                handleUserTask(taskMap.get("web_daily_play_monster_crush"), mqData.getUid(), taskDate, num, handleId, false);
                break;
            case CommonMqTaskConstant.PLAY_CARROM_POOL:
                handleUserTask(taskMap.get("web_daily_play_carrom_pool"), mqData.getUid(), taskDate, num, handleId, false);
                break;
            case CommonMqTaskConstant.PLAY_JACKAROO:
                handleUserTask(taskMap.get("web_daily_play_jackaroo"), mqData.getUid(), taskDate, num, handleId, false);
                break;
            case CommonMqTaskConstant.PLAY_BALOOT:
                handleUserTask(taskMap.get("web_daily_play_baloot"), mqData.getUid(), taskDate, num, handleId, false);
                break;
            case CommonMqTaskConstant.PLAY_DOMINO:
                handleUserTask(taskMap.get("web_daily_play_domino"), mqData.getUid(), taskDate, num, handleId, false);
                break;
            default:
                break;
        }
    }

    /**
     * 日常任务和进阶任务
     */
    private void handleUserTask(TaskInfo taskInfo, String uid, int taskDate, int num, String handleId, boolean checkRepeat) {
        if (taskInfo == null){
            return;
        }
        int nowTime = DateHelper.getNowSeconds();
        boolean finishedTask = false;
        synchronized (stringPool.intern(WEB_USER_TASK_LOCK_KEY + uid)) {
            UserTaskData oldRecord = userTaskDao.getTaskByTaskKey(uid, taskDate, taskInfo.getKey());
            if (oldRecord != null) {
                if (checkRepeat && oldRecord.getTaskValue().contains(handleId)) {
                    return;
                }
                if (oldRecord.getTaskNum() >= taskInfo.getLimit()) {
                    return;
                }
                oldRecord.setTaskNum(oldRecord.getTaskNum() + num);
                oldRecord.setMtime(nowTime);
                if (checkRepeat) {
                    oldRecord.setTaskValue(oldRecord.getTaskValue() + "," + handleId);
                }
                if (oldRecord.getTaskNum() >= taskInfo.getLimit() && oldRecord.getStatus() == 0) {
                    finishedTask = true;
                    oldRecord.setStatus(1);
                }
                userTaskDao.update(oldRecord);
            } else {
                UserTaskData newRecord = new UserTaskData();
                newRecord.setUid(uid);
                newRecord.setTaskDate(taskDate);
                newRecord.setTaskKey(taskInfo.getKey());
                newRecord.setTaskNum(num);
                newRecord.setTaskValue(checkRepeat ? handleId : "");
                if (newRecord.getTaskNum() >= taskInfo.getLimit()) {
                    finishedTask = true;
                    newRecord.setStatus(1);
                } else {
                    newRecord.setStatus(0);
                }
                newRecord.setMtime(nowTime);
                newRecord.setCtime(nowTime);
                userTaskDao.insert(newRecord);
            }
        }
        if (finishedTask) {
            sendUserTaskCompletionMsg(uid, taskInfo.getKey());
            userTaskRedis.incWebUserHasRewardCount(uid, 1);
        }
    }

    /**
     * 新人任务
     */
    public void handleNewcomerTask(TaskInfo taskInfo, String uid, int num, String handleId, boolean checkRepeat) {
        if (taskInfo == null){
            return;
        }

        int nowTime = DateHelper.getNowSeconds();
        if (!isNewUser(uid)) {
            return;
        }
        boolean finishedTask = false;
        synchronized (stringPool.intern(WEB_ROOKIE_TASK_LOCK_KEY + uid)) {
            NewcomerTaskData oldRecord = newcomerTaskDao.getByUidAndTaskKey(uid, taskInfo.getKey());
            if (oldRecord != null) {
                if (checkRepeat && oldRecord.getTaskValue().contains(handleId)) {
                    return;
                }
                if (oldRecord.getTaskNum() >= taskInfo.getLimit()) {
                    return;
                }
                oldRecord.setTaskNum(oldRecord.getTaskNum() + num);
                oldRecord.setMtime(nowTime);
                if (checkRepeat) {
                    oldRecord.setTaskValue(oldRecord.getTaskValue() + "," + handleId);
                }
                if (oldRecord.getTaskNum() >= taskInfo.getLimit() && oldRecord.getStatus() == 0) {
                    finishedTask = true;
                    oldRecord.setStatus(1);
                }
                newcomerTaskDao.update(oldRecord);
            } else {
                NewcomerTaskData newRecord = new NewcomerTaskData();
                newRecord.setUid(uid);
                newRecord.setTaskKey(taskInfo.getKey());
                newRecord.setTaskNum(num);
                newRecord.setTaskValue(checkRepeat ? handleId : "");
                if (newRecord.getTaskNum() >= taskInfo.getLimit()) {
                    finishedTask = true;
                    newRecord.setStatus(1);
                } else {
                    newRecord.setStatus(0);
                }
                newRecord.setMtime(nowTime);
                newRecord.setCtime(nowTime);
                newcomerTaskDao.insert(newRecord);
            }
        }
        if (finishedTask) {
            sendUserTaskCompletionMsg(uid, taskInfo.getKey());
            // 新手任务埋点
            NoviceTaskRecordEvent event = new NoviceTaskRecordEvent();
            event.setUid(uid);
            event.setTask_key(taskInfo.getKey());
            event.setCtime(DateHelper.getNowSeconds());
            eventReport.track(new EventDTO(event));
        }
    }

    private void sendUserTaskCompletionMsg(String uid, String taskKey) {
        UserWebTaskCompletionMsg msg = new UserWebTaskCompletionMsg();
        msg.setTaskKey(taskKey);
        // msg.setShowGuide(ActorUtils.isNewRegisterActor(uid, 7) ? 1 : 0);  // 7天用户展示引导
        msg.setShowGuide(1);
        msg.setShowPopUp(1);
        logger.info("sendUserTaskCompletionMsg uid: {}, msg:{}", uid, JSON.toJSONString(msg));
        roomWebSender.sendPlayerWebMsg("", uid, uid, msg, false);
    }

    private boolean isNewUser(String uid) {
        return ActorUtils.isNewRegisterActor(uid, 7);
    }


    // 增加任务埋点
    private void doNewComerTaskRecordEvent(String uid, TaskInfo taskInfo, int process) {
        TaskCompleteEvent event = new TaskCompleteEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setTask_type(taskInfo.getType());
        event.setTask_name(taskInfo.getName());
        event.setTask_progress(process);
        event.setTask_total_progress(taskInfo.getLimit());
        event.setTask_status(2);
        eventReport.track(new EventDTO(event));
    }

}
