package com.quhong.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.quhong.data.ApplePublicKeyData;
import com.quhong.data.RegisterOrLoginContext;
import com.quhong.service.AbstractLoginService;
import com.quhong.service.ThirdApiLoginService;
import io.jsonwebtoken.*;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.math.BigInteger;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.RSAPublicKeySpec;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class AppleJwtLoginService extends AbstractLoginService {
    private static final Logger logger = LoggerFactory.getLogger(AppleJwtLoginService.class);
    /**
     * 6个月有效期
     * 新密钥：
     * ******************************************************************************************************************************************************************************************************************************************************************************************************
     * 旧密钥：
     * ******************************************************************************************************************************************************************************************************************************************************************************************************
     */
    private static final String NEW_CLIENT_SECRET = "******************************************************************************************************************************************************************************************************************************************************************************************************";
    private static final String OLD_CLIENT_SECRET = "******************************************************************************************************************************************************************************************************************************************************************************************************";

    @Override
    public RegisterOrLoginContext beforeLogin(RegisterOrLoginContext context) {
        context.setThirdUid(getAppleUid(context));
        return context;
    }

    @Override
    public RegisterOrLoginContext afterLogin(RegisterOrLoginContext context) {
        return null;
    }

    private String getAppleUid(RegisterOrLoginContext context) {
        // https://blog.csdn.net/w_monster/article/details/124171787
        // 方式一 通过code获取id_token  https://appleid.apple.com/auth/token,然后再jwt解析
        // 方式二 获取公钥 https://appleid.apple.com/auth/keys 访问apple获取公钥。apple的接口会返回很多公钥的，根据jwt数据header的kid，找到对应的公钥,然后再jwt解析、校验
        try {
            DecodedJWT jwt = JWT.decode(context.getLoginToken());
            String kid = jwt.getHeaderClaim("kid").asString();
            String iss = jwt.getClaim("iss").asString();
            String aud = jwt.getClaim("aud").asString();
            String sub = jwt.getClaim("sub").asString();
            if (StringUtils.isEmpty(kid) || StringUtils.isEmpty(iss) || StringUtils.isEmpty(aud) || StringUtils.isEmpty(sub)) {
                logger.info("getAppleUid jwt empty, kid={} iss={} aud={} sub={} context={}", kid, iss, aud, sub, context);
                return null;
            }

            int result = verifyToken(context.getLoginToken(), kid, iss, aud, sub, true);
            if (result == 1) {
                result = verifyToken(context.getLoginToken(), kid, iss, aud, sub, false);
            }
//            if (result == 0) {
//                String oldSub = userMigrationInfo(sub);
//                logger.info("userMigrationSub:{} sub：{}", oldSub, sub);
//                return null != oldSub ? oldSub : sub;
//            }
            if (result == 0) {
                return sub;
            } else {
                monitorSender.info(ThirdApiLoginService.LOGIN_WARN_NAME, "调用apple verifyToken失败",
                        "result=" + result);
            }
            return null;
        } catch (Exception e) {
            logger.error("getAppleUid error, req={} error msg={}", context, e.getMessage(), e);
            monitorSender.info(ThirdApiLoginService.LOGIN_WARN_NAME, "调用apple getAppleUid失败",
                    "msg=" + e.getMessage());
        }
        return null;
    }

//    @PostConstruct
//    public void init() {
//        userMigrationInfo("000681.12d92853cda34d0497ccc33c2b6c100b.1426");
//    }

    public String userMigrationInfo(String sub) {
        try {
            String accessToken = getAccessToken(NEW_CLIENT_SECRET);
            Map<String, String> migration = new HashMap<>();
            migration.put("sub", sub);
            migration.put("client_secret", NEW_CLIENT_SECRET);
            migration.put("client_id", "com.stonemobile.youstar");
            migration.put("target", "N8SQASNPTJ");
            Map<String, String> header = new HashMap<>();
            header.put("Authorization", "Bearer " + accessToken);
            String resp = webClient.sendPost("https://appleid.apple.com/auth/usermigrationinfo", migration, header);
            JSONObject respResult = JSON.parseObject(resp);
            String transferSub = respResult.getString("transfer_sub");
            logger.info("userMigrationInfo. sub={} resp={}", sub, resp);
            return changeSub(transferSub, accessToken);
        } catch (Exception e) {
            return null;
        }
    }

    private String getAccessToken(String clientSecret) {
        Map<String, String> genTokenParams = new HashMap<>();
        genTokenParams.put("client_id", "com.stonemobile.youstar");
        genTokenParams.put("client_secret", clientSecret);
        genTokenParams.put("scope", "user.migration");
        genTokenParams.put("grant_type", "client_credentials");
        String tokenResp = webClient.sendPost("https://appleid.apple.com/auth/token", genTokenParams, 1);
        logger.info("appleRevokeToken tokenResp={}", tokenResp);
        JSONObject tokenResult = JSON.parseObject(tokenResp);
        String accessToken = tokenResult.getString("access_token");
        if (StringUtils.isEmpty(accessToken)) {
            logger.error("appleRevokeToken error accessToken is empty tokenResp={}", tokenResp);
            return null;
        }
        return accessToken;
    }

    public int verifyToken(String identityToken, String kid, String iss, String aud, String sub, boolean useCache) {
        int result = 5;
        try {
//            String n = "1JiU4l3YCeT4o0gVmxGTEK1IXR-Ghdg5Bzka12tzmtdCxU00ChH66aV-4HRBjF1t95IsaeHeDFRgmF0lJbTDTqa6_VZo2hc0zTiUAsGLacN6slePvDcR1IMucQGtPP5tGhIbU-HKabsKOFdD4VQ5PCXifjpN9R-1qOR571BxCAl4u1kUUIePAAJcBcqGRFSI_I1j_jbN3gflK_8ZNmgnPrXA0kZXzj1I7ZHgekGbZoxmDrzYm2zmja1MsE5A_JX7itBYnlR41LOtvLRCNtw7K3EFlbfB6hkPL-Swk5XNGbWZdTROmaTNzJhV-lWT0gGm6V1qWAK2qOZoIDa_3Ud0Gw";
//            String e = "AQAB";
            List<ApplePublicKeyData> all = thirdApiLoginService.getApplePublicKeys(useCache,sub);
            if (CollectionUtils.isEmpty(all)) {
                logger.info("verifyToken error, PublicKeys is empty kid={} sub={}", kid, sub);
                return result;
            }

            ApplePublicKeyData nowKey = null;
            for (ApplePublicKeyData item : all) {
                if (kid.equals(item.getKid())) {
                    nowKey = item;
                    break;
                }
            }

            if (null == nowKey || StringUtils.isEmpty(nowKey.getN()) || StringUtils.isEmpty(nowKey.getE())) {
                logger.info("verifyToken error, not find PublicKey by kid={} sub={} nowKey={}", kid, sub, nowKey);
                return result;
            }
            logger.info("use kid={} sub={} nowKey={}", kid, sub, nowKey);
            BigInteger modulus = new BigInteger(1, Base64.decodeBase64(nowKey.getN()));
            BigInteger publicExponent = new BigInteger(1, Base64.decodeBase64(nowKey.getE()));

            RSAPublicKeySpec spec = new RSAPublicKeySpec(modulus, publicExponent);
            KeyFactory kf = KeyFactory.getInstance("RSA");
            PublicKey publicKey = kf.generatePublic(spec);

            JwtParser jwtParser = Jwts.parser().setSigningKey(publicKey);
            jwtParser.requireIssuer(iss);
            jwtParser.requireAudience(aud);
            jwtParser.requireSubject(sub);
            try {
                Jws<Claims> claim = jwtParser.parseClaimsJws(identityToken);
                if (claim != null && claim.getBody().containsKey("auth_time")) {
                    return 0;
                }
                result = 2;
            } catch (ExpiredJwtException e) {
                result = 3;
                logger.error("verifyToken ExpiredJwtException error, sub={} error msg={}", sub, e.getMessage(), e);
            } catch (SignatureException e) {
                result = 1;
                logger.error("verifyToken SignatureException error, sub={} error msg={}", sub, e.getMessage(), e);
            }

        } catch (Exception e) {
            result = 4;
            logger.error("verifyToken unknown error, sub={} error msg={}", sub, e.getMessage(), e);
            monitorSender.info(ThirdApiLoginService.LOGIN_WARN_NAME, "调用apple verifyToken失败",
                    "msg=" + e.getMessage());

        }
        return result;
    }

    public String changeSub(String transferSub, String accessToken) {
        try {
            Map<String, String> migration = new HashMap<>();
            migration.put("transfer_sub", transferSub);
            migration.put("client_secret", NEW_CLIENT_SECRET);
            migration.put("client_id", "com.stonemobile.youstar");
            Map<String, String> header = new HashMap<>();
            header.put("Authorization", "Bearer " + accessToken);
            String resp = webClient.sendPost("https://appleid.apple.com/auth/usermigrationinfo", migration, header);
            JSONObject respResult = JSON.parseObject(resp);
            String sub = respResult.getString("sub");
            logger.info("changeSub. sub={} resp={}", sub, resp);
            return sub;
        } catch (Exception e) {
            return null;
        }
    }
}
