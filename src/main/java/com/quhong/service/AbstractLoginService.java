package com.quhong.service;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.TnRiskRecordEvent;
import com.quhong.constant.LoginConstant;
import com.quhong.constant.LoginHttpCode;
import com.quhong.constant.TnSignEnum;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.web.WebClient;
import com.quhong.data.*;
import com.quhong.data.dto.*;
import com.quhong.data.vo.*;
import com.quhong.enums.BlockTnConstant;
import com.quhong.enums.ClientOS;
import com.quhong.enums.PKGConstant;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.feign.ISundryService;
import com.quhong.handler.HttpEnvData;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.mongo.data.MongoLoginActorData;
import com.quhong.mongo.data.NewUserHonorData;
import com.quhong.mongo.data.UserMonitorData;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.AdCampaignGameData;
import com.quhong.mysql.data.GreetUserData;
import com.quhong.mysql.data.MySQLActorData;
import com.quhong.mysql.data.UserMoneyData;
import com.quhong.redis.*;
import com.quhong.service.mysql.MySQLActorService;
import com.quhong.utils.*;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


public abstract class AbstractLoginService implements ILoginService {
    private static final Logger logger = LoggerFactory.getLogger(AbstractLoginService.class);
    private static final List<String> ARAB_COUNTRY_LIST = Arrays.asList
            ("AE", "BH", "DZ", "EG", "IQ", "JO", "KW", "LB", "LY", "MA", "OM", "QA", "SA", "SD", "SY", "TN", "YE", "TR", "IR", "AF", "PS");
    private static final List<String> ARAB_LANG_LIST = Arrays.asList("ar", "tr", "pa", "fa");
    private static final List<String> PARTIAL_CODE_LIST = Arrays.asList("CN", "SA");
    private static final List<String> POWER_EMULATOR_UID = Arrays.asList("60642cf7abb01a001456afd0", "5e4949b4afe1d84c079014f6", "5ec4ec929542d3aaa93aa70c",
            "620a2386b3562a26f8bef8df", "620a216cb3562a26f8bef893", "609cb6e14467ea39b9ca7ff8");
    //# 203 疑似应⽤被注⼊  204 疑似应⽤被重打包 205 疑似使⽤hook技术 206 疑似应⽤被双开
    //    # 301 疑似主流模拟器 302 疑似云模拟器 303 疑似开发板设备 401 疑似使⽤⾃动化软件 402 疑似群控⾃动化软件
    //    # 1001 疑似设备参数篡改 1100 疑似云真机
    private static final List<Integer> DEVICE_EMULATOR_TYPE = Arrays.asList(203, 204, 205, 206, 209, 216, 301, 302, 303, 401, 402, 1001, 1100, 5010, 501, 214);
    private static final List<Integer> BEAUTIFUL_RID_LIST_ALREADY_USE = Arrays.asList(6666660, 6666666, 7000000, 7000001, 7000007, 7777775, 7777777, 8000000, 8888880, 8888888, 9090909, 9999999);
    private static final List<Integer> RID_LIST_ALREADY_USE = Arrays.asList
            (11102221, 11111111, 22201111, 22222221, 95326470, 95326471, 95326472, 95326473,
                    101000228, 101000229, 104444440, 104444441, 104444442, 104444443, 104444444, 105000000, 105555555, 106000000, 106006002, 106006006, 106666660, 106666666, 6666660, 6666666, 7000000, 7000001, 7000007, 7777775, 7777777, 8000000, 8000008, 8888880, 8888888, 9000000, 9000009, 9090909, 9740000, 9999990, 9999998, 9999999, 10000002);

    private static final int MIN_HONOR_EXP = 100000;
    private static final int MIN_LEVEL = 5;
    protected static final int GENDER_MALE = 1;
    protected static final int GENDER_FEMALE = 2;
    protected static final int TALK_VSTATUS_ACCEPT = 1;
    protected static final int TALK_VCHAT_FREE = 0;
    private static final int BORTH_AGE = 18;
    private static final int MAX_GEN_COUNT = 1000000;
    private static final int TIME_OUT_FIVE = 5;
    private static final int TIME_OUT_TEN = 10;
    private static int minGenRid = 6000000;
    private static final String NEW_GENDER = "new_gender";
    private static final String BACK_GENDER = "back_gender";
    private static final String BACK_DAY = "back_day";
    private static final boolean myDebug = true;
    public static final String DEFAULT_COUNTRY = "us_United States";
    private static Map<String, Integer> ONE_CLICK_DT = new HashMap<>();
    private static final List<String> SIM_OPERATION_LIST = Arrays.asList("中国移动", "中国联通", "中国电信", "中国广电", "China");
    private static final List<String> INSTALL_APPS_LIST = Arrays.asList("wexin", "douyin");
    private static final List<String> TIME_ZONE_LIST = Arrays.asList("Asia/Shanghai_8", "Asia/Shanghai_+8", "Asia/Chongqing_8", "Asia/Chongqing_+8", "Asia/Harbin_8", "Asia/Harbin_+8");
    private static final List<String> CHINA_MOBILE_LIST = Arrays.asList("CH");
    private static final List<String> DEVICE_EMULATOR_SHUMEI_TYPE = Arrays.asList("b_pc_emulator", "b_cloud_device", "b_phone_emulator"
            , "b_altered", "b_faker", "b_farmer", "b_multi_boxing_by_os", "b_multi_boxing_by_app", "b_multi_boxing"
            , "b_root", "b_alter_apps", "b_hook", "b_manufacture");
    public static final Map<String, Integer> SHU_MEI_RISK_MAP = new HashMap<String, Integer>() {
        {
            // fake_device 标签下
            put("b_pc_emulator", 10101);
            put("b_cloud_device", 10102);
            put("b_phone_emulator", 10103);
            put("b_altered", 10104);
            put("b_faker", 10105);
            put("b_farmer", 10106);
            put("b_multi_boxing_by_os", 10107);
            put("b_multi_boxing_by_app", 10108);
            put("b_multi_boxing", 10109);

            put("b_offerwall", 10110);
            put("b_alter_route", 10111);
            put("fake_device.b_alter_apps", 10112); // 文档有，可忽略


            //  device_suspicious_labels 标签下
            put("b_root", 10201);
            put("b_alter_apps", 10202);  // 文档没有实际返回有
            put("b_monkey_apps", 10203);
            put("b_vpn", 10204);
            put("b_vpn_apps", 10205);
            put("b_acc", 10206);
            put("b_hook", 10207);
            put("b_manufacture", 10211);
            put("b_game_cheat_apps", 10227);
            put("b_device_proxy", 10224);

            put("b_sim", 10208);
            put("b_debuggable", 10209);
            put("b_multi_boxing_apps", 10210);
            put("b_icloud", 10212);
            put("b_wx_code", 10213);
            put("b_sms_code", 10214);
            put("b_low_osver", 10215);
            put("b_remote_control_apps", 10216);
            put("b_repackage", 10217);
            put("b_alter_loc", 10218);
            put("b_reset", 10219);
            put("b_console", 10220);
            put("b_old_model", 10221);
            put("b_non_appstore", 10222);
            put("b_wangzhuan_active", 10223);
            put("b_camera_hook", 10225);
            put("b_adb_enable", 10226);


            // monkey_device 标签下
            put("monkey_device.common.b_monkey_apps", 10301);
            put("b_monkey_game_apps", 10302);
            put("b_monkey_read_apps", 10303);
        }
    };

    @Resource
    private ISundryService iSundryService;
    @Resource
    protected ThirdApiLoginService thirdApiLoginService;
    @Resource
    protected ActorDao actorDao;
    @Resource
    private CommonDao commonDao;
    @Resource
    private BlockRedis blockRedis;
    @Resource
    private UserMonitorDao userMonitorDao;
    @Resource
    private TnCheckRedis tnCheckRedis;
    @Resource
    protected NewUserHonorDao newUserHonorDao;
    @Resource
    protected UserLevelDao userLevelDao;
    @Resource
    protected LoginActorDao loginActorDao;
    @Resource
    private BasePlayerRedis basePlayerRedis;
    @Resource
    protected BaseInitData baseInitData;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    protected VipInfoDao vipInfoDao;
    @Resource
    private UserMoneyDao userMoneyDao;
    @Resource
    private MySQLActorService mySQLActorService;
    @Resource
    private GenRidRedis genRidRedis;
    @Resource
    private BeautifulRidDao beautifulRidDao;
    @Resource
    protected LoginDataCountService loginDataCountService;
    @Autowired
    protected MonitorSender monitorSender;
    @Resource
    protected CommonConfig commonConfig;
    @Resource
    protected BlockIpRedis blockIpRedis;
    @Resource
    protected CreditRiskService creditRiskService;
    @Resource
    protected WebClient webClient;
    @Resource
    protected EventReport eventReport;
    @Resource
    private TnDeviceAccountDao tnDeviceAccountDao;
    @Resource
    private AdCampaignGameDao adCampaignGameDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private GameRoomRedis gameRoomRedis;


    @PostConstruct
    public void init() {
        // 1男性触发 2女性触发  3全触发 0全都不触发
        ONE_CLICK_DT.put(NEW_GENDER, 3);
        ONE_CLICK_DT.put(BACK_GENDER, 3);
        ONE_CLICK_DT.put(BACK_DAY, 15);

        if (ServerConfig.isNotProduct()) {
            minGenRid = 1000000;
        }
    }

    public LoginRespVO registerOrLogin(RegisterOrLoginDTO dto) {
        int loginType = dto.getType();
        String loginToken = dto.getLogin_token();
        RegisterOrLoginContext context = new RegisterOrLoginContext();
        BeanUtils.copyProperties(dto, context);
        if (context.invalidPkgName()) {
            logger.info("pkg name is invalid context={}", context);
            loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
            throw new CommonException(LoginHttpCode.PARAM_ERROR);
        }
        if (!AppVersionUtils.versionCheck(8563, context)) {
            logger.info("versionCheck limit context={}", context);
            loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
            throw new CommonException(LoginHttpCode.UPDATE_APP);
        }
//        if (!StringUtils.hasLength(context.getShuMeiMsg())) {
//            logger.info("shuMeiMsg is empty return");
//            loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
//            throw new CommonException(LoginHttpCode.UPDATE_APP);
//        }
        initData(context);
        if (loginType == LoginConstant.FIRE_BASE_PHONE_TYPE || loginType == LoginConstant.SPECIAL_GUST_TYPE
                || loginType == LoginConstant.HUAWEI_PHONE_TYPE) {
            if (StringUtils.isEmpty(context.getAccount()) || StringUtils.isEmpty(context.getPassword())) {
                logger.info("account or pwd is empty context={}", context);
                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
                throw new CommonException(LoginHttpCode.PARAM_ERROR);
            }
            if (context.getAccount().length() < 7) {
                logger.info("account length low 7 context={}", context);
                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
                throw new CommonException(LoginHttpCode.PARAM_ERROR);
            }
            int newPwdLength = context.getPassword().length();
            if (newPwdLength > 12 || newPwdLength < 6) {
                logger.info("pwd length error context={} ", context);
                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
                throw new CommonException(LoginHttpCode.SPECIAL_GUST_LOGIN_PWD_ERROR);
            }
        } else {
            if (StringUtils.isEmpty(loginToken)) {
                logger.info("loginToken is empty dto={}", dto);
                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
                throw new CommonException(LoginHttpCode.PARAM_ERROR);
            }
            if (loginToken.length() == 32) {
                logger.info("loginToken length is 32 invalid token dto={}", dto);
                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
                monitorSender.info(ThirdApiLoginService.LOGIN_WARN_NAME, "客户端上报的第三方token不合法",
                        "dto=" + dto);
                throw new CommonException(LoginHttpCode.PARAM_ERROR);
            }
        }
        if (StringUtils.isEmpty(context.getTn_msg()) || context.getTn_msg().length() < 32) {
            logger.info("tn msg is empty or length lt 32 context={}", context);
            loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
            throw new CommonException(LoginHttpCode.TN_MSG_EMPTY);
        }
        context.setLoginToken(loginToken);
        saveIpCountry(context);
        saveArea(context);
        beforeLogin(context);
        if (StringUtils.isEmpty(context.getThirdUid())) {
            logger.info("login fail, get third uid fail context={}", context);
            int errorType = getErrorType(context.getType());
            loginDataCountService.recordLoginStatusToTga(context, errorType);
            throw new CommonException(LoginHttpCode.LOGIN_FAIL);
        }
        DistributeLock lock = new DistributeLock(getLockKey(context.getThirdUid()));
        try {
            boolean ret = lock.tryLock(TIME_OUT_TEN, TimeUnit.SECONDS);
            if (!ret) {
                logger.error("Distributed Lock Timeout context={}", context);
                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_TIME_OUT);
                throw new CommonException(LoginHttpCode.LOCK_TIMEOUT);
            }
            boolean isMustLogin = false;
            MongoLoginActorData userActorData;
            if ((context.getType() == LoginConstant.FIRE_BASE_PHONE_TYPE && context.isCheckPhoneAccount())
                    || (context.getType() == LoginConstant.HUAWEI_PHONE_TYPE && context.isCheckPhoneAccount())
                    || context.getType() == LoginConstant.SPECIAL_GUST_TYPE) {
                userActorData = commonDao.findOne
                        (new Query(Criteria.where("_id").is(new ObjectId(context.getThirdUid()))), MongoLoginActorData.class);
                isMustLogin = true;
            } else {
                userActorData = commonDao.findOne
                        (new Query(Criteria.where("uid").is(context.getThirdUid()).and("login_type").is(context.getType())), MongoLoginActorData.class);
//                if (userActorData == null && context.getExistPhoneAccount() != null) {
//                    String phoneUid = context.getExistPhoneAccount().getUid();
//                    userActorData = commonDao.findOne
//                            (new Query(Criteria.where("_id").is(new ObjectId(phoneUid))), MongoLoginActorData.class);
//                    logger.info("maybe change phone login type phoneUid:{} loginType:{} success userActorData:{}",
//                            phoneUid, context.getType(), userActorData);
//                }
            }
            if (userActorData == null) {
                if (context.getType() == LoginConstant.FIRE_BASE_MAIL_TYPE) {
                    loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
                    logger.info("Email registration has been disabled. context={}", context);
                    throw new CommonException(LoginHttpCode.EMAIL_LOGIN_REFUSE);
                } else if (isMustLogin) {
                    loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_OTHER_ERROR);
                    logger.info("login fail, must login but not find actor context={}", context);
                    throw new CommonException(LoginHttpCode.LOGIN_FAIL);
                }
                context.setRegister(true);
                saveTn(context);
//                saveShuMei(context);
                checkIp(context);
                checkDevice(context);
                saveName(context);
                register(context);
            } else {
                context.setRegister(false);
                context.setMongoLoginActorData(userActorData);
                context.setUid(userActorData.get_id().toString());
                context.setRid(userActorData.getRid());
                context.setRidData(userActorData.getRidData());
                if (isMustLogin) {
                    context.setThirdUid(userActorData.getUid());
                }
                checkAccount(context);
                saveTn(context);
//                saveShuMei(context);
                checkIp(context);
                checkDevice(context);
                saveName(context);
                login(context);
            }
            addAccountToDevice(context);
            afterLogin(context);

        } catch (InterruptedException e) {
            loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_OTHER_ERROR);
            logger.error("InterruptedException context={}", context, e);
            throw new CommonException(LoginHttpCode.LOGIN_FAIL);
        } finally {
            lock.unlock();
        }
        saveToken(context);
        loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SUCCESS);
        loginDataCountService.asynMainTask(context);
        LoginRespVO vo = fillLoginRespVO(context);
        logger.info("login-register-success, context={} ****return***vo={}", context, vo);
        return vo;
    }

    protected LoginRespVO fillLoginRespVO(RegisterOrLoginContext context) {
        LoginRespVO loginRespVO = new LoginRespVO();
        loginRespVO.setUid(context.getUid());
        loginRespVO.setGender(context.getFbGender());
        loginRespVO.setAccept_talk(context.getAcceptTalk());
        loginRespVO.setIsnew(context.getIsNew());
        loginRespVO.setHead(context.getToClientHead());
        loginRespVO.setName(context.getToClientName());
        loginRespVO.setBirthday(context.getBirthday());
        loginRespVO.setAnchor(context.getAnchor());
        loginRespVO.setRoomId(context.getToClientRoomId());
        loginRespVO.setRid(context.getRid());
        loginRespVO.setRidData(context.getRidData());
        loginRespVO.setNlang(context.getToClientNlang());
        loginRespVO.setUlvl(context.getuLvl());
        loginRespVO.setViplevel(context.getVipLevel());
        loginRespVO.setCountry(StringUtils.isEmpty(context.getToClientCountry()) ? "" : context.getToClientCountry());
        loginRespVO.setLicense(0);
        loginRespVO.setIs_open(0);
        loginRespVO.setOne_click_gd(0);
        loginRespVO.setOne_click_sw(0);
        loginRespVO.setType(context.getType());
        loginRespVO.setOpen_id(StringUtils.isEmpty(context.getTnId()) ? "" : context.getTnId());
        loginRespVO.setToken(context.getToClientToken());
        int isReg = context.isRegister() ? 1
                : context.getMongoLoginActorData().getUid().startsWith(LoginConstant.TEST_USER_PRE) && context.getIsNew() == 1
                ? 1 : 0;
        loginRespVO.setIsRegister(isReg);
        JSONObject switchConfig = commonConfig.getSwitchConfig();
        String lastChar = context.getUid().substring(context.getUid().length() - 1);
        JSONArray jsonArray = switchConfig.getJSONArray("ta_log_char");
        if (jsonArray.contains(lastChar)) {
            loginRespVO.setTaLogSwitch(1);
        } else {
            loginRespVO.setTaLogSwitch(0);
        }

        int logPageDay = switchConfig.getIntValue("ta_log_page_sw");
        if (logPageDay > 0) {
            loginRespVO.setTaLogAppPageSwitch(ActorUtils.isNewRegisterActor
                    (context.getUid(), logPageDay) ? 1 : 0);
        } else {
            loginRespVO.setTaLogAppPageSwitch(0);
        }
        fillEntryTab(loginRespVO, context);
        loginRespVO.setIsNewUser(context.getIsNewUser());
        return loginRespVO;
    }

    private void fillEntryTab(LoginRespVO loginRespVO, RegisterOrLoginContext context) {

        String ipCountry = ObjectUtils.isEmpty(context.getIpCountry()) ? "" : context.getIpCountry().toUpperCase();
        // logger.info("fillEntryTab uid={} ipCountry={}", loginRespVO.getUid(), ipCountry);
        // 进入社交页面不进入
        if (PARTIAL_CODE_LIST.contains(ipCountry)) {
            char ch = ActorUtils.getUidEndChar(loginRespVO.getUid());
            if (ch >= '0' && ch <= '7') {
                loginRespVO.setHome_tab(5);
            } else {
                loginRespVO.setHome_tab(6);
            }
        }else {
            if (ActorUtils.isNewRegisterActor(loginRespVO.getUid(), 1)) {
                loginRespVO.setHome_tab(6);
            } else {
                loginRespVO.setHome_tab(7);
            }
            loginRespVO.setDiscover_tab(1);
        }
        Map<String, Object> properties = new HashMap<>();
        properties.put("diversion_rule_mark", String.valueOf(loginRespVO.getHome_tab()));
        eventReport.userSet(new EventDTO(loginRespVO.getUid(), "", properties));
    }

    private void saveIpCountry(RegisterOrLoginContext context) {
        QueryCountryByIpDTO qDTO = new QueryCountryByIpDTO();
        qDTO.setIp(context.getIp());
        qDTO.setOutId(context.getRequestId());
        QueryCountryVO queryCountryVO = iSundryService.queryCountryByIp(qDTO).getData();
        String ipCountry = queryCountryVO == null ? "" : queryCountryVO.getCode();
        context.setIpCountry(ipCountry);
        context.setIpCountryName(queryCountryVO == null ? "" : queryCountryVO.getCountry());
    }

    private String getLockKey(String id) {
        return "register_or_login_" + id;
    }

    private String getGenRidLockKey() {
        return "java_gen_rid_register";
    }

    protected String handleArbNum(String oldAccount) {
        char[] aa = oldAccount.toCharArray();
        StringBuilder newAccount = new StringBuilder();
        for (Character ch : aa) {
            newAccount.append(parseArb(ch.toString()));
        }
        return newAccount.toString();
    }

    private String parseArb(String userPwd) {
        String userStrPwd = userPwd;
        try {
            // 针对阿语数字进行特殊处理
            // ARABIC_INDIC = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٩',','۸']
            userStrPwd = Integer.parseInt(userPwd) + "";
        } catch (NumberFormatException e) {
//            logger.info("userPwd={} error msg={}", userPwd, e.getMessage());
        }
        return userStrPwd;
    }

    private void saveArea(RegisterOrLoginContext context) {
        int area = 1;
        if (ARAB_COUNTRY_LIST.contains(context.getIpCountry())) {
            area = 2;
        } else {
            if (!StringUtils.isEmpty(context.getLang()) && context.getLang().length() >= 2) {
                if (ARAB_LANG_LIST.contains(context.getLang().substring(0, 2))) {
                    area = 2;
                }
            }
        }
        context.setArea(area);
    }

    private void saveName(RegisterOrLoginContext context) {
        String name = "user" + ThreadLocalRandom.current().nextInt(100000, 999999);
        if (context.isRegister()) {
            if (context.getType() == LoginConstant.FIRE_BASE_PHONE_TYPE) {
                name = baseInitData.generateSysNewRandomName(context.getGender(), context.getSlang());
//                name = "NewStar" + ThreadLocalRandom.current().nextInt(10, 99);
            } else if (StringUtils.isEmpty(context.getName())) {
                name = baseInitData.generateSysNewRandomName(context.getGender(), context.getSlang());
//                name = "YouStar" + ThreadLocalRandom.current().nextInt(100000, 999999);
            } else {
                if (!context.getName().toLowerCase().contains("vip")) {
                    if (context.getName().length() >= 16) {
                        name = context.getName().substring(0, 16);
                    } else {
                        name = context.getName();
                    }
                }
            }
        }
        context.setRandomName(name);
    }

    private void saveShuMei(RegisterOrLoginContext context) {
        ShuMeiDeviceDTO dto = new ShuMeiDeviceDTO();
        dto.setShuMeiMsg(context.getShuMeiMsg());
        dto.setUid(context.getUid());
        dto.setTnId(context.getTnId());
        dto.setTnRisk(context.getTnRisk());
        dto.setReqTn(!context.isUseCacheTn());

        dto.setOs(context.getOs());
        dto.setApp_package_name(context.getApp_package_name());
        dto.setVname(context.getVname());
        dto.setVersioncode(context.getVersioncode());
        dto.setFromInterface("registerOrLogin");
        SMDeviceRespondsData smDeviceRespondsData = thirdApiLoginService.getShuMeiDeviceData(dto);
        if (smDeviceRespondsData != null &&
                StringUtils.hasLength(smDeviceRespondsData.getDeviceLabels().getId())) {
            context.setShuMeiId(smDeviceRespondsData.getDeviceLabels().getId());
            context.setShuMeiRisk(genShuMeiRisk(smDeviceRespondsData));
            context.setSmDeviceRespondsData(smDeviceRespondsData);
        }

    }


    private void saveTn(RegisterOrLoginContext context) {
        saveTn(context, false);
    }

    private void saveTn(RegisterOrLoginContext context, boolean isReqTn) {
        String oldTnId = null;
        if (!isReqTn && !context.isRegister()) {
            MongoLoginActorData mongoLoginActorData = context.getMongoLoginActorData();
            oldTnId = mongoLoginActorData.getTn_id();
            String reqTnId = context.getOpen_id();
            if (!StringUtils.isEmpty(oldTnId) && !StringUtils.isEmpty(reqTnId) && reqTnId.equals(oldTnId)) {
                context.setTnRisk(mongoLoginActorData.getTn_risk());
                //风险设备每次都请求图灵顿
                if (checkDeviceEmulatorLimit(context)) {
                    context.setTnId(oldTnId);
                    context.setUseCacheTn(true);
                    doReportTnRiskRecordEvent(context.getUid(), context.getTnId(), context.getTnRisk(), true);
                    logger.info("saveTn use cache tnId={} tnRisk={}", context.getTnId(), context.getTnRisk());
                    return;
                }
            }
        }
//        if (StringUtils.isEmpty(context.getTn_msg())) {
//            logger.info("send post. url={} req={} context={}", TN_POST_URL, req.toJSONString(), context);
//            monitorSender.info(LoginConstant.USTAR_JAVA_EXCEPTION_WARN_NAME, "调用" + TN_POST_URL + "失败",
//                    "responseData is null" + " req=" + req.toJSONString() + " context=" + context);
//            if (context.isRegister()) {
//                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
//                throw new CommonException(LoginHttpCode.TN_MSG_EMPTY);
//            } else {
//                MongoLoginActorData mongoLoginActorData = context.getMongoLoginActorData();
//                oldTnId = mongoLoginActorData.getTn_id();
//                String reqId = getReqId(context);
//                String myTnId = !StringUtils.isEmpty(oldTnId) ? oldTnId :
//                        !StringUtils.isEmpty(reqId) ? "my-" + reqId : "my-" + new ObjectId();
//                TnRespondsData tnData = new TnRespondsData();
//                tnData.setRet(0);
//                tnData.setOpenId(myTnId);
//                tnData.setExtraInfo(Collections.emptyMap());
//                context.setTnId(myTnId);
//                context.setTnRisk(Collections.emptyList());
//                context.setTnRespondsData(tnData);
//            }
//        } else {
//        TnRespondsData tnData = thirdApiLoginService.getTnDeviceData(context);
        TnRespondsData tnData = thirdApiLoginService.getTnDeviceDataNew(context);
        if (tnData != null) {
            if (tnData.getRet() != 0) {
                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_THIRD_LOGIN_FAIL_TN);
                logger.info("login fail, tnData !=0 ret={} context={}", tnData.getRet(), context);
                throw new CommonException(LoginHttpCode.LOGIN_FAIL);
            }
            if (!StringUtils.isEmpty(tnData.getOpenId())) {
                context.setTnId(tnData.getOpenId());
                context.setTnRisk(null == tnData.getRiskObj() ? new ArrayList<>() : tnData.getRiskObj());
                context.setTnRespondsData(tnData);
                doReportTnRiskRecordEvent(context.getUid(), context.getTnId(), context.getTnRisk(), false);
            } else {
                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_THIRD_LOGIN_FAIL_TN);
                logger.info("login fail, tnData openId is empty ret={} context={}", tnData.getRet(), context);
                throw new CommonException(LoginHttpCode.LOGIN_FAIL);
            }
            if(!context.isUseCacheTn()) {
                logger.info("saveTn use api  isRegister={} tnId={} tnRisk={}", context.isRegister(), context.getTnId(), context.getTnRisk());
            }
        } else {
            loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_THIRD_LOGIN_FAIL_TN);
            logger.info("login fail, tnData is null context={}", context);
            throw new CommonException(LoginHttpCode.LOGIN_FAIL);
        }
//        }

    }


    private void checkAccount(RegisterOrLoginContext context) {
        MongoLoginActorData actorData = context.getMongoLoginActorData();
        if (actorData != null && actorData.getValid() == 0) {
            logger.info("account limit showMsg={} context={}", actorData.getRid(), context);
            String showMsg = actorData.getRid() + "";
            CommonException e = new CommonException(LoginHttpCode.ACCOUNT_LOGIN_BLOCK_BAN, showMsg);
            AccountBlockRspVO vo = getAccountBlockRspVO(actorData.get_id().toString(), actorData.getRid(), context.getSlang());
            e.setData(vo);
            loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_ACCOUNT_INVALID);
            throw e;
        }
    }

    public AccountBlockRspVO getAccountBlockRspVO(String uid, int rid, int slang) {
        AccountBlockRspVO vo = new AccountBlockRspVO();
        String reason = slang == SLangType.ENGLISH ? "Your account has been blocked" : "حسابك محظور";
        int blockType = 3;
        UserMonitorData userMonitorData = userMonitorDao.findBanData(uid);
        if (null != userMonitorData) {
            // 封禁 1 24小时 , 2 7天 , 5 30天,  3 永久 ,其他 永久
            blockType = "1".equals(userMonitorData.getBlock_term())
                    ? 1 : "2".equals(userMonitorData.getBlock_term()) ?
                    2 : "5".equals(userMonitorData.getBlock_term()) ? 5 : 3;
            if (userMonitorData.getReason_type() == 1) {
                reason = slang == SLangType.ENGLISH ? "Publish pornographic material and indecent contents."
                        : "نشر مواد إباحية ومحتويات غير لائقة.";
            } else if (userMonitorData.getReason_type() == 2) {
                reason = slang == SLangType.ENGLISH ? "Obtaining or reselling diamonds illegally"
                        : "سرقة الألماس وإعادة بيعها بشكل غير قانوني";
            } else if (userMonitorData.getReason_type() == 3) {
                reason = slang == SLangType.ENGLISH ? "Insult the reputation of the platform and damage the official image"
                        : "اهانة سمعة المنصة وتدمير صورة رسمية البرنامج";
            } else if (userMonitorData.getReason_type() == 4) {
                reason = slang == SLangType.ENGLISH ? "Abuse others and damage the atmosphere of community"
                        : "الشتم للآخرين والإضرار ببيئة البرنامج";
            } else {
                reason = StringUtils.isEmpty(userMonitorData.getReason()) ? "" : userMonitorData.getReason();
            }
        }
        vo.setRid(rid);
        vo.setBlock_type(blockType);
        vo.setReason(reason);
        return vo;
    }


    private String toStrUid(MongoActorData actor) {
        return actor.get_id().toString();
    }

    private boolean isValidHonorDevice(RegisterOrLoginContext context) {
        String tnId = context.getTnId();
        List<MongoActorData> allLsit = actorDao.findListByTnId(tnId);
        if (CollectionUtils.isEmpty(allLsit)) {
            context.setDeviceAllUid(new HashSet<>());
            // 没有可判断的账号，返回失败
            return false;
        } else {
            Set<String> allUid = allLsit.stream().map(this::toStrUid).collect(Collectors.toSet());
            context.setDeviceAllUid(allUid);
            for (String item : allUid) {
                NewUserHonorData honorData = newUserHonorDao.findData(item);
                long honorExp = honorData == null ? 0 : honorData.getBeans();
                if (honorExp >= MIN_HONOR_EXP) {
                    String useTnId = tnCheckRedis.getTnIdByHonorHash(item);
                    if (StringUtils.isEmpty(useTnId) || tnId.equals(useTnId)) {
                        tnCheckRedis.addToHonorHash(item, tnId);
                        tnCheckRedis.setDeviceLimitNum(tnId, LoginConstant.DEVICE_LIMIT_ACCOUNT_NUM.get(2));
                        context.setDeviceHonorUid(item);
                        logger.info("use honor success use_uid={} nowTnId={} honorExp={}"
                                , item, tnId, honorExp);
                        return true;
                    } else {
                        logger.info("already use honor use_uid={} useTnId={} nowTnId={} honorExp={}"
                                , item, useTnId, tnId, honorExp);
                    }
                }
            }
        }
        return false;
    }

    private boolean isValidLevelDevice(RegisterOrLoginContext context) {
        String tnId = context.getTnId();
        Set<String> allUid = context.getDeviceAllUid();
        if (allUid == null) {
            List<MongoActorData> allLsit = actorDao.findListByTnId(tnId);
            if (CollectionUtils.isEmpty(allLsit)) {
                context.setDeviceAllUid(new HashSet<>());
                return false;
            }
            allUid = allLsit.stream().map(this::toStrUid).collect(Collectors.toSet());
            context.setDeviceAllUid(allUid);
        }
        for (String item : allUid) {
            int level = userLevelDao.getUserLevel(item);
            if (level >= MIN_LEVEL) {
                String useTnId = tnCheckRedis.getTnIdByLevelHash(item);
                if (StringUtils.isEmpty(useTnId) || tnId.equals(useTnId)) {
                    tnCheckRedis.addToLevelHash(item, tnId);
                    tnCheckRedis.setDeviceLimitNum(tnId, LoginConstant.DEVICE_LIMIT_ACCOUNT_NUM.get(1));
                    context.setDeviceLevelUid(item);
                    logger.info("use level success use_uid={} nowTnId={} level={}"
                            , item, tnId, level);
                    return true;
                } else {
                    logger.info("already use level use_uid={} useTnId={} nowTnId={} level={}"
                            , item, useTnId, tnId, level);
                }
            }
        }
        return false;

    }

    private void addAccountToDevice(RegisterOrLoginContext context) {
        if (context.isCheckDeviceAccountNum() && null != context.getNowDeviceAccountNum()) {
            tnCheckRedis.setDeviceNowNum(context.getTnId(), context.getNowDeviceAccountNum());
        }
    }

    private boolean checkDeviceAccountLimit(RegisterOrLoginContext context) {
        String tnId = context.getTnId();
        boolean isEnter = false;
        int nowNum = tnCheckRedis.getDeviceNowNum(tnId) + 1;

        int maxNum = ServerConfig.isNotProduct() ? LoginConstant.DEVICE_LIMIT_ACCOUNT_TEST_MAX_NUM :
                LoginConstant.DEVICE_LIMIT_ACCOUNT_NUM.get(3);

        if (nowNum > maxNum) {
//            isEnter = false;
        } else if (nowNum > LoginConstant.DEVICE_LIMIT_ACCOUNT_NUM.get(2)) {
            // (30,50]
            isEnter = tnCheckRedis.isWhite(tnId);
        } else if (nowNum > LoginConstant.DEVICE_LIMIT_ACCOUNT_NUM.get(1)) {
            // (20,30]
            isEnter = tnCheckRedis.isWhite(tnId) || (tnCheckRedis.getDeviceLimitNum(tnId) >= nowNum)
                    || isValidHonorDevice(context);

        } else if (nowNum > LoginConstant.DEVICE_LIMIT_ACCOUNT_NUM.get(0)) {
            // (3,20]
            isEnter = tnCheckRedis.isWhite(tnId) || (tnCheckRedis.getDeviceLimitNum(tnId) >= nowNum)
                    || isValidHonorDevice(context) || isValidLevelDevice(context);
        } else {
            isEnter = true;
        }
        if (isEnter) {
            context.setNowDeviceAccountNum(nowNum);
            context.setCheckDeviceAccountNum(true);
        } else {
            context.setNowDeviceAccountNum(nowNum - 1);
        }
        logger.info("now device add account after num={} isEnter={} context={}", nowNum, isEnter, context);
        return isEnter;
    }

    private void fillDeviceAreaLimit(RegisterOrLoginContext context) {
        if (ServerConfig.isNotProduct() && myDebug) {
            context.setArb(true);
            context.setWhite(true);
        } else {
            IsLimitUserDTO req = new IsLimitUserDTO();
            req.setIp(context.getIp());
            req.setIpCountry(context.getIpCountry());
            req.setTnId(context.getTnId());
            req.setLang(context.getLang());
            req.setUid(context.getUid());
            req.setOs(context.getOs());
            req.setVersioncode(context.getVersioncode());
            req.setLoginType(context.getType());
            req.setChannel(context.getChannel());
            IsNotLimitUseVO vo = iSundryService.isNotLimitUse(req).getData();
            logger.info("checkDeviceAreaLimit req={} ----> vo={}", req, vo);
            if (vo != null) {
                context.setArb(vo.isArb());
                context.setWhite(vo.isWhite());
                if (!isDeviceNewContinue(context.getDeviceInfo())) {
                    context.setArb(false);
                }
            } else {
                context.setArb(true);
                context.setWhite(false);
            }
        }
    }

    private boolean isDeviceNewContinue(RegisterOrLoginDTO.DeviceInfo deviceInfo) {
//        logger.info("deviceInfo:{}", JSON.toJSONString(deviceInfo));
        if (deviceInfo == null) {
            return true;
        }
        String simOperation = deviceInfo.getSimOperation();
        String timeZone = deviceInfo.getTimeZone();
        String chinaMobile = deviceInfo.getChinaMobile();
        List<String> installApps = deviceInfo.getInstallApps();

        if (!filterDeviceContinue(simOperation, SIM_OPERATION_LIST)) {
            return false;
        }
        if (!filterDeviceContinue(timeZone, TIME_ZONE_LIST)) {
            return false;
        }
        if (!filterDeviceContinue(chinaMobile, CHINA_MOBILE_LIST)) {
            return false;
        }

        if (CollectionUtils.isEmpty(installApps)) {
            return true;
        }
        // 命中数量大于等于2才拦截
        Set<String> appSet = new HashSet<>();
        for (String item : installApps) {
            if (INSTALL_APPS_LIST.contains(item)) {
                appSet.add(item);
            }
        }
        if (appSet.size() >= 2) {
            logger.info("appSet={}", Arrays.toString(appSet.toArray()));
            return false;
        }
        return true;
    }

    private boolean filterDeviceContinue(String key, List<String> filterList) {
        return filterDeviceContinue(key, filterList, false);
    }

    private boolean filterDeviceContinue(String key, List<String> filterList, boolean isSplit) {
        if (!StringUtils.hasLength(key)) {
            return true;
        }
        if (isSplit) {
            String[] split = key.split("_");
            key = split[split.length - 1];
        }
        for (String item : filterList) {
            if (key.contains(item)) {
//                logger.info("key:{} contains item:{}", key, item);
                return false;
            }
        }
        return true;
    }

    private List<Integer> getLimitList(JSONObject limitConfig, String key, List<Integer> defaultValue) {
        try {
            if (limitConfig != null) {
                JSONArray jsonArray = limitConfig.getJSONArray(key);
                return jsonArray.toJavaList(Integer.class);
            }
        } catch (Exception e) {
            logger.error("getLimitList error key:{} msg:{}", key, e.getMessage(), e);
        }
        return defaultValue;
    }

    private List<String> getShuMeiLimitList(JSONObject limitConfig, String key, List<String> defaultValue) {
        try {
            if (limitConfig != null) {
                JSONArray jsonArray = limitConfig.getJSONArray(key);
                return jsonArray.toJavaList(String.class);
            }
        } catch (Exception e) {
            logger.error("getShuMeiLimitList error key:{} msg:{}", key, e.getMessage(), e);
        }
        return defaultValue;
    }


    private boolean checkDeviceEmulatorLimit(RegisterOrLoginContext context) {
        List<Integer> emulatorList = getLimitList(commonConfig.getLoginConfig(), "limit_emulator", DEVICE_EMULATOR_TYPE);
        List<String> shuMeiEmulatorList = getShuMeiLimitList(commonConfig.getLoginConfig(),
                "shumei_limit_emulator", DEVICE_EMULATOR_SHUMEI_TYPE);
        logger.info("shuMeiEmulatorList:{} user-shuMeiRisk:{} tnEmulatorList:{} user-tnRisk:{}",
                shuMeiEmulatorList, context.getShuMeiRisk(), emulatorList, context.getTnRisk());
        if (ServerConfig.isNotProduct() && myDebug) {
            return true;
        }
//        if (StringUtils.hasLength(context.getShuMeiId())) {
//            List<String> shuMeiRisk = context.getShuMeiRisk();
//            if (!CollectionUtils.isEmpty(shuMeiRisk)) {
//                for (String item : shuMeiRisk) {
//                    if (shuMeiEmulatorList.contains(item)) {
//                        return false;
//                    }
//                }
//            }
//        } else {
//            List<Integer> tnRisk = context.getTnRisk();
//            if (!CollectionUtils.isEmpty(tnRisk)) {
//                for (Integer item : tnRisk) {
//                    if (emulatorList.contains(item)) {
//                        return false;
//                    }
//                }
//            }
//        }

        List<Integer> tnRisk = context.getTnRisk();
        if (!CollectionUtils.isEmpty(tnRisk)) {
            for (Integer item : tnRisk) {
                if (emulatorList.contains(item)) {
                    return false;
                }
            }
        }
        return true;
    }

    private void fillTnUserLimit(RegisterOrLoginContext context) {
        if (ServerConfig.isNotProduct() && myDebug) {
            context.setLimitUserByTn(false);
        }
        if (!StringUtils.isEmpty(context.getUid()) && POWER_EMULATOR_UID.contains(context.getUid())) {
            context.setLimitUserByTn(false);
        }
        if (context.isWhite()) {
            context.setLimitUserByTn(false);
        }
        List<Integer> tnRisk = context.getTnRisk();
        if (!CollectionUtils.isEmpty(tnRisk)) {
            for (Integer item : tnRisk) {
                if (LoginConstant.TN_LIMIT_TYPE.contains(item)) {
                    context.setLimitUserByTn(true);
                    break;
                }
            }
        }
        context.setLimitUserByTn(false);
    }

    private void checkDevice(RegisterOrLoginContext context) {
        if (StringUtils.isEmpty(context.getTnId())) {
            MongoLoginActorData actorData = context.getMongoLoginActorData();
            if (null != actorData && actorData.getIs_delete() == 1) {
                ShowIdData showBean = getShowBean(context);
                String showId = showBean.getShowId();
                logger.info("device account limit tnId is empty showBean={} context={}", showBean, context);
                CommonException e = new CommonException(LoginHttpCode.DEVICE_LOGIN_LIMIT_ACCOUNT, showId);
                OpenIdVO vo = new OpenIdVO();
                vo.setOpen_id(context.getTnId());
                e.setData(vo);
                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_DEVICE_NUM);
                throw e;
            }
            logger.info("device tnId is empty return context={}", context);
            return;
        }

        // 1 设备语言环境或ip限制
        fillDeviceAreaLimit(context);
        if (!context.isWhite() && !context.isArb()) {
            ShowIdData showBean = getShowBean(context);
            logger.info("device area limit tnId={} shuMeiId={} showBean={} lang={} ipCountry={} deviceInfo:{}",
                    context.getTnId(), context.getShuMeiId(),
                    showBean, context.getLang(), context.getIpCountry()
                    , JSON.toJSONString(context.getDeviceInfo()));
            CommonException e = new CommonException(LoginHttpCode.DEVICE_LOGIN_LIMIT_AREA, showBean.getShowId());
            OpenIdVO vo = new OpenIdVO();
            vo.setOpen_id(context.getTnId());
            e.setData(vo);
            loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_DEVICE_LANGUAGE);
            throw e;
        }

        // 2 设备参数，模拟器限制
        if (!context.isWhite() && !checkDeviceEmulatorLimit(context)) {
            ShowIdData showBean = getShowBean(context);
            String showId = showBean.getShowId();
//            List<Integer> deviceRisk = StringUtils.isEmpty(context.getShuMeiId()) ? context.getTnRisk() :
//                    context.getShuMeiRisk().stream().map(k -> SHU_MEI_RISK_MAP.getOrDefault(k, -999)).collect(Collectors.toList());
            List<Integer> deviceRisk = context.getTnRisk();
            logger.info("device emulator limit tnId={} shuMeiId={} showBean={} deviceRisk={}",
                    context.getTnId(), context.getShuMeiId(), showBean, deviceRisk);
            CommonException e = new CommonException(LoginHttpCode.DEVICE_LOGIN_LIMIT_EMULATOR, showId, deviceRisk);
            OpenIdVO vo = new OpenIdVO();
            vo.setOpen_id("fake"); // 保证风险设备每次都请求图灵顿
            e.setData(vo);
            loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_DEVICE_EMULATOR);
            throw e;
        }
        // 3 设备被封限制
        String blockTime = blockRedis.checkBlock(context.getTnId(), BlockTnConstant.BLOCK_LOGIN);
        if (!StringUtils.isEmpty(blockTime)) {
            ShowIdData showBean = getShowBean(context);
            logger.info("device ban limit showBean={} tnId={}", showBean, context.getTnId());
            CommonException e = new CommonException(LoginHttpCode.DEVICE_LOGIN_LIMIT_BAN, blockTime);
            ShowOpenIdVO vo = new ShowOpenIdVO();
            vo.setOpen_id(context.getTnId());
            vo.setRid(showBean.getRid());
            vo.setUid(showBean.getUid());
            e.setData(vo);
            loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_DEVICE_INVALID, true);
            throw e;
        }

        MongoLoginActorData actorData = context.getMongoLoginActorData();
        if (context.isRegister() || (null != actorData && actorData.getIs_delete() == 1)) {
            // 4 设备已注册账号数限制
            if (!checkDeviceAccountLimit(context)) {
                ShowIdData showBean = getShowBean(context);
                String showId = showBean.getShowId();
                logger.info("device account limit showBean={}", showBean);
                CommonException e = new CommonException(LoginHttpCode.DEVICE_LOGIN_LIMIT_ACCOUNT, showId);
                OpenIdVO vo = new OpenIdVO();
                vo.setOpen_id(context.getTnId());
                e.setData(vo);
                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_DEVICE_NUM);
                throw e;
            }
        }
//        fillTnUserLimit(context);
//        if (context.isLimitUserByTn()) {
//            // 设备被风控系统限制
//            ShowIdData showBean = getShowBean(context);
//            String showId = showBean.getShowId();
//            logger.info("credit risk device tn limit user showBean={} risk={} context={}", showBean, context.getTnRisk(), context);
//            CommonException e = new CommonException(LoginHttpCode.DEVICE_LOGIN_LIMIT_EMULATOR, showId, context.getTnRisk());
//            OpenIdVO vo = new OpenIdVO();
//            vo.setOpen_id("fake"); // 保证每次都请求图灵顿
//            e.setData(vo);
//            loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_DEVICE_LIMIT_USER);
//            throw e;
//        }
//        int score = creditRiskService.getCreditRiskScoreByThirdUid(context.getThirdUid(), context.getTnId(), context.getIp());
//        if (score <= 0) {
//            // 账号分为0,风控系统限制
//            ShowIdData showBean = getShowBean(context);
//            String showId = showBean.getShowId();
//            logger.info("credit risk third user empty score={} showBean={} risk={} context={}", score, showBean, context.getTnRisk(), context);
//            CommonException e = new CommonException(LoginHttpCode.ACCOUNT_RISK_ZERO, showId, context.getTnRisk());
//            OpenIdVO vo = new OpenIdVO();
//            vo.setOpen_id("fake"); // 保证每次都请求图灵顿
//            e.setData(vo);
//            loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_ACCOUNT_RISK_ZERO);
//            throw e;
//        }
    }


    private void checkIp(RegisterOrLoginContext context) {
        if (blockIpRedis.isBlockIp(context.getIp())) {
            if (context.isUseCacheTn()) {
                // 风险ip请求图灵顿
                saveTn(context, true);
            }
            ShowIdData showBean = getShowBean(context);
            String showId = showBean.getShowId();
            int nowTime = DateHelper.getNowSeconds();
            logger.info("user limit ip={} showId={} try_uid={} nowTime={} context={}", context.getIp(), showId, showBean.getUid(), nowTime, context);
            CommonException e = new CommonException(LoginHttpCode.DEVICE_LOGIN_LIMIT_IP, nowTime);
            loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_IP_LIMIT_ERROR);
            String opt = context.isRegister() ? "注册" : "登入";
//            JSONObject jsonObject = new JSONObject(true);
//            jsonObject.put("告警描述", "非法IP尝试" + opt + "youstar，疑似骚扰，请市场运营关注此用户");
//            jsonObject.put("相关用户id", StringUtils.isEmpty(showBean.getUid())?"":showBean.getShowId());
//            jsonObject.put("IP地址", context.getIp());
//            jsonObject.put("IP所属国家区域",context.getIpCountryName());
//            jsonObject.put("图灵顿设备id", null == context.getTnId() ? "" : context.getTnId());
//            jsonObject.put("图灵顿风险标签", null == context.getTnRisk() ? "" : context.getTnRisk().toString());
//            jsonObject.put("告警时间戳", nowTime);
//            String content = JSON.toJSONString(jsonObject, SerializerFeature.PrettyFormat,
//                    SerializerFeature.WriteMapNullValue,SerializerFeature.WriteDateUseDateFormat,
//                    SerializerFeature.WriteNullListAsEmpty);
            String content = "告警描述:非法IP尝试" + opt + "youstar，疑似骚扰，请市场运营关注此用户\n"
                    + ">相关用户id: " + (StringUtils.isEmpty(showBean.getUid()) ? "" : showBean.getShowId()) + "\n"
                    + ">IP地址: " + context.getIp() + "\n"
                    + ">IP所属国家区域: " + context.getIpCountryName() + "\n"
                    + ">图灵顿设备id: " + (null == context.getTnId() ? "" : context.getTnId()) + "\n"
                    + ">图灵顿风险标签: " + getTnTagMsg(context.getTnRisk()) + "\n"
                    + ">告警时间戳: " + nowTime;
            loginDataCountService.noticeWarn(content);
            throw e;
        }
    }

    private String getLongCountryName(RegisterOrLoginContext context) {
        String countryName = context.getIpCountry();
        if (!StringUtils.isEmpty(context.getIpCountryName())) {
            return countryName + "_" + context.getIpCountryName();
        }
        return countryName;
    }

    private void register(RegisterOrLoginContext context) {
        MongoLoginActorData registerData = new MongoLoginActorData();

        int fbGender = GENDER_MALE;
        int improveData = 0;
        if (AppVersionUtils.versionCheck(825, context)) {
//            improveData = 1;
            if (context.getType() == LoginConstant.FACE_BOOK_TYPE &&
                    (context.getGender() == GENDER_MALE || context.getGender() == GENDER_FEMALE)) {
                fbGender = context.getGender();
            }
        }
        Map<String, Object> generalConf = new HashMap<>();
        generalConf.put("fill_gender", 0);
        generalConf.put("fill_done", 0);
//        if (context.getType() == LoginConstant.GOOGLE_TYPE || context.getType() == LoginConstant.FACE_BOOK_TYPE) {
//            generalConf.put("fill_done", 1);
//        }
        String appPkgName = context.getApp_package_name() == null ? "" : context.getApp_package_name().trim();
        if (context.getOs() == ClientOS.IOS && StringUtils.isEmpty(appPkgName)) {
            appPkgName = PKGConstant.IOS_MAIN;
        }
//        String head = baseInitData.generateRandomHead(fbGender);
        String head = baseInitData.generateSysNewRandomHead(fbGender);
        if (!StringUtils.isEmpty(context.getHead()) && context.getHead().startsWith("https:")) {
            head = context.getHead();
        }

        String birthDay = generateBirthDay(BORTH_AGE);

        String country = StringUtils.isEmpty(context.getIpCountry()) ? DEFAULT_COUNTRY : getLongCountryName(context);
        String email = StringUtils.isEmpty(context.getRealEmail()) ? StringUtils.isEmpty(context.getEmail()) ?
                "" : context.getEmail() : context.getRealEmail();
        registerData.setUid(context.getThirdUid());
        registerData.setLogin_type(context.getType());
        registerData.setName(context.getRandomName());
        registerData.setHead(head);
        registerData.setAccept_talk(TALK_VSTATUS_ACCEPT);
        registerData.setVchat_status(TALK_VCHAT_FREE);
        registerData.setBeans(0);
        registerData.setGold(0);
        registerData.setBirthday(birthDay);
        registerData.setAge(BORTH_AGE);
        registerData.setFb_gender(fbGender);
        registerData.setGender(GENDER_MALE);
        registerData.setOs(context.getOs() + "");
        registerData.setIp(context.getIp());
        registerData.setIpCodeCountry(getLongCountryName(context));
        registerData.setAndroid_id(context.getAndroidid());
        registerData.setIos_key(context.getP3());
        registerData.setLang(context.getLang());
        registerData.setArea(context.getArea());
        registerData.setRoom("");
        registerData.setVideo_option("0");
        registerData.setImprove_data(improveData);
        registerData.setGeneral_conf(generalConf);
        registerData.setCountry(country);

        FBInstallReferrerData installReferrerData = InstallReferrerUtils.parseFBInstallReferrer
                (context.getInstallReferrer(), appPkgName, ServerConfig.isProduct());
        registerData.setPromotion_id(installReferrerData == null ? "" : installReferrerData.getGroupName());
        registerData.setChannel(context.getChannel());
        registerData.setApp_package_name(appPkgName);
        registerData.setVersion_code(context.getVersioncode());
        registerData.setIdfa(context.getIdfa());
        registerData.setDistinct_id(context.getDistinct_id());
        registerData.setAccount_status(0);
        registerData.setRobot(0);
        registerData.setEmail(email);
        if (!StringUtils.isEmpty(context.getTnId())) {
            registerData.setTn_id(context.getTnId());
            registerData.setTn_risk(context.getTnRisk());
            if (context.getNowDeviceAccountNum() == 1) {
                if (null == tnDeviceAccountDao.findRookieSignOne(context.getTnId(), false)) {
                    registerData.setFirstTnId(context.getTnId());
                    context.setIsNewUser(1);
                    logger.info("new device:{} new uid:{} add success", context.getTnId(), context.getUid());
                }
            }
        }
        int isNew = 1;
        if (context.getVersioncode() == 381 && PKGConstant.ANDROID_YOUSTAR_LITE.equals(appPkgName)) {
            isNew = 0;
        }

        context.setRegisterData(registerData);

        context.setFbGender(fbGender);
        context.setAcceptTalk(TALK_VSTATUS_ACCEPT);
        context.setIsNew(isNew);
        context.setToClientHead(head);
        context.setToClientName(context.getRandomName());
        context.setBirthday(birthDay);
        context.setAnchor(GENDER_MALE);
        context.setToClientRoomId("");
        context.setToClientNlang(0);
        context.setuLvl(0);
        context.setVipLevel(0);
        context.setToClientCountry(country);
        context.setToClientPkgName(appPkgName);
        context.setFbInstallReferrerData(installReferrerData);
        registerSyn(context);
        if (!insertMysqlFinderDb(context)) {
            logger.error("login fail, save actor to finder db fail context={}", context);
            loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_OTHER_ERROR);
            monitorSender.info(LoginConstant.USTAR_JAVA_EXCEPTION_WARN_NAME,
                    "insert finder db t_user_money fail",
                    "context=" + context);
            throw new CommonException(LoginHttpCode.LOGIN_FAIL);
        }
        // 处理广告奖励发放
        handleCampaignReward(context);
    }


    protected String generateBirthDay(int age) {
        LocalDate nowDate = DateSupport.ARABIAN.getToday();
        LocalDate oldDate = nowDate.minusYears(age);
        // 获取字符格式 yyyy-MM-dd
        return DateSupport.format(oldDate);
    }

    public void registerSyn(RegisterOrLoginContext context) {
        DistributeLock lock = new DistributeLock(getGenRidLockKey());
        try {
            boolean ret = lock.tryLock(TIME_OUT_FIVE, TimeUnit.SECONDS);
            if (!ret) {
                logger.error("genRid Distributed Lock Timeout context={}", context);
                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_GEN_RID_ERROR);
                monitorSender.info(LoginConstant.USTAR_JAVA_EXCEPTION_WARN_NAME,
                        "distributed lock timeout lock key:" + getGenRidLockKey(),
                        "context=" + context);
                throw new CommonException(LoginHttpCode.LOGIN_FAIL);
            }
            int rid = genRid(context);
            MongoLoginActorData registerData = context.getRegisterData();
            registerData.setRid(rid);
            MongoLoginActorData toMongoLoginActorData = loginActorDao.save(registerData);
            if (null == toMongoLoginActorData) {
                fixUnVisibleName(context);
                toMongoLoginActorData = loginActorDao.save(registerData);
                if (null == toMongoLoginActorData) {
                    logger.error("login fail, save actor fail context={}", context);
                    loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_GEN_RID_ERROR);
                    monitorSender.info(LoginConstant.USTAR_JAVA_EXCEPTION_WARN_NAME, "insert mongo db actor fail",
                            "context=" + context);
                    throw new CommonException(LoginHttpCode.LOGIN_FAIL);
                }
            }
            context.setUid(toMongoLoginActorData.get_id().toString());
            context.setRid(rid);
            context.setRidData(toMongoLoginActorData.getRidData());
            if (!insertMysqlDb(context)) {
                logger.error("save actor to cloud db fail context={}", context);
                monitorSender.info(LoginConstant.USTAR_JAVA_EXCEPTION_WARN_NAME, "insert cloud db t_user_actor fail",
                        "context=" + context);

               // loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_GEN_RID_ERROR);
               // throw new CommonException(LoginHttpCode.LOGIN_FAIL);
            }

        } catch (InterruptedException e) {
            logger.error("InterruptedException context={}", context, e);
            loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_GEN_RID_ERROR);
            monitorSender.info(LoginConstant.USTAR_JAVA_EXCEPTION_WARN_NAME,
                    "registerSyn find InterruptedException msg:" + e.getMessage(),
                    "context=" + context);
            throw new CommonException(LoginHttpCode.LOGIN_FAIL);
        } finally {
            lock.unlock();
        }

    }


    private int genRid(RegisterOrLoginContext context) {
        int rid = genRidRedis.incrementRid();
        if (rid < minGenRid) {
            rid = getGenRidByMysql();
            if (rid < minGenRid) {
                logger.error("getGenRidByMysql fail<{} rid={} context={}", minGenRid, rid, context);
                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_GEN_RID_ERROR);
                monitorSender.info(LoginConstant.USTAR_JAVA_EXCEPTION_WARN_NAME,
                        "rid < 6000000 error rid=" + rid,
                        "context=" + context);
                throw new CommonException(LoginHttpCode.LOGIN_FAIL);
            }
            genRidRedis.setRid(rid);
        }
        int count = 0;
        while (BeautifulRidUtils.isBeautifulRid(rid + "") ||
                null != beautifulRidDao.findDataByBeautifulRid(rid + "") ||
                (RID_LIST_ALREADY_USE.contains(rid)) ||
                null != actorDao.getActorByStrRidFromDb(rid + "")) {
            logger.info("step rid={} thirdUid={} login type={}", rid, context.getThirdUid(), context.getType());
            count += 1;
            if (count > MAX_GEN_COUNT) {
                logger.error("genRid count:{} >= {} context={} rid={}", count, MAX_GEN_COUNT, rid, context);
                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_GEN_RID_ERROR);
                monitorSender.info(LoginConstant.USTAR_JAVA_EXCEPTION_WARN_NAME,
                        "count > 1000000 error count=" + count,
                        "context=" + context);
                throw new CommonException(LoginHttpCode.LOGIN_FAIL);
            }
            rid++;
        }
        genRidRedis.setRid(rid);
        return rid;
    }


    private int getGenRidByMysql() {
        int maxRid = mySQLActorService.getMaxGenRid();
        logger.info("maxRid:{} from cloud mysql", maxRid);
        if (maxRid == 0) {
            return 0;
//            maxRid = userMoneyDao.getMaxGenRid(); // redis有值后这个得去掉
//            logger.info("maxRid:{} from finder mysql", maxRid);
        }
        return maxRid + 1;
    }

    public boolean insertMysqlFinderDb(RegisterOrLoginContext context) {
        UserMoneyData userMoneyData = new UserMoneyData();
        Date now = new Date();
        userMoneyData.setUserid(context.getUid());
        userMoneyData.setStatus((byte) 1);
        userMoneyData.setCtime(now);
        userMoneyData.setMtime(now);
        userMoneyData.setMtotal(0);
        userMoneyData.setMleft(0);
        userMoneyData.setDetailTable("t_money_detail");
        return userMoneyDao.insertMoney(userMoneyData);
    }

    private boolean insertMysqlDb(RegisterOrLoginContext context) {
        int nowTime = DateHelper.getNowSeconds();
        MySQLActorData mySQLActorData = new MySQLActorData();
        mySQLActorData.setActorUid(context.getUid());
        mySQLActorData.setRid(context.getRid());
        mySQLActorData.setGenId(context.getRid());
        mySQLActorData.setName(context.getToClientName());
        mySQLActorData.setHead(context.getToClientHead());
        mySQLActorData.setFbGender(context.getFbGender());
        mySQLActorData.setCountry(context.getToClientCountry());
        mySQLActorData.setValid(1);
        mySQLActorData.setOs(context.getOs());
        mySQLActorData.setBeans(0);
        mySQLActorData.setLoginType(context.getType());
        mySQLActorData.setUid(context.getThirdUid());
        mySQLActorData.setLang(context.getLang());
        mySQLActorData.setVersionCode(context.getVersioncode());
        mySQLActorData.setRobot(context.getRegisterData().getRobot());
        mySQLActorData.setIsDelete(0);
        mySQLActorData.setAccountStatus(0);
        mySQLActorData.setTnId(context.getTnId());
        mySQLActorData.setChannel(context.getChannel());
        mySQLActorData.setAppPackageName(context.getToClientPkgName());
        mySQLActorData.setIsFace(0);
        mySQLActorData.setIp(context.getIp());
        mySQLActorData.setLastLoginTime(nowTime);
        mySQLActorData.setMtime(nowTime);
        mySQLActorData.setCtime(nowTime);
        return mySQLActorService.insertActor(mySQLActorData);
    }

    private void login(RegisterOrLoginContext context) {
        MongoLoginActorData oldData = context.getMongoLoginActorData();
        String head = oldData.getHead();
        String birthday = oldData.getBirthday();
        String uid = oldData.get_id().toString();
        String name = oldData.getName();
        int age = oldData.getAge();

        String appPkgName = context.getApp_package_name() == null ? "" : context.getApp_package_name().trim();
        if (context.getOs() == ClientOS.IOS && StringUtils.isEmpty(appPkgName)) {
            appPkgName = PKGConstant.IOS_MAIN;
        }
        String email = StringUtils.isEmpty(context.getRealEmail()) ? StringUtils.isEmpty(context.getEmail()) ?
                "" : context.getEmail() : context.getRealEmail();

        MongoLoginActorData updateData = new MongoLoginActorData();
        updateData.setActor_uid(uid);
        updateData.setArea(context.getArea());
        updateData.setOs(context.getOs() + "");

        if (StringUtils.isEmpty(name)) {
            name = context.getRandomName();
            updateData.setName(name);
        }
        if (StringUtils.isEmpty(birthday) || age == 0) {
            updateData.setAge(BORTH_AGE);
            birthday = generateBirthDay(BORTH_AGE);
            updateData.setBirthday(birthday);
        }

        if (StringUtils.isEmpty(head)) {
            head = baseInitData.generateRandomHead(oldData.getFb_gender());
            updateData.setHead(head);
            updateData.setBanner(Arrays.asList(head));
        }
        if (!StringUtils.isEmpty(context.getIp())) {
            updateData.setIp(context.getIp());
        }
        if (!StringUtils.isEmpty(context.getP3())) {
            updateData.setIos_key(context.getP3());
        }
        if (!StringUtils.isEmpty(context.getAndroidid())) {
            updateData.setAndroid_id(context.getAndroidid());
        }
        if (!StringUtils.isEmpty(context.getChannel())) {
            updateData.setChannel(context.getChannel());
        }
        if (!StringUtils.isEmpty(context.getIdfa())) {
            updateData.setIdfa(context.getIdfa());
        }
        if (!StringUtils.isEmpty(appPkgName)) {
            updateData.setApp_package_name(appPkgName);
        }

        if (null != context.getTnRespondsData()) {
            updateData.setTn_id(context.getTnId());
            updateData.setTn_risk(context.getTnRisk());
        }
        if (context.getVersioncode() != 0) {
            updateData.setVersion_code(context.getVersioncode());
        }
        if (oldData.getIs_delete() == 1) {
            updateData.setIs_delete(0);
        }
        if (oldData.getAccount_status() == 1) {
            updateData.setAccount_status(0);
        }
        if (!StringUtils.isEmpty(email)) {
            updateData.setEmail(email);
        }

        if (!StringUtils.isEmpty(context.getInstallReferrer())) {
            FBInstallReferrerData installReferrerData = InstallReferrerUtils.parseFBInstallReferrer
                    (context.getInstallReferrer(), appPkgName, ServerConfig.isProduct());
            String promotionId = installReferrerData == null ? "" : installReferrerData.getGroupName();
            if (!StringUtils.isEmpty(promotionId)) {
                updateData.setPromotion_id(promotionId);
            }
            context.setFbInstallReferrerData(installReferrerData);
        }

        loginActorDao.updateLoginData(updateData);

        int isNew = oldData.getImprove_data() == 1 ? 0 : 1;
        String roomId = RoomUtils.formatRoomId(uid);
        context.setMongoLoginUpdateData(updateData);
        context.setFbGender(oldData.getFb_gender());
        context.setAcceptTalk(oldData.getAccept_talk());
        context.setIsNew(isNew);
        context.setToClientHead(head);
        context.setToClientName(name);
        context.setBirthday(birthday);
        context.setAnchor(oldData.getGender());
        context.setToClientRoomId(mongoRoomDao.getDataFromCache(roomId) != null ? roomId : "");
//        context.setRid(oldData.getRid());
        context.setToClientNlang(oldData.getNlang());
        context.setuLvl(userLevelDao.getUserLevel(uid));
        context.setVipLevel(vipInfoDao.getIntVipLevel(uid));
        context.setToClientCountry(oldData.getCountry());
        context.setToClientPkgName(appPkgName);
        context.setIsNewUser(ActorUtils.isNewDeviceAccount(uid, oldData.getFirstTnId()) ? 1 : 0);
    }

    private void saveToken(RegisterOrLoginContext context) {
        String toClientToken = UUID.randomUUID().toString().replace("-", "");
        basePlayerRedis.addToken(context.getUid(), toClientToken);
        context.setToClientToken(toClientToken);
    }

    public String getReqId(RegisterOrLoginContext context) {
        String reqId = "";
        if (!StringUtils.isEmpty(context.getRequestId())) {
            reqId = context.getRequestId().replace("null-", "");
        }
        return reqId;
    }

    private ShowIdData getShowBean(RegisterOrLoginContext context) {
        ShowIdData showIdData = new ShowIdData();
        MongoLoginActorData fromActorData = context.getMongoLoginActorData();
        if (null != fromActorData) {
            showIdData.setFromRid(fromActorData.getRid());
            showIdData.setFromUid(fromActorData.get_id().toString());
        }
        String reqId = getReqId(context);
        if (context.isRegister()) {
            MongoLoginActorData deviceActorData = commonDao.findOne
                    (new Query(Criteria.where("tn_id").is(context.getTnId())), MongoLoginActorData.class);
            if (deviceActorData == null) {
                logger.info("not find show device bean tnId={}", context.getTnId());
                showIdData.setShowId(reqId);
                showIdData.setRid(0);
                showIdData.setUid("");
            } else {
                showIdData.setShowId(reqId);
                showIdData.setRid(deviceActorData.getRid());
                showIdData.setUid(deviceActorData.get_id().toString());
            }
        } else {
            if (null != fromActorData) {
                showIdData.setShowId(fromActorData.getRid() + "-" + reqId);
                showIdData.setRid(fromActorData.getRid());
                showIdData.setUid(fromActorData.get_id().toString());
            } else {
                logger.info("getShowBean login but from bean is null context={}", context);
                showIdData.setShowId(reqId);
                showIdData.setRid(0);
                showIdData.setUid("");
            }
        }
        context.setShowIdData(showIdData);
        return showIdData;
    }


    private int getErrorType(int type) {
        int errorType;
        switch (type) {
            case LoginConstant.GOOGLE_TYPE: {
                errorType = LoginConstant.LOGIN_STATE_THIRD_LOGIN_FAIL_G;
                break;
            }
            case LoginConstant.FACE_BOOK_TYPE: {
                errorType = LoginConstant.LOGIN_STATE_THIRD_LOGIN_FAIL_FB;
                break;
            }
            case LoginConstant.FIRE_BASE_MAIL_TYPE: {
                errorType = LoginConstant.LOGIN_STATE_THIRD_LOGIN_FAIL_MAIL;
                break;
            }
            case LoginConstant.FIRE_BASE_PHONE_TYPE: {
                errorType = LoginConstant.LOGIN_STATE_THIRD_LOGIN_FAIL_PHONE;
                break;
            }
            case LoginConstant.APPLE_TYPE: {
                errorType = LoginConstant.LOGIN_STATE_THIRD_LOGIN_FAIL_APPLE;
                break;
            }
            case LoginConstant.HUAWEI_TYPE: {
                errorType = LoginConstant.LOGIN_STATE_THIRD_LOGIN_FAIL_HUAWEI;
                break;
            }
            case LoginConstant.SPECIAL_GUST_TYPE: {
                errorType = LoginConstant.LOGIN_STATE_THIRD_LOGIN_FAIL_VIP;
                break;
            }
            default:
                errorType = LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR;
        }
        return errorType;
    }

    protected void initData(RegisterOrLoginContext context) {
        if (!StringUtils.isEmpty(context.getEmail())) {
            String email = handleArbNum(context.getEmail());
            context.setEmail(email);
        }
    }

    private String fixUnVisibleName(RegisterOrLoginContext context) {
//        String name = context.getRandomName();
//        try {
//            name = name.replaceAll("[\\p{C}]", "");
//        } catch (Exception e) {
//            logger.error("fixUnVisibleName error name={}", name, e);
//            name = "YouStar" + ThreadLocalRandom.current().nextInt(100000, 999999);
//        }
        String name = "YouStar" + ThreadLocalRandom.current().nextInt(100000, 999999);
        logger.info("not support name change name third_uid={} old_name={} new_name={}", context.getThirdUid(), context.getRandomName(), name);
        context.getRegisterData().setName(name);
        context.setRandomName(name);
        context.setToClientName(name);
        return name;
    }

    public String getTnTagMsg(List<Integer> tnList) {
        if (CollectionUtils.isEmpty(tnList)) {
            return "";
        } else {
            StringBuilder bd = new StringBuilder();
            for (Integer item : tnList) {
                bd.append(TnSignEnum.valueOf(item));
                bd.append(" ");
            }
            return bd.toString();
        }
    }

    private List<String> genShuMeiRisk(SMDeviceRespondsData shuMeiData) {
        List<String> shuMeiRisk = new ArrayList<>();

        SMDeviceRespondsData.FakeDevice fakeDevice = shuMeiData.getDeviceLabels().getFake_device();
        SMDeviceRespondsData.DeviceSuspiciousLabels deviceSuspiciousLabels = shuMeiData.getDeviceLabels().getDevice_suspicious_labels();
        SMDeviceRespondsData.MonkeyDevice monkeyDeviceLabels = shuMeiData.getDeviceLabels().getMonkey_device();

        if (fakeDevice != null) {
            if (fakeDevice.getB_altered() == 1) {
                shuMeiRisk.add("b_altered");
            }
            if (fakeDevice.getB_cloud_device() == 1) {
                shuMeiRisk.add("b_cloud_device");
            }
            if (fakeDevice.getB_faker() == 1) {
                shuMeiRisk.add("b_faker");
            }
            if (fakeDevice.getB_farmer() == 1) {
                shuMeiRisk.add("b_farmer");
            }
            if (fakeDevice.getB_multi_boxing() == 1) {
                shuMeiRisk.add("b_multi_boxing");
            }
            if (fakeDevice.getB_multi_boxing_by_app() == 1) {
                shuMeiRisk.add("b_multi_boxing_by_app");
            }
            if (fakeDevice.getB_multi_boxing_by_os() == 1) {
                shuMeiRisk.add("b_multi_boxing_by_os");
            }
            if (fakeDevice.getB_offerwall() == 1) {
                shuMeiRisk.add("b_offerwall");
            }
            if (fakeDevice.getB_pc_emulator() == 1) {
                shuMeiRisk.add("b_pc_emulator");
            }
            if (fakeDevice.getB_phone_emulator() == 1) {
                shuMeiRisk.add("b_phone_emulator");
            }
            if (fakeDevice.getB_altered_route() == 1) {
                shuMeiRisk.add("b_alter_route");
            }
        }

        if (deviceSuspiciousLabels != null) {
            if (deviceSuspiciousLabels.getB_acc() == 1) {
                shuMeiRisk.add("b_acc");
            }
            if (deviceSuspiciousLabels.getB_adb_enable() == 1) {
                shuMeiRisk.add("b_adb_enable");
            }
            if (deviceSuspiciousLabels.getB_alter_apps() == 1) {
                shuMeiRisk.add("b_alter_apps");
            }
            if (deviceSuspiciousLabels.getB_alter_loc() == 1) {
                shuMeiRisk.add("b_alter_loc");
            }
            if (deviceSuspiciousLabels.getB_console() == 1) {
                shuMeiRisk.add("b_console");
            }
            if (deviceSuspiciousLabels.getB_debuggable() == 1) {
                shuMeiRisk.add("b_debuggable");
            }
            if (deviceSuspiciousLabels.getB_device_proxy() == 1) {
                shuMeiRisk.add("b_device_proxy");
            }
            if (deviceSuspiciousLabels.getB_hook() == 1) {
                shuMeiRisk.add("b_hook");
            }
            if (deviceSuspiciousLabels.getB_manufacture() == 1) {
                shuMeiRisk.add("b_manufacture");
            }
            if (deviceSuspiciousLabels.getB_monkey_apps() == 1) {
                shuMeiRisk.add("b_monkey_apps");
            }
            if (deviceSuspiciousLabels.getB_multi_boxing_apps() == 1) {
                shuMeiRisk.add("b_multi_boxing_apps");
            }
            if (deviceSuspiciousLabels.getB_remote_control_apps() == 1) {
                shuMeiRisk.add("b_remote_control_apps");
            }
            if (deviceSuspiciousLabels.getB_repackage() == 1) {
                shuMeiRisk.add("b_repackage");
            }
            if (deviceSuspiciousLabels.getB_reset() == 1) {
                shuMeiRisk.add("b_reset");
            }
            if (deviceSuspiciousLabels.getB_root() == 1) {
                shuMeiRisk.add("b_root");
            }
            if (deviceSuspiciousLabels.getB_sim() == 1) {
                shuMeiRisk.add("b_sim");
            }
            if (deviceSuspiciousLabels.getB_vpn() == 1) {
                shuMeiRisk.add("b_vpn");
            }
            if (deviceSuspiciousLabels.getB_vpn_apps() == 1) {
                shuMeiRisk.add("b_vpn_apps");
            }
            if (deviceSuspiciousLabels.getB_game_cheat_apps() == 1) {
                shuMeiRisk.add("b_game_cheat_apps");
            }
            if (deviceSuspiciousLabels.getB_icloud() == 1) {
                shuMeiRisk.add("b_icloud");
            }
            if (deviceSuspiciousLabels.getB_old_model() == 1) {
                shuMeiRisk.add("b_old_model");
            }
            if (deviceSuspiciousLabels.getB_non_appstore() == 1) {
                shuMeiRisk.add("b_non_appstore");
            }
            if (deviceSuspiciousLabels.getB_wangzhuan_active() == 1) {
                shuMeiRisk.add("b_wangzhuan_active");
            }
        }

        if (monkeyDeviceLabels != null) {
            if (monkeyDeviceLabels.getCommon() != null &&
                    monkeyDeviceLabels.getCommon().getB_monkey_apps() == 1) {
                shuMeiRisk.add("monkey_device.common.b_monkey_apps");
            }
            if (monkeyDeviceLabels.getMonkey_game() != null &&
                    monkeyDeviceLabels.getMonkey_game().getB_monkey_game_apps() == 1) {
                shuMeiRisk.add("b_monkey_game_apps");
            }
            if (monkeyDeviceLabels.getMonkey_read() != null &&
                    monkeyDeviceLabels.getMonkey_read().getB_monkey_read_apps() == 1) {
                shuMeiRisk.add("b_monkey_read_apps");
            }
        }
        return shuMeiRisk;
    }

    private void doReportTnRiskRecordEvent(String uid, String tnId, List<Integer> tnRisk,
                                           boolean isCache) {
        TnRiskRecordEvent event = new TnRiskRecordEvent();
        event.setUid(uid);
        event.setTn_id(tnId);
        String tnRiskStr = CollectionUtils.isEmpty(tnRisk) ? "" : tnRisk.toString();
        event.setTn_risk(tnRiskStr);
        event.setIs_cache(isCache ? 1 : 0);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    /**
     * 处理广告奖励发放
     * 检查用户注册时的广告来源信息，如果匹配则发放1000个金币奖励
     *
     * @param context 注册上下文
     */
    private void handleCampaignReward(RegisterOrLoginContext context) {
        try {
            String promotion_id = context.getRegisterData().getPromotion_id();
            if (ObjectUtils.isEmpty(promotion_id)) {
                return;
            }

            String[] promotionPartArray = promotion_id.split("-");
            if (promotionPartArray.length == 0){
                return;
            }
            String campaign = promotionPartArray[promotionPartArray.length - 1];
            // 查询广告系列映射表
            AdCampaignGameData adCampaignGame = adCampaignGameDao.findByCampaign(campaign);
            if (adCampaignGame == null) {
                logger.info("handleCampaignReward: no mapping found for campaign={}, uid={}", campaign, context.getUid());
                return;
            }
            logger.info("handleCampaignReward: found mapping for campaign={}, uid={}", campaign, context.getUid());

            // 发放1000个金币奖励
            String uid = context.getUid();
            String titleEn = "Advertisement Campaign Reward";
            String desc = "Welcome bonus for advertisement users";
            resourceKeyHandlerService.sendResourceData(uid, "adjustCampaignReward", 73, titleEn, titleEn, desc, "", "", 6);
            if (adCampaignGame.getRoomType() == 5) {
                gameRoomRedis.setGameRoomAdjustCampaign(uid, adCampaignGame.getGameType());
            }
        } catch (Exception e) {
            logger.error("handleCampaignReward: error processing campaign reward for uid={}, error={}", context.getUid(), e.getMessage(), e);
        }
    }


}
