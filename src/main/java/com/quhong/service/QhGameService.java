package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.GameRecordLogEvent;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.ApiResult;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.feign.DataCenterService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.dao.ActorPayExternalDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.qh.*;
import com.quhong.redis.DataRedisBean;
import com.quhong.redis.GameKingRedis;
import com.quhong.redis.GameRoomRedis;
import com.quhong.redis.RobotRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.CacheUtils;
import com.quhong.utils.PageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class QhGameService {

    private static final Logger logger = LoggerFactory.getLogger(QhGameService.class);
    private static final Map<String, QuHongGameData> GAME_INFO_MAP = new HashMap<>();
    private static final int BIG_DIAMONDS = ServerConfig.isProduct() ? 5000 : 100;

    private String appKey;

    @Resource
    private BasePlayerRedis basePlayerRedis;
    @Resource
    private ActorDao actorDao;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private GameKingRedis gameKingRedis;
    @Resource
    private EventReport eventReport;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private RobotRedis robotRedis;
    @Resource
    private ActorPayExternalDao actorPayExternalDao;
    @Autowired
    private GameMsgService gameMsgService;
    @Resource
    private GameRoomRedis gameRoomRedis;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    @PostConstruct
    public void init() {
        appKey = "018f4d17-9208-7393-ab95-36eb264dd43f";
        GAME_INFO_MAP.put("10001", new QuHongGameData("10001", "Greedy", 13, 966, "Greedy game fee", 967, "Greedy game rewards"));
        GAME_INFO_MAP.put("10002", new QuHongGameData("10002", "Animal Party", 14, 972, "Animal Party game fee", 973, "Animal Party game rewards"));
        GAME_INFO_MAP.put("10003", new QuHongGameData("10003", "Big Battle", 15, 974, "Big Battle game fee", 975, "Big Battle game rewards"));
        GAME_INFO_MAP.put("10004", new QuHongGameData("10004", "Slot", 16, 976, "Yummy Slot game fee", 977, "Yummy Slot game rewards"));
        GAME_INFO_MAP.put("10005", new QuHongGameData("10005", "Fruit Party", 17, 920, "Fruit Party game fee", 921, "Fruit Party game rewards"));
        GAME_INFO_MAP.put("10006", new QuHongGameData("10006", "Crash", 18, 2060, "Crash game fee", 2061, "Crash game rewards"));
        GAME_INFO_MAP.put("10007", new QuHongGameData("10007", "Aladdin Slot", 19, 2070, "Aladdin Slot game fee", 2071, "Aladdin Slot game rewards"));
        GAME_INFO_MAP.put("10014", new QuHongGameData("10014", "Slots", 26, 2140, "Slots game fee", 2141, "Slots game rewards"));
        GAME_INFO_MAP.put("10015", new QuHongGameData("10015", "Fishing", 27, 2150, "Fishing game fee", 2151, "Fishing game rewards"));
        GAME_INFO_MAP.put("10018", new QuHongGameData("10018", "Lucky Fruit", 30, 2180, "Lucky Fruit game fee", 2181, "Lucky Fruit game rewards"));
    }

    private <T> QhGameVO<T> checkParam(BaseDTO dto) {
        ActorData actorData = actorDao.getActorDataFromCache(dto.getUid());
        if (actorData == null) {
            return QhGameVO.getError(1001, "can not find user data");
        }
        if (invalidSignature(dto.getSignature(), dto.getSignatureNonce(), appKey, dto.getTimestamp())) {
            return QhGameVO.getError(1003, "signature error");
        }
        return QhGameVO.getOk();
    }

    private synchronized boolean invalidSignature(String signature, String signatureNonce, String appKey, long timestamp) {
        // 数据无效
//        if (DateHelper.getNowSeconds() - timestamp > 1800) {
//            return true;
//        }
        return !Objects.equals(signature, getSignature(signatureNonce, appKey, timestamp));
    }

    private String getSignature(String signatureNonce, String appKey, long timestamp) {
        return DigestUtils.md5DigestAsHex((signatureNonce + appKey + timestamp).getBytes());
    }

    public QhGameVO<TokenVO> getToken(BaseDTO dto) {
        QhGameVO<TokenVO> result = checkParam(dto);
        if (result.isError()) {
            return result;
        }
        TokenVO tokenVO = new TokenVO();
        tokenVO.setToken(basePlayerRedis.getToken(dto.getUid()));
        if (null != tokenVO.getToken()) {
            tokenVO.setTtl(basePlayerRedis.getTokenExpireTime(dto.getUid()));
        }
        return QhGameVO.getOk(tokenVO);
    }

    public QhGameVO<PlayerInfoVO> getPlayerInfo(BaseDTO dto) {
        QhGameVO<PlayerInfoVO> result = checkParam(dto);
        if (result.isError()) {
            return result;
        }
        MongoActorData actorData = actorDao.findActorDataFromDB(dto.getUid());
        PlayerInfoVO vo = new PlayerInfoVO();
        vo.setUid(actorData.get_id().toString());
        vo.setRid(actorData.getStrRid());
        vo.setName(actorData.getName());
        vo.setCountryCode(ActorUtils.getUpperCaseCountryCode(actorData.getCountry()));
        vo.setRegDay(ActorUtils.getRegDays(dto.getUid()));
        vo.setRechargeAmount(actorPayExternalDao.getUserRechargeMoney(dto.getUid()).intValue());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        vo.setBalance(actorData.getBeans());
        vo.setDeviceId(actorData.getTn_id());
        return QhGameVO.getOk(vo);
    }

    // 幂等校验，signatureNonce全局唯⼀，防重放攻击，可以放本地缓存、redis等
    private boolean isRepeatRequest(String signatureNonce) {
        return Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent("str:qhGame:" + signatureNonce, "1", 2, TimeUnit.HOURS));
    }

    public QhGameVO<UserBalanceVO> changeBalance(QhGameDTO.ChangeBalanceDTO dto) {
        QhGameVO<UserBalanceVO> result = checkParam(dto);
        if (result.isError()) {
            return result;
        }
        if (isRepeatRequest(dto.getSignatureNonce())) {
            logger.info("repeat request. uid={} signatureNonce={}", dto.getUid(), dto.getSignatureNonce());
            return QhGameVO.getOk(new UserBalanceVO(actorDao.findActorDataFromDB(dto.getUid()).getBeans()));
        }
        long balance = 0;
        if (dto.getChange() != 0) {
            MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
            moneyDetailReq.setRandomId();
            moneyDetailReq.setUid(dto.getUid());
            moneyDetailReq.setChanged(dto.getChange());
            moneyDetailReq.setMtime(DateHelper.getNowSeconds());
            QuHongGameData gameInfo = GAME_INFO_MAP.get(dto.getGameId());
            if (null == gameInfo) {
                return QhGameVO.getError(1022, "target game not config.");
            }
            if (dto.getChange() > 0) {
                MongoActorData actorData = actorDao.findActorDataFromDB(dto.getUid());
                balance = actorData.getBeans() + dto.getChange();
                moneyDetailReq.setAtype(gameInfo.getIncBeansActType());
                moneyDetailReq.setTitle(gameInfo.getIncBeansTitle());
                moneyDetailReq.setDesc(ObjectUtils.isEmpty(dto.getRoundId()) ? gameInfo.getIncBeansTitle() : "round: " + dto.getRoundId());
                mqSenderService.asyncChargeDiamonds(moneyDetailReq);
                if (!whiteTestDao.isMemberByType(dto.getUid(), WhiteTestDao.WHITE_TYPE_RID)) {
                    // 游戏王活动
                    gameKingRedis.updateRanking(dto.getUid(), dto.getChange());
                }
                gameRoomRedis.updateLastPlayGameTime(dto.getUid(), String.valueOf(gameInfo.getGameType()));
                if ("10005".equals(dto.getGameId())) {
                    // 每日任务：水果机赢钻石
                    commonTaskService.sendCommonTaskMq(new CommonMqTopicData(dto.getUid(), "", "", "", CommonMqTaskConstant.WIN_FRUIT_GAME, dto.getChange()));
                }
                // if (dto.getChange() >= BIG_DIAMONDS) {
                //     // gameMsgService.pushFruitBigReward(dto.getUid(), dto.getChange());
                //     gameMsgService.pushCommonBigReward(dto.getUid(), dto.getChange(), GameMsgService.GAME_NAME_EN, GameMsgService.GAME_NAME_AR, 1, GameMsgService.URL_FRUIT_PARTY);
                // }

                gameRoomRedis.addBCGameRecord(dto.getUid());
                // commonTaskService.sendCommonTaskMq(new CommonMqTopicData(dto.getUid(), "", "", "", CommonMqTaskConstant.WIN_GREEDY_GAME, dto.getChange()));
            } else {
                moneyDetailReq.setAtype(gameInfo.getReduceBeansActType());
                moneyDetailReq.setTitle(gameInfo.getReduceBeansTitle());
                moneyDetailReq.setDesc(ObjectUtils.isEmpty(dto.getRoundId()) ? gameInfo.getIncBeansTitle() : "round: " + dto.getRoundId());
                ApiResult<String> apiResult = dataCenterService.reduceBeans(moneyDetailReq);
                if (apiResult.isError()) {
                    if (1 == apiResult.getCode().getCode()) {
                        return QhGameVO.getError(1008, "insufficient balance");
                    }
                    logger.error("reduce beans error, msg={}", apiResult.getCode().getMsg());
                    return QhGameVO.getError(1021, "change balance server error.");
                }
                balance = Long.parseLong(apiResult.getData());
                if ("10005".equals(dto.getGameId())) {
                    // 每日任务：玩水果机
                    commonTaskService.sendCommonTaskMq(new CommonMqTopicData(dto.getUid(), "", "", dto.getRoundId(), CommonMqTaskConstant.PLAY_FRUIT_MACHINE, 1));
                } else if ("10001".equals(dto.getGameId())) {
                    // 每日任务：玩自研游戏greedy
                    CommonMqTopicData commonMqTopicData = new CommonMqTopicData(dto.getUid(), "", "", dto.getRoundId(), CommonMqTaskConstant.PLAY_GREEDY_GAME, 1);
                    commonMqTopicData.setJsonData(JSONObject.toJSONString(new CommonMqTopicData.PlayGameInfo(2, Math.abs(dto.getChange()))));
                    commonTaskService.sendCommonTaskMq(commonMqTopicData);
                } else if ("10006".equals(dto.getGameId())) {
                    // 每日任务：玩自研游戏crash
                    commonTaskService.sendCommonTaskMq(new CommonMqTopicData(dto.getUid(), "", "", dto.getRoundId(), CommonMqTaskConstant.PLAY_CRASH_GAME, dto.getChange()));
                }
            }
        }
        // 游戏埋点
        doEventReport(dto);
        return QhGameVO.getOk(new UserBalanceVO(balance));
    }

    private void doEventReport(QhGameDTO.ChangeBalanceDTO dto) {
        GameRecordLogEvent event = new GameRecordLogEvent();
        event.setUid(dto.getUid());
        event.setGame_id(dto.getRoundId());
        event.setRoom_id(null);
        event.setIs_creator(1);
        event.setGame_type(GAME_INFO_MAP.getOrDefault(dto.getGameId(), new QuHongGameData()).getGameType());
        event.setGet_diamonds(Math.max(dto.getChange(), 0));
        event.setCost_diamonds(-Math.min(dto.getChange(), 0));
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    public QhGameVO<RobotVO> getRobot(QhGameDTO.RobotDTO dto) {
        List<String> bizRobotList = robotRedis.getGameRobotList();
        PageUtils.PageData<String> pageData = PageUtils.getPageData(bizRobotList, dto.getPage(), dto.getPageSize());
        Set<String> result = new HashSet<>(pageData.list);
        return QhGameVO.getOk(new RobotVO(result, pageData.nextPage));
    }
}
