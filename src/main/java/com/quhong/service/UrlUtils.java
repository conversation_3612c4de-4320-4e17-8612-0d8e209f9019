package com.quhong.service;

import java.util.Optional;

public class UrlUtils {
    
    /**
     * URL路径信息封装类
     */
    public static class UrlPathInfo {
        private final String fileName;
        private final String pathSegment;

        public UrlPathInfo(String fileName, String pathSegment) {
            this.fileName = fileName;
            this.pathSegment = pathSegment;
        }


        public String getFileName() {
            return fileName;
        }
        
        public String getPathSegment() {
            return pathSegment;
        }
        
        @Override
        public String toString() {
            return "UrlPathInfo{fileName='" + fileName + "', pathSegment='" + pathSegment + "'}";
        }
    }
    
    /**
     * 从给定的URL字符串中提取文件名和路径段
     *
     * @param url 完整的URL地址
     * @return 包含文件名和路径段的UrlPathInfo对象，如果无法提取则返回empty Optional
     */
    public static Optional<UrlPathInfo> getUrlPathInfo(String url) {
        return Optional.ofNullable(url)
                .filter(u -> !u.isEmpty())
                .filter(u -> u.contains("/") && u.lastIndexOf('/') < u.length() - 1)
                .map(u -> {
                    String fileName = u.substring(u.lastIndexOf('/') + 1);
                    String withoutFileName = u.substring(0, u.lastIndexOf('/'));
                    String pathSegment = withoutFileName.substring(withoutFileName.lastIndexOf('/') + 1);
                    return new UrlPathInfo(fileName, pathSegment);
                });
    }
    
    // 示例用法
    // public static void main(String[] args) {
    //     String url = "https://cloudcdn.qmovies.tv/room_message/C626AD173A7DB5B649EB47885786AA17.jpg";
    //
    //     getUrlPathInfo(url).ifPresent(info -> {
    //         System.out.println("文件名: " + info.getFileName());      // 输出: C626AD173A7DB5B649EB47885786AA17.jpg
    //         System.out.println("路径段: " + info.getPathSegment());   // 输出: room_message
    //         System.out.println(info);  // 输出完整对象信息
    //     });
    // }
}
