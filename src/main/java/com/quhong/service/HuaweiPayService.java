package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.cache.CacheMap;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.web.HttpResponseData;
import com.quhong.data.HuaweiIAPData;
import com.quhong.data.HuaweiInAppPurchaseData;
import com.quhong.data.dto.HuaweiPayDTO;
import com.quhong.data.vo.HuaweiPayVO;
import com.quhong.exception.CommonException;
import com.quhong.httpResult.PayHttpCode;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.mysql.dao.HuaweiPayDao;
import com.quhong.mysql.data.HuaweiPayData;
import com.quhong.redis.DataRedisBean;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@Service
public class HuaweiPayService extends AbstractPayService {
    private final static Logger logger = LoggerFactory.getLogger(HuaweiPayService.class);
    private static final String HUAWEI_PAY_CHARGE = "huaweiPayCharge";
    private static final String HUAWEI_ACCESS_TOKEN = "https://oauth-login.cloud.huawei.com/oauth2/v2/token";
    private static final String TOKEN_VERIFY_1 = "https://orders-dre.iap.cloud.huawei.eu";
    private static final String TOKEN_VERIFY_2 = "https://orders-at-dre.iap.cloud.huawei.eu";
    private static final Map<String, String> PARAMS = new HashMap<String, String>() {{
        put("client_id", "103185171");
        put("client_secret", "8e6f5617bc57260c6661019de1ae0b738d24f7f9d568122613cf3d7f76126240");
        put("grant_type", "client_credentials");
    }};
    private final CacheMap<String, String> tokenCacheMap;

    public HuaweiPayService() {
        tokenCacheMap = new CacheMap<>(15 * 60 * 1000L);
    }


    @Resource
    private HuaweiPayDao huaweiPayDao;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;


    /**
     * <a href="https://developer.huawei.com/consumer/cn/doc/development/HMS-References/iap-api-order-service-purchase-token-verification-v4">Order服务购买Token校验</a>
     */
    public HuaweiPayVO huaweiPayVerify(HuaweiPayDTO dto) {
        try (DistributeLock lock = new DistributeLock(dto.getPurchaseToken(), 20)) {
            lock.lock();
            HuaweiPayData existOrder = huaweiPayDao.lambdaQuery()
                    .eq(HuaweiPayData::getPurchaseToken, dto.getPurchaseToken())
                    .one();
            if (null != existOrder && existOrder.getPurchaseState() == 0) {
                return new HuaweiPayVO(dto.getPurchaseToken(), null);
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("purchaseToken", dto.getPurchaseToken());
            jsonObject.put("productId", dto.getProductId());
            HttpResponseData<String> huaweiResp = webClient.sendRestfulPost(getRootUrl(dto.getAccount_flag()) + "/applications/purchases/tokens/verify",
                    jsonObject.toJSONString(),
                    buildAuthorization(getAccessToken(true)),
                    1);
            if (null == huaweiResp || StringUtils.isEmpty(huaweiResp.getBody())) {
                logger.error("huawei verify failed. responseData. body is empty, uid={}", dto.getUid());
                sendWarn("华为支付接口异常", JSON.toJSONString(huaweiResp));
                throw new CommonException(PayHttpCode.SERVER_ERROR);
            }
            logger.info("verify huawei product. status={} body={}", huaweiResp.getStatus(), huaweiResp.getBody());
            JSONObject respJSON = JSON.parseObject(huaweiResp.getBody());
            HuaweiIAPData iapData = respJSON.toJavaObject(HuaweiIAPData.class);
            iapData.setPurchaseTokenData(JSON.parseObject(respJSON.get("purchaseTokenData").toString(), HuaweiInAppPurchaseData.class));
            if (!dto.getPurchase_signature().equals(iapData.getDataSignature())) {
                logger.error("verify huawei order signature error. uid={} reqSignature={} huaweiResp={}",
                        dto.getUid(), dto.getPurchase_signature(), iapData.getDataSignature());
                throw new CommonException(PayHttpCode.HUAWEI_SIGNATURE_ERROR);
            }
            HuaweiInAppPurchaseData purchaseData = iapData.getPurchaseTokenData();
            saveOrUpdateOrder(existOrder, purchaseData, dto);
            int balance;
            if (0 == purchaseData.getPurchaseState()) {
                if (null != purchaseData.getPurchaseType() && purchaseData.getPurchaseType() == 0) {
                    sendWarn("华为支付沙盒订单", huaweiResp.getBody());
                }
                String bizId = DigestUtils.md5DigestAsHex((dto.getPurchaseToken()).getBytes());
                if (isPatchOrder(purchaseData.getOrderId())) {
                    logger.info("huawei patch order. orderId={} uid={}", purchaseData.getOrderId(), dto.getUid());
                    balance = actorDao.findActorDataFromDB(dto.getUid()).getBeans();
                } else {
                    balance = chargeBeans(bizId, dto.getProductId(), dto.getUid(), HUAWEI_PAY_CHARGE, "buy " + dto.getProductId(), purchaseData.getOrderId(), dto.getCouponId());
                }
                // 首充检查
                firstRechargeService.checkFirstRecharge(dto.getUid(), 3, dto.getProductId());
            } else {
                MongoActorData actorDataFromDB = actorDao.findActorDataFromDB(dto.getUid());
                balance = actorDataFromDB.getBeans();
            }
            return new HuaweiPayVO(dto.getPurchaseToken(), balance);
        }
    }

    private void saveOrUpdateOrder(HuaweiPayData existOrder, HuaweiInAppPurchaseData purchaseData, HuaweiPayDTO dto) {
        HuaweiPayData data = new HuaweiPayData();
        int nowSeconds = DateHelper.getNowSeconds();
        if (null == existOrder) {
            BeanUtils.copyProperties(purchaseData, data);
            data.setUid(dto.getUid());
            data.setAccountFlag(dto.getAccount_flag());
            data.setConfirmed(0);
            data.setCrtAt(nowSeconds);
            data.setUpdAt(nowSeconds);
            data.setPurchaseType(purchaseData.getPurchaseType() == null ? 10 : 0);
            huaweiPayDao.save(data);
        } else {
            data.setPurchaseState(purchaseData.getPurchaseState());
            data.setCurrency(purchaseData.getCurrency());
            data.setPrice(purchaseData.getPrice());
            data.setUpdAt(nowSeconds);
            huaweiPayDao.lambdaUpdate().eq(HuaweiPayData::getPurchaseToken, dto.getPurchaseToken()).update(data);
        }
    }

    public String getAccessToken(boolean cache) {
        if (cache && tokenCacheMap.hasData(HUAWEI_ACCESS_TOKEN)) {
            return tokenCacheMap.getData(HUAWEI_ACCESS_TOKEN);
        }
        HttpResponseData<String> response = webClient.sendPostWithHttpResp(HUAWEI_ACCESS_TOKEN, PARAMS, 1);
        logger.info("get huawei access token. response={}", JSON.toJSONString(response));
        JSONObject jsonObject = JSON.parseObject(response.getBody());
        String accessToken = jsonObject.getString("access_token");
        if (StringUtils.isEmpty(accessToken)) {
            logger.error("request huawei access token error. status={} body={}", response.getStatus(), response.getBody());
            return null;
        }
        tokenCacheMap.cacheData(HUAWEI_ACCESS_TOKEN, accessToken);
        return accessToken;
    }

    private Map<String, String> buildAuthorization(String appAt) {
        String oriString = MessageFormat.format("APPAT:{0}", appAt);
        String authorization = MessageFormat.format("Basic {0}", Base64.encodeBase64String(oriString.getBytes(StandardCharsets.UTF_8)));
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", authorization);
        headers.put("Content-Type", "application/json; charset=UTF-8");
        return headers;
    }

    private String getRootUrl(Integer accountFlag) {
        if (null != accountFlag && accountFlag == 1) {
            return TOKEN_VERIFY_2;
        }
        return TOKEN_VERIFY_1;
    }

    @Override
    void onChargeFailure(String bizId, String productId, String uid) {

    }

    private String getHuaweiPatchKey(String orderId) {
        return "str:huawei:patch:" + orderId;
    }

    public void addHuaweiPatch(String orderId) {
        redisTemplate.opsForValue().set(getHuaweiPatchKey(orderId), orderId, 30, TimeUnit.DAYS);
    }

    private boolean isPatchOrder(String orderId) {
        try {
            return orderId.equals(redisTemplate.opsForValue().get(getHuaweiPatchKey(orderId)));
        } catch (Exception e) {
            logger.error("isPatchOrder error, orderId={} {}", orderId, e.getMessage(), e);
            return false;
        }
    }
}
