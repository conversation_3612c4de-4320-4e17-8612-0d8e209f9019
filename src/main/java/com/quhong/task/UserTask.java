package com.quhong.task;

import com.quhong.analysis.DailyTaskRecordEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.TaskCompleteEvent;
import com.quhong.config.AsyncConfig;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.date.DateSupport;
import com.quhong.core.date.DayTimeSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.TaskInfo;
import com.quhong.mongo.dao.FamilyDevoteDao;
import com.quhong.mysql.dao.FamilyMemberDao;
import com.quhong.mysql.dao.NewcomerTaskDao;
import com.quhong.mysql.dao.UserTaskDao;
import com.quhong.mysql.data.NewcomerTaskData;
import com.quhong.mysql.data.UserTaskData;
import com.quhong.service.DailyTaskCenterService;
import com.quhong.service.FamilyService;
import com.quhong.service.WebTaskCenterService;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.K8sUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/10/23
 */
@Component
public class UserTask {

    private static final Logger logger = LoggerFactory.getLogger(UserTask.class);

    @Resource
    private K8sUtils k8sUtils;
    @Resource
    private UserTaskDao userTaskDao;
    @Resource
    private DailyTaskCenterService dailyTaskCenterService;
    @Resource
    private EventReport eventReport;
    // @Resource
    // private FamilyService familyService;
    // @Resource
    // private FamilyDevoteDao familyDevoteDao;
    // @Resource
    // private FamilyMemberDao familyMemberDao;
    @Resource
    private WebTaskCenterService webTaskCenterService;
    @Resource
    private NewcomerTaskDao newcomerTaskDao;


    /**
     * 每天北京时间：6:00 执行一次, 日常任务埋点
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "45 0 22 * * ?")
    public void userTaskEventReport() {
        if (!k8sUtils.isMasterFromCache()) {
            return;
        }
        String strDate = DateHelper.ARABIAN.getYesterdayStr(new Date());
        int taskDate = Integer.parseInt(strDate.replace("-", ""));
        List<UserTaskData> taskList = userTaskDao.getTaskListByTaskDate(taskDate);
        if (CollectionUtils.isEmpty(taskList)) {
            logger.info("taskList is empty.");
            return;
        }
        Map<String, TaskInfo> taskInfoMap = dailyTaskCenterService.getTaskInfoMap();
        Map<String, TaskInfo> webTaskInfoMap = webTaskCenterService.getTaskInfoMap();
        for (UserTaskData userTaskData : taskList) {
            DailyTaskRecordEvent event = new DailyTaskRecordEvent();
            event.setUid(userTaskData.getUid());
            event.setDate_key(userTaskData.getTaskDate());
            event.setTask_key(userTaskData.getTaskKey());
            TaskInfo taskInfo = taskInfoMap.get(userTaskData.getTaskKey());
            event.setDaily_task_type(taskInfo != null && taskInfo.getType() == 2 ? 2 : 1);
            event.setTask_value(userTaskData.getTaskNum());
            event.setAward_status(userTaskData.getStatus());
            event.setCtime(getCtime(strDate));
            eventReport.track(new EventDTO(event));

            TaskInfo webTaskInfo = webTaskInfoMap.get(userTaskData.getTaskKey());
            if (webTaskInfo != null){
                TaskCompleteEvent taskCompleteEvent = new TaskCompleteEvent();
                taskCompleteEvent.setUid(userTaskData.getUid());
                taskCompleteEvent.setCtime(DateHelper.getNowSeconds());
                taskCompleteEvent.setTask_type(webTaskInfo.getType());
                taskCompleteEvent.setTask_name(webTaskInfo.getName());
                taskCompleteEvent.setTask_progress(userTaskData.getTaskNum());
                taskCompleteEvent.setTask_total_progress(webTaskInfo.getLimit());
                taskCompleteEvent.setTask_status(userTaskData.getStatus());
                eventReport.track(new EventDTO(taskCompleteEvent));
            }
        }
        logger.info("userTaskEventReport success. strDate={}", strDate);

        // 上报7天前新人未完成任务
        userReportNewcomerTask(webTaskInfoMap);
    }

    private int getCtime(String strDate) {
        LocalDate date = DateSupport.parse(strDate);
        LocalDateTime dateTime = date.atTime(12, 0, 0);
        return (int) DayTimeSupport.UTC.getTimeSeconds(dateTime);
    }


    public void userReportNewcomerTask(Map<String, TaskInfo> webTaskInfoMap){
        int todayStartTime = (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000);
        int sevenStartTime = todayStartTime - 7*86400;
        int sevenEndTime = sevenStartTime + 86400;
        String startObj = ActorUtils.getStrObjectIdByTime(sevenStartTime);
        String endObj = ActorUtils.getStrObjectIdByTime(sevenEndTime);
        logger.info("newcomerTaskReport start. startObj={}, endObj={}", startObj, endObj);
        int taskTime = DateHelper.getNowSeconds();
        List<NewcomerTaskData> newcomerTaskList = newcomerTaskDao.selectNewcomerTaskByTime(startObj, endObj);
        for (NewcomerTaskData newcomerTaskData: newcomerTaskList) {
            TaskInfo webTaskInfo = webTaskInfoMap.get(newcomerTaskData.getTaskKey());
            if (webTaskInfo == null){
                continue;
            }

            if (newcomerTaskData.getStatus() == 2){
                continue;
            }
            TaskCompleteEvent taskCompleteEvent = new TaskCompleteEvent();
            taskCompleteEvent.setUid(newcomerTaskData.getUid());
            taskCompleteEvent.setCtime(newcomerTaskData.getCtime());
            taskCompleteEvent.setTask_type(webTaskInfo.getType());
            taskCompleteEvent.setTask_name(webTaskInfo.getName());
            taskCompleteEvent.setTask_progress(newcomerTaskData.getTaskNum());
            taskCompleteEvent.setTask_total_progress(webTaskInfo.getLimit());
            taskCompleteEvent.setTask_status(newcomerTaskData.getStatus());
            eventReport.track(new EventDTO(taskCompleteEvent));
        }
        logger.info("newcomerTaskReport end. time={}", DateHelper.getNowSeconds() - taskTime);
    }

    // //每45分钟执行一次
    // @Async(AsyncConfig.ASYNC_TASK)
    // @Scheduled(cron = "16 0/45 * * * ?")
    // public void checkFamilyData() {
    //     if (k8sUtils.isMasterFromCache()) {
    //         familyService.familyDismiss();
    //         familyService.requestListExpire();
    //     }
    // }

    //每30分钟执行一次
//    @Async(AsyncConfig.ASYNC_TASK)
//    @Scheduled(cron = "20 0/30 * * * ?")
//    public void familyRequestNew() {
//        if (k8sUtils.isMasterFromCache()) {
//            familyService.familyRequestNewNotice();
//        }
//    }


    // @Async(AsyncConfig.ASYNC_TASK)
    // @Scheduled(cron = "20 0/30 * * * ?")
    // public void cleanDevoteDetail() {
    //     long millis = TimeUnit.DAYS.toMillis(100);
    //     long deleteCount = familyDevoteDao.cleanDevote(millis);
    //     logger.info("clean devote detail. deleteCount={}", deleteCount);
    // }

}
