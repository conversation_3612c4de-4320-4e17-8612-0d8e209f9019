package com.quhong.task;

import com.quhong.config.AsyncConfig;
import com.quhong.mq.MqSenderService;
import com.quhong.utils.K8sUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class MqTask {
    private final static Logger logger = LoggerFactory.getLogger(MqTask.class);

    @Resource
    private K8sUtils k8sUtils;
    @Resource
    private MqSenderService mqSenderService;


    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "01 * * * * ?")
    public void mqTaskProcess() {
        if (k8sUtils.isMasterFromCache()) {
            // 重试异常mq消息
            mqSenderService.processRetryMessages();
        }
    }
}
