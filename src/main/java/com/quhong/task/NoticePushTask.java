package com.quhong.task;

import com.quhong.config.AsyncConfig;
import com.quhong.service.*;
import com.quhong.utils.K8sUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/10/15
 */
@Component
public class NoticePushTask {

    public final Logger logger = LoggerFactory.getLogger(getClass());


    @Resource
    private K8sUtils k8sUtils;
//    @Resource
//    private NoticePushService noticePushService;
    @Resource
    private FCMPushService fcmPushService;
    @Resource
    private UserNoActivePushService userNoActivePushService;
    @Resource
    private OperationFcmPushService operationFcmPushService;
    @Resource
    private RegisterUserPushService registerUserPushService;
    @Resource
    private UserRoomPushService userRoomPushService;

    /**
     * 官方消息推送
     * 每2分钟执行一次
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "48 0/2 * * * ?")
    public void noticePushTask() {
        if (k8sUtils.isMasterFromCache()) {
//            noticePushService.officialMsgPush();
            userRoomPushService.pushPopularRoomProcess();
        }
    }

    /**
     * 删除15天前的官方推送，每天北京时间10点执行
     */
//    @Async(AsyncConfig.ASYNC_TASK)
//    @Scheduled(cron = "0 45 2 * * ?")
//    public void deleteOfficialPush() {
//        logger.info("deleteOfficialPush");
//        if (k8sUtils.isMasterFromCache()) {
//            noticePushService.onClearOfficialPushTick2();
//        }
//    }

    /**
     * 运营消息推送
     * 每5分钟执行一次
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "32 0/5 * * * ?")
    public void operationFcmPushTask() {
        if (!k8sUtils.isMasterFromCache()) {
            return;
        }
        operationFcmPushService.operationPushProcess();
        userRoomPushService.pushRoomEventMsgProcess();
    }


    /**
     * 连续三天不活跃的用户推送
     * 每小时执行一次
     * <a href="https://www.tapd.cn/tapd_fe/20792731/story/detail/1120792731001019314">...</a>
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0 32 0/1 * * ?")
    public void userNoActive() {
        if (k8sUtils.isMasterFromCache()) {
            userNoActivePushService.noActivePushProcess();
            registerUserPushService.push0To1HourIncompleteProfileUsers();
        }
    }

    /**
     * FCM每日签到消息通知，UTC+3 20:00
     */
    // @Async(AsyncConfig.ASYNC_TASK)
    // @Scheduled(cron = "0 0 17 * * ?")
    // public void dailySignNotice() {
    //     if (!k8sUtils.isMaster()) {
    //         return;
    //     }
    //     fcmPushService.dailySignFcmPush();
    // }

    /**
     * 注册3天新用户推送
     * UTC+3 21:00
     * <a href="https://www.tapd.cn/tapd_fe/20792731/story/detail/1120792731001019314">...</a>
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0 18 18 * * ?")
    public void register3DayPush() {
        if (!k8sUtils.isMaster()) {
            return;
        }
        registerUserPushService.registerUserPushProcess();
    }

}
