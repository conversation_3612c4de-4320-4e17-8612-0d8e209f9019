package com.quhong.task;

import com.quhong.config.AsyncConfig;
import com.quhong.service.NoticePushService;
import com.quhong.utils.K8sUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/10/15
 */
@Component
public class NoticePushTask {

    public final Logger logger = LoggerFactory.getLogger(getClass());


    @Resource
    private K8sUtils k8sUtils;
    @Resource
    private NoticePushService noticePushService;

    /**
     * 官方消息推送
     * 每2分钟执行一次
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "48 0/2 * * * ?")
    public void noticePushTask() {
        if (k8sUtils.isMasterFromCache()) {
            noticePushService.officialMsgPush();
        }
    }
    /**
     * 删除15天前的官方推送，每天北京时间10点执行
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0 45 2 * * ?")
    public void deleteOfficialPush() {
        logger.info("deleteOfficialPush");
        if (k8sUtils.isMasterFromCache()) {
            noticePushService.onClearOfficialPushTick2();
        }
    }



}
