package com.quhong.constant;

import com.quhong.enums.HttpCode;

public class MomentHttpCode extends HttpCode {

    public static final HttpCode MOMENT_FROZEN = new HttpCode(2001, "moment_frozen");
    public static final HttpCode MOMENT_BLOCK = new HttpCode(20, "moment_block");
    public static final HttpCode LENGTH_NOT_ALLOW = new HttpCode(21, "length_not_allow");
    public static final HttpCode IMG_LIMIT = new HttpCode(21, "img_limit");
    public static final HttpCode AT_LIMIT = new HttpCode(21, "at_limit");
    public static final HttpCode TOPIC_LIMIT = new HttpCode(21, "topic_limit");
    public static final HttpCode DIRTY_WORD = new HttpCode(41, "dirty_word");
    public static final HttpCode POST_SUCCESS = new HttpCode(0, "Moment posted successfully!", "تم نشر اللحظة بنجاح!");
    public static final HttpCode COMMENT_SUCCESS = new HttpCode(0, "Comment done successfully! ", "تم التعليق بنجاح!");
    public static final HttpCode COMMENT_DEL_SUCCESS = new HttpCode(0, "Comment deleted successfully!", "تم حذف اللحظة بنجاح.");
    public static final HttpCode REPORT_SUCCESS = new HttpCode(0, "Report successfully!", "تم الإبلاغ بنجاح!");
    public static final HttpCode BLOCK_SUCCESS = new HttpCode(0, "Block successfully!", "تم الحظر بنجاح!");
    public static final HttpCode LIKE_SUCCESS = new HttpCode(0, "Like done successfully.", "تم الإعجاب بنجاح!");
    public static final HttpCode MOMENT_NOT_EXIST = new HttpCode(204, "moment_not_exist");
    public static final HttpCode COMMENT_NOT_EXIST = new HttpCode(20, "comment_not_exist");
    public static final HttpCode COMMENT_TOO_LENGTH = new HttpCode(21, "comment_too_length");
    public static final HttpCode COMMENT_AUTH_ERROR = new HttpCode(20, "comment_auth_error");
    public static final HttpCode LEVEL_LIMIT = new HttpCode(20, "level_limit");
    public static final HttpCode LINK_VALID = new HttpCode(203, "link_valid");
    public static final HttpCode INSUFFICIENT_DIAMONDS = new HttpCode(50, "");
    public static final HttpCode INSUFFICIENT_GOLDS = new HttpCode(55, "");
    public static final HttpCode SAME_COMMENT = new HttpCode(56, " We would appreciate it if you posted diverse comment", "سنكون ممتنين إذا قمت بنشر تعليق متنوع");

    public static final HttpCode TOPIC_EXIST_ALREADY = new HttpCode(2121, "topic_exist_already");
    public static final HttpCode CODE_NOT_ALLOW = new HttpCode(2120, "There are sensitive words in the current name/announcement, please modify it and submit it.",
            "يحتوي اسم العائلة / الإعلان الحالي على كلمات حساسة، يرجى تعديلها ثم تقديمها.");
    public static final HttpCode TOPIC_MUST_PUBLIC = new HttpCode(2121, "You can only select public when publishing moment with topic.");
    public static final HttpCode TOPIC_MEMBER_BLACK = new HttpCode(2122, "topic_member_black");
    public static final HttpCode TOPIC_ADMIN_MAX = new HttpCode(2122, "topic_admin_max");
    public static final HttpCode TOPIC_LEVEL_LIMIT = new HttpCode(2122, "topic_level_limit");
    public static final HttpCode MOMENT_POST_HOUR_LIMIT = new HttpCode(2123, "moment_post_hour_limit");
    public static final HttpCode MOMENT_POST_MINUTE_LIMIT = new HttpCode(2124, "moment_post_minute_limit");
}
