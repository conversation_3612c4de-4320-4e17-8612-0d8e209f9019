package com.quhong.mongo.data;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 荣誉等级奖励
 */
@Document(collection = "honor_award")
public class HonorAwardData {

    @Id
    private ObjectId _id;
    private String honorLevel;            // 荣誉等级
    private List<Integer> badgeList;      // 对应勋章
    private List<Integer> joinList;       // 对应坐骑

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getHonorLevel() {
        return honorLevel;
    }

    public void setHonorLevel(String honorLevel) {
        this.honorLevel = honorLevel;
    }

    public List<Integer> getBadgeList() {
        return badgeList;
    }

    public void setBadgeList(List<Integer> badgeList) {
        this.badgeList = badgeList;
    }

    public List<Integer> getJoinList() {
        return joinList;
    }

    public void setJoinList(List<Integer> joinList) {
        this.joinList = joinList;
    }
}
