package com.quhong.mongo.data;

import com.quhong.mongo.dao.UserFriendsDao;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Document(collection = UserFriendsDao.TABLE_NAME)
public class UserFriendsData {

    @Field("uid_first")
    private String uidFirst;//第一个用户的uid
    @Field("uid_second")
    private String uidSecond;//第二个用户的uid
    private Integer source;//成为好友的途径
    @Field("friend_index")
    private String friendIndex;//索引由两个uid组合而成,用下划线隔开
    @Field("friend_relation")
    private Integer friendRelation;//# 好友关系 1--已经是好友  2--好友关系解除  # 解除后直接删除 @2019/07
    private Integer ctime;

    public String getUidFirst() {
        return uidFirst;
    }

    public void setUidFirst(String uidFirst) {
        this.uidFirst = uidFirst;
    }

    public String getUidSecond() {
        return uidSecond;
    }

    public void setUidSecond(String uidSecond) {
        this.uidSecond = uidSecond;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public String getFriendIndex() {
        return friendIndex;
    }

    public void setFriendIndex(String friendIndex) {
        this.friendIndex = friendIndex;
    }

    public Integer getFriendRelation() {
        return friendRelation;
    }

    public void setFriendRelation(Integer friendRelation) {
        this.friendRelation = friendRelation;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
