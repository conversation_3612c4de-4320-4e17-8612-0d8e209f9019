package com.quhong.mongo.dao;

import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.RoyaltyUserData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
@Component
public class RoyaltyUserDao {

    private final static Logger logger = LoggerFactory.getLogger(RoyaltyUserDao.class);

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public RoyaltyUserData findDataByRid(int rid) {
        try {
            Query query = new Query(Criteria.where("rid").is(rid));
            return mongoTemplate.findOne(query, RoyaltyUserData.class);
        } catch (Exception e) {
            logger.error("find royalty user data by rid error. rid={} {}", rid, e.getMessage(), e);
            return null;
        }
    }

    public void save(RoyaltyUserData royaltyUserData) {
        try {
            mongoTemplate.save(royaltyUserData);
        } catch (Exception e) {
            logger.error("save royalty user data error. {}", e.getMessage(), e);
        }
    }

    public void remove(RoyaltyUserData royaltyUserData) {
        try {
            mongoTemplate.remove(royaltyUserData);
        } catch (Exception e) {
            logger.error("remove royalty user data error. {}", e.getMessage(), e);
        }
    }
}
