package com.quhong.mongo.dao;

import com.quhong.cache.CacheMap;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.FriendData;
import com.quhong.utils.FriendUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Component
public class FriendDao {
    private static final Logger logger = LoggerFactory.getLogger(FriendDao.class);

    private static final long CACHE_TIME_MILLIS = 60 * 1000L;
    private final CacheMap<String, FriendData> cacheMap;
    public static final String TABLE_NAME = "friends";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public FriendDao() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    public FriendData findDataFromCache(String uid, String aid) {
        String friendIndex = FriendUtils.generateFriendIndex(uid, aid);
        FriendData data = cacheMap.getData(friendIndex, true);
        if (null != data) {
            return data;
        } else {
            return findData(uid, aid);
        }
    }

    public FriendData findData(String uid, String aid) {
        try {
            String friendIndex = FriendUtils.generateFriendIndex(uid, aid);
            Criteria criteria = Criteria.where("friend_index").is(friendIndex);
            FriendData data = mongoTemplate.findOne(new Query(criteria), FriendData.class);
            if (null != data) {
                cacheMap.cacheData(friendIndex, data);
            } else {
                cacheMap.remove(friendIndex);
            }
            return data;
        } catch (Exception e) {
            logger.error("find friend data error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }
}
