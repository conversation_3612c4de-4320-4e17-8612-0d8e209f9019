package com.quhong.mongo.dao;

import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.UserFriendsData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
public class UserFriendsDao {
    private static final Logger logger = LoggerFactory.getLogger(UserFriendsDao.class);
    public static final String TABLE_NAME = "friends";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;
    @Resource
    private UserLevelDao userLevelDao;

    public Integer getFriendsRank(String uid) {
        try {
            Criteria criteria = new Criteria();
            criteria.orOperator(Criteria.where("uid_first").is(uid), Criteria.where("uid_second").is(uid));
            Query query = new Query(criteria);
            List<UserFriendsData> list = mongoTemplate.find(query, UserFriendsData.class);
            Set<Integer> friendsLevelSet = new HashSet<>();
            int myLevel = userLevelDao.getUserLevel(uid);
            for (UserFriendsData data : list) {
                if (!data.getUidFirst().equals(uid)) {
                    friendsLevelSet.add(userLevelDao.getUserLevel(data.getUidFirst()));
                }
                if (!data.getUidSecond().equals(uid)) {
                    friendsLevelSet.add(userLevelDao.getUserLevel(data.getUidSecond()));
                }
            }
            List<Integer> friendsLevelList = new ArrayList<>(friendsLevelSet);
            Collections.sort(friendsLevelList);
            Collections.reverse(friendsLevelList);
            if (friendsLevelList.contains(myLevel)) {
                return friendsLevelList.indexOf(myLevel) + 1;
            }
            friendsLevelList.add(myLevel);
            Collections.sort(friendsLevelList);
            Collections.reverse(friendsLevelList);
            return friendsLevelList.indexOf(myLevel) + 1;
        } catch (Exception e) {
            logger.error("get friends rank error {}", e.getMessage(), e);
            return 0;
        }
    }
}
