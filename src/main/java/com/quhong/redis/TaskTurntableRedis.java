package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class TaskTurntableRedis{
    private static final Logger logger = LoggerFactory.getLogger(TaskTurntableRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 10;
    private static final int MAX_SIZE = 30;
    private static final int MAX_USER_SIZE = 30000;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;


    private String getTodayDrawKey(String uid) {
        String dateStr = DateHelper.ARABIAN.formatDateInDay();
        return String.format("str:taskTurntableTodayDraw:%s:%s", dateStr, uid);
    }

    public int getTodayDrawScore(String uid) {
        try{
            String score = clusterTemplate.opsForValue().get(getTodayDrawKey(uid));
            return score != null ? Integer.parseInt(score) : 0;
        } catch (Exception e) {
            logger.info("getTodayDrawScore error uid={} e={}", uid, e.getMessage(), e);
        }
        return 0;
    }

    public void incrTodayDrawScore(String uid, int score) {
        try {
            String key = getTodayDrawKey(uid);
            clusterTemplate.opsForValue().increment(key, score);
            clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incrTodayDrawScore error uid={}  score={} e={}", uid, score, e.getMessage(), e);
        }
    }


    private String getPoolSizeKey(String turntableType) {
        return "list:pool_size:" + turntableType;
    }

    public int getPoolSize(String turntableType) {
        try {
            Long poolSize = clusterTemplate.opsForList().size(getPoolSizeKey(turntableType));
            return poolSize != null ? poolSize.intValue() : 0;
        } catch (Exception e) {
            logger.info("getPoolSize error={}", e.getMessage(), e);
            return 0;
        }
    }


    public void initPoolSize(List<String> rewardConfigList, String turntableType) {
        try {
            clusterTemplate.opsForList().rightPushAll(getPoolSizeKey(turntableType), rewardConfigList);
            clusterTemplate.expire(getPoolSizeKey(turntableType), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);

        } catch (Exception e) {
            logger.info("initPoolSize  error={}", e.getMessage(), e);
        }
    }


    // 抽奖
    public String drawCardKey(String turntableType) {
        try {
            String cardKey = clusterTemplate.opsForList().leftPop(getPoolSizeKey(turntableType));
            return cardKey != null ? cardKey : "";
        } catch (Exception e) {
            logger.info("drawCardKey error = {}", e.getMessage(), e);
            return "";
        }
    }


    private String getRollRecordKey(String turntableType) {
        return "list:taskTurntable:" + turntableType;
    }

    public void addRollRecord(String turntableType, String uid, String drawType) {

        String drawRollKey = uid + "_" + drawType;
        Long listSize = clusterTemplate.opsForList().leftPush(getRollRecordKey(turntableType), drawRollKey);
        if (null != listSize && listSize > MAX_SIZE) {
            clusterTemplate.opsForList().trim(getRollRecordKey(turntableType), 0, MAX_SIZE);
        }
    }

    @Cacheable(value = "getRollRecordList", cacheManager = CaffeineCacheConfig.EXPIRE_5S_AFTER_WRITE)
    public List<String> getRollRecordList(String turntableType) {
        try {
            List<String> jsonList = clusterTemplate.opsForList().range(getRollRecordKey(turntableType), 0, MAX_SIZE - 1);
            if (CollectionUtils.isEmpty(jsonList)) {
                return Collections.emptyList();
            }
            List<String> resultList = new ArrayList<>(MAX_SIZE);
            resultList.addAll(jsonList);
            return resultList;
        } catch (Exception e) {
            logger.error("getRollRecordList error", e);
            return Collections.emptyList();
        }
    }


    /**
     * Web版本抽奖转盘
     */

    private String getWebPoolSizeKey() {
        return "list:webTurntable:size";
    }

    public int getWebPoolSize() {
        try {
            Long poolSize = clusterTemplate.opsForList().size(getWebPoolSizeKey());
            return poolSize != null ? poolSize.intValue() : 0;
        } catch (Exception e) {
            logger.error("getWebPoolSize error={}", e.getMessage(), e);
            return 0;
        }
    }

    public void initWebPoolSize(List<String> rewardConfigList) {
        try {
            clusterTemplate.opsForList().rightPushAll(getWebPoolSizeKey(), rewardConfigList);
            clusterTemplate.expire(getWebPoolSizeKey(), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("initWebPoolSize  error={}", e.getMessage(), e);
        }
    }

    // web抽奖
    public String drawWebCardKey() {
        try {
            String cardKey = clusterTemplate.opsForList().leftPop(getWebPoolSizeKey());
            return cardKey != null ? cardKey : "";
        } catch (Exception e) {
            logger.info("drawWebCardKey error = {}", e.getMessage(), e);
            return "";
        }
    }

    private String getWebRollRecordKey() {
        return "list:webTaskTurntable:RollRecord";
    }

    public void addWebRollRecord(String rollRecord) {
        Long listSize = clusterTemplate.opsForList().leftPush(getWebRollRecordKey(), rollRecord);
        clusterTemplate.expire(getWebRollRecordKey(), ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        if (null != listSize && listSize > MAX_SIZE) {
            clusterTemplate.opsForList().trim(getWebRollRecordKey(), 0, MAX_SIZE);
        }
    }

    @Cacheable(value = "getWebRollRecordList", cacheManager = CaffeineCacheConfig.EXPIRE_5S_AFTER_WRITE)
    public List<String> getWebRollRecordList() {
        try {
            List<String> jsonList = clusterTemplate.opsForList().range(getWebRollRecordKey(), 0, MAX_SIZE - 1);
            if (CollectionUtils.isEmpty(jsonList)) {
                return Collections.emptyList();
            }
            List<String> resultList = new ArrayList<>(MAX_SIZE);
            resultList.addAll(jsonList);
            return resultList;
        } catch (Exception e) {
            logger.error("getWebRollRecordList error", e);
            return Collections.emptyList();
        }
    }

    /**
     * 用户抽奖记录
     */
    private String getWebUserRecordKey(String uid) {
        return "list:webTaskTurntable:Record:" + uid;
    }

    public void addWebUserRecord(String uid, String record) {
        Long listSize = clusterTemplate.opsForList().leftPush(getWebUserRecordKey(uid), record);
        clusterTemplate.expire(getWebUserRecordKey(uid), ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        if (null != listSize && listSize > MAX_USER_SIZE) {
            clusterTemplate.opsForList().trim(getWebRollRecordKey(), 0, MAX_USER_SIZE);
        }
    }

    public List<String> getWebUserPageRecord(String uid, int start, int end) {
        try {
            String key = getWebUserRecordKey(uid);
            List<String> jsonList = clusterTemplate.opsForList().range(key, start, end - 1);
            if (CollectionUtils.isEmpty(jsonList)) {
                return Collections.emptyList();
            }
            return jsonList;
        } catch (Exception e) {
            logger.error("getWebUserPageRecord error", e);
            return Collections.emptyList();
        }
    }


    /**
     * 用户耗金币榜单
     */
    private String getWebDrawRankKey(String rankDate) {
        return "zSet:webTaskTurntable:DrawRank:" + rankDate;
    }

    public int getWebDrawZSetRankScore(String uid, String rankDate) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getWebDrawRankKey(rankDate), uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getWebDrawZSetRankScore error uid={} rankDate={}, e={}", uid, rankDate, e.getMessage(), e);
            return 0;
        }
    }

    public int incrWebDrawRankScore(String uid, String rankDate, int score) {
        try {
            String key = getWebDrawRankKey(rankDate);
            int curScore = getWebDrawZSetRankScore(uid, rankDate);
            int nowScore = curScore + score;
            double rankScore = new BigDecimal(nowScore + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            clusterTemplate.opsForZSet().add(key, uid, rankScore);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
            return nowScore;
        } catch (Exception e) {
            logger.info("incrWebDrawRankScore error uid={} rankDate={} score={} error={}", uid, rankDate, score, e.getMessage(), e);
            return 0;
        }
    }


    /**
     * 获取带分数排行榜
     */
    public Map<String, Integer> getWebDrawRankMap(String rankDate, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getWebDrawRankKey(rankDate);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

}
