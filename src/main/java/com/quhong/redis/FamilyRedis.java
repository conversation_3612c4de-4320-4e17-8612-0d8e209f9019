package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class FamilyRedis {
    private static final Logger logger = LoggerFactory.getLogger(FamilyRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 10;
    private static final int MAX_SIZE = 30;
    private String expireDate = "";
    private String expireDateNew = "";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    /**
     * @param familyId t_family表主键id
     * @param time
     */
    public void addFamilyDismiss(Integer familyId, int time) {
        try {
            String key = getFamilyDismissKey();
            clusterTemplate.opsForZSet().add(key, String.valueOf(familyId), time);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(expireDate)) {
                clusterTemplate.expire(key, 30, TimeUnit.DAYS);
                expireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("addFamilyDismiss error familyId={}", familyId, e);
        }
    }

    /**
     * 获取分数
     */
    public int getFamilyDismissScore(Integer familyId) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getFamilyDismissKey(), String.valueOf(familyId));
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getFamilyDismissScore error familyId={}", familyId, e);
            return 0;
        }
    }

    /**
     * 撤销解散
     */
    public int cancelFamilyDismiss(Integer familyId) {
        try {
            Long score = clusterTemplate.opsForZSet().remove(getFamilyDismissKey(), String.valueOf(familyId));
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("cancelFamilyDismiss error familyId={}", familyId, e);
            return 0;
        }
    }

    /**
     *
     */
    public Map<String, Integer> getFamilyDismissMapByScore(int max) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getFamilyDismissKey();
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().rangeByScoreWithScores(key, 0, max);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    public void addFamilyNoticeLast(String uid, Integer familyId, int time) {
        try {
            String key = getUidNoticeLastKey(uid, familyId);
            clusterTemplate.opsForValue().set(key, String.valueOf(time));
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(expireDateNew)) {
                clusterTemplate.expire(key, 30, TimeUnit.DAYS);
                expireDateNew = dateStr;
            }
        } catch (Exception e) {
            logger.info("addFamilyDismiss error uid={}", uid, e);
        }
    }

    public int getFamilyNoticeLast(String uid, Integer familyId) {
        try {
            String value = clusterTemplate.opsForValue().get(getUidNoticeLastKey(uid, familyId));
            return null == value ? 0 : Integer.parseInt(value);
        } catch (Exception e) {
            return 0;
        }
    }

    public void addUidKickOutTime(String uid, Integer familyId, int time) {
        try {
            String key = getUidKickOutKey(uid, familyId);
            clusterTemplate.opsForValue().set(key, String.valueOf(time));
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("addUidKickOutTime error uid={}", uid, e);
        }
    }

    public int getUidKickOutTime(String uid, Integer familyId) {
        try {
            String value = clusterTemplate.opsForValue().get(getUidKickOutKey(uid, familyId));
            return null == value ? 0 : Integer.parseInt(value);
        } catch (Exception e) {
            return 0;
        }
    }

    private String getFamilyDismissKey() {
        return "zset:family:dismiss:key";
    }

    private String getUidNoticeLastKey(String uid, Integer familyId) {
        return "str:family:notice:last:key:" + uid + ":" + familyId;
    }

    private String getUidKickOutKey(String uid, Integer familyId) {
        return "str:family:kick:out:key:" + uid + ":" + familyId;
    }

}
