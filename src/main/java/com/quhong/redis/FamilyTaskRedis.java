package com.quhong.redis;

import com.quhong.config.FamilyTaskConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.dao.FamilyDevoteDao;
import com.quhong.mysql.dao.FamilyMemberDao;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.CacheUtils;
import com.quhong.vo.FamilyTaskInfoVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
@Lazy
public class FamilyTaskRedis {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    private static final String ACCUMULATION_1 = "accumulation_1";
    private static final String ACCUMULATION_2 = "accumulation_2";
    private static final String ACCUMULATION_3 = "accumulation_3";
    private static final String ACCUMULATION_4 = "accumulation_4";
    private static final String ACCUMULATION_5 = "accumulation_5";

    private static final String FAMILY_CHECK = "family_check";
    private static final int FAMILY_CHECK_LIMIT = 1;
    private static final int FAMILY_CHECK_DEVOTE = 20;

    private static final String FAMILY_DEVOTE_CHECK = "family_devote_check";
    private static final int FAMILY_DEVOTE_CHECK_LIMIT = 1;
    private static final int FAMILY_DEVOTE_CHECK_DEVOTE = 30;

    private static final String INVITE_FAMILY_MEMBER = "invite_family_member";
    private static final int INVITE_FAMILY_MEMBER_LIMIT = 5;
    private static final int INVITE_FAMILY_MEMBER_DEVOTE = 50;
    private static final int INVITE_FAMILY_MEMBER_NEW_DEVOTE = 100;

    private static final String SEND_FAMILY_GIFT = "send_family_gift";
    private static final int SEND_FAMILY_GIFT_LIMIT = 1;
    private static final int SEND_FAMILY_GIFT_DEVOTE = 50;

    private static final String RECEIVE_FAMILY_GIFT = "receive_family_gift";
    private static final int RECEIVE_FAMILY_GIFT_LIMIT = 1;
    private static final int RECEIVE_FAMILY_GIFT_DEVOTE = 50;

    private static final String TAKE_MIC = "take_mic";
    private static final int TAKE_MIC_LIMIT = 30;
    private static final int TAKE_MIC_DEVOTE = 1;

    private static final String SEND_GIFT_IN_EVENT = "send_gift_in_event";
    private static final int SEND_GIFT_IN_EVENT_LIMIT = 2;
    private static final int SEND_GIFT_IN_EVENT_DEVOTE = 50;

    private static final String PLAY_GAME = "play_game";
    private static final int PLAY_GAME_LIMIT = 5;
    private static final int PLAY_GAME_DEVOTE = 20;


    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;
    @Resource
    private FamilyDevoteDao familyDevoteDao;
    @Resource
    private FamilyMemberDao familyMemberDao;
    @Resource
    private FamilyTaskConfig familyTaskConfig;


    public void familyCheck(String uid) {
        incrFamilyMemberLimitDevote(uid, FAMILY_CHECK, FAMILY_CHECK_DEVOTE, 1, FAMILY_CHECK_LIMIT);
    }

    public void familyDevoteCheck(String uid) {
        incrFamilyMemberLimitDevote(uid, FAMILY_DEVOTE_CHECK, FAMILY_DEVOTE_CHECK_DEVOTE, 1, FAMILY_DEVOTE_CHECK_LIMIT);
    }

    public void takeMic(String uid, int takeMicMinutes) {
        incrFamilyMemberLimitDevote(uid, TAKE_MIC, takeMicMinutes * TAKE_MIC_DEVOTE, takeMicMinutes, TAKE_MIC_LIMIT);
    }

    public void playGame(String uid) {
        incrFamilyMemberLimitDevote(uid, PLAY_GAME, PLAY_GAME_DEVOTE, 1, PLAY_GAME_LIMIT);
    }

    public void sendRoomGift(String uid) {
        incrFamilyMemberLimitDevote(uid, SEND_FAMILY_GIFT, SEND_FAMILY_GIFT_DEVOTE, 1, SEND_FAMILY_GIFT_LIMIT);
    }

    public void sendGiftInEvent(String uid) {
        incrFamilyMemberLimitDevote(uid, SEND_GIFT_IN_EVENT, SEND_GIFT_IN_EVENT_DEVOTE, 1, SEND_GIFT_IN_EVENT_LIMIT);
    }

    public void receiveRoomGift(String uid) {
        incrFamilyMemberLimitDevote(uid, RECEIVE_FAMILY_GIFT, RECEIVE_FAMILY_GIFT_DEVOTE, 1, RECEIVE_FAMILY_GIFT_LIMIT);
    }

    public void inviteFamilyMember(String uid, String aid, String deviceId) {
        String key = "set:inviteFamilyMemberTask" + DateHelper.ARABIAN.formatDateInDay();
        if (Boolean.TRUE.equals(clusterRedis.opsForSet().isMember(key, deviceId))) {
            return;
        }
        clusterRedis.opsForSet().add(key, deviceId);
        clusterRedis.expire(key, 1, TimeUnit.DAYS);
        incrFamilyMemberLimitDevote(uid, INVITE_FAMILY_MEMBER, ActorUtils.getRegDays(aid) <= 7 ? INVITE_FAMILY_MEMBER_NEW_DEVOTE : INVITE_FAMILY_MEMBER_DEVOTE, 1, INVITE_FAMILY_MEMBER_LIMIT);
    }

    /**
     * 家族个人任务，每日有次数限制
     */
    public void incrFamilyMemberLimitDevote(String uid, String type, int devote, int count, int limit) {
        int familyId = familyMemberDao.getAllMemberFromCache().getOrDefault(uid, 0);
        if (familyId == 0) {
            return;
        }
        int limitCount = checkLimit(count, limit, getKey(familyId, uid), type);
        if (limitCount > 0) {
            incrFamilyDevote(uid, familyId, type, devote, limitCount);
        }
    }

    /**
     * 家族共享任务，每日有次数限制
     */
    public void incrFamilyLimitDevote(int familyId, String type, int devote, int count, int limit) {
        int limitCount = checkLimit(count, limit, getKey(familyId, null), type);
        if (limitCount > 0) {
            incrFamilyDevote(null, familyId, type, devote, limitCount);
        }
    }

    public void incrFamilyDevote(String uid, int familyId, String type, int devote, int count) {
        try {
            String key = getKey(familyId, uid);
            clusterRedis.opsForHash().increment(key, type, count);
            familyDevoteDao.incrDevote(uid, familyId, devote);
            logger.info("incrDevote uid={} familyId={} type={} devote={}", uid, familyId, type, devote);
            if (!CacheUtils.hasKey(key)) {
                clusterRedis.expire(key, 2, TimeUnit.DAYS);
                CacheUtils.put(key, familyId);
            }
        } catch (Exception e) {
            logger.error("incrFamilyDevote error uid={} type={}", uid, type, e);
        }
    }

    private int checkLimit(int count, int limit, String key, String hk) {
        if (CacheUtils.get(key + hk) != null) {
            return -1;
        }

        int current = getTodayFamilyTaskCount(key, hk);
        if (current >= limit) {
            CacheUtils.put(key + hk, -1);
            return -1;
        }

        if (count + current > limit) {
            count = limit - current;
        }
        return count;
    }

    private int getTodayFamilyTaskCount(String key, String hk) {
        try {
            Object value = clusterRedis.opsForHash().get(key, hk);
            return ObjectUtils.isEmpty(value) ? 0 : Integer.parseInt(String.valueOf(value));
        } catch (Exception e) {
            logger.error("getTodayFamilyTaskCount error key={} hk={}", key, hk, e);
            return 0;
        }
    }

    /**
     * 获取家族任务进度
     */
    public List<FamilyTaskInfoVO> getFamilyTaskProcess(int familyId, String uid) {
        try {
            Map<String, Integer> taskMap = new HashMap<>();
            Map<Object, Object> entries = clusterRedis.opsForHash().entries(getKey(familyId, uid));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                String taskName = String.valueOf(entry.getKey());
                int completeCount = Integer.parseInt(String.valueOf(entry.getValue()));
                taskMap.put(taskName, completeCount);
            }
            List<FamilyTaskInfoVO> taskDataList = new ArrayList<>();
            for (FamilyTaskInfoVO taskData : familyTaskConfig.getFamilyTasks()) {
                String taskId = taskData.getId();
                int completeCount = taskMap.getOrDefault(taskId, 0);
                FamilyTaskInfoVO vo = new FamilyTaskInfoVO();
                BeanUtils.copyProperties(taskData, vo);
                vo.setName(String.format(taskData.getName(), completeCount, taskData.getTotal()));
                vo.setNameAr(String.format(taskData.getNameAr(), completeCount, taskData.getTotal()));
                vo.setCompleted(completeCount >= taskData.getTotal() ? 1 : 0);
                taskDataList.add(vo);
            }
            return taskDataList;
        } catch (Exception e) {
            logger.error("getFamilyTaskProcess error e={}", e.getMessage(), e);
            return familyTaskConfig.getFamilyTasks();
        }
    }

    private String getKey(int familyId, String uid) {
        return "hash:familyTaskLimit:" + DateHelper.ARABIAN.formatDateInDay() + ":" + familyId + ":" + uid;
    }
}
