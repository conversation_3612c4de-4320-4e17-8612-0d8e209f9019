package com.quhong.enums;

public class UserHttpCode extends HttpCode {

    public static final HttpCode NOVICE_TASK_NOT_EXIST = new HttpCode(2201,"Novice task not exist");
    public static final HttpCode NOVICE_TASK_NOT_FINISH = new HttpCode(2202,"Novice task not finished");
    public static final HttpCode REWARD_HAS_RECEIVED = new HttpCode(2203,"The reward has been received");
    public static final HttpCode INSUFFICIENT_NUMBER = new HttpCode(5001, "not_enough_coin");
    public static final HttpCode INSUFFICIENT_TICKET = new HttpCode(5001, "not_enough_ticket");

    public static final HttpCode NO_NEW_USERS_CANNOT_RECEIVE = new HttpCode(2210, "Non-new users cannot receive newcomer tasks rewards.", "لا يمكن للمستخدمين غير الجدد الحصول على مكافآت مهام الوافد الجديد");
    public static final HttpCode NOT_COMPLETED_AND_CANNOT_RECEIVE = new HttpCode(2211, "The task has not been completed and the reward cannot be received.", "لم تكتمل المهمة ولا يمكن استلام المكافأة");
    public static final HttpCode THIS_REWARD_HAS_BEEN_GOTTEN = new HttpCode(2212, "This reward has been gotten.", "تم الحصول على هذه الجائزة بالفعل");

    public static final HttpCode NEED_UPGRADE = new HttpCode(2213, "Please upgrade to the latest version to use it.", "يرجى الترقية إلى أحدث إصدار لاستخدامه.");

    public static final HttpCode FAMILY_FULL = new HttpCode(2112, "The number of family members is full", "عدد أعضاء العائلة كامل");
    public static final HttpCode FAMILY_DO_NOT_REAPPLY = new HttpCode(2113, "Your application has been rejected,No re-application is allowed within 24 hours.", "لقد تم رفض طلبك، ولا يسمح بإعادة تقديم الطلب خلال 24 ساعة.");
    public static final HttpCode FAMILY_ONLY_JOIN_ONE = new HttpCode(2114, "Currently a member of the family", "معلومات العائلة");
    public static final HttpCode FAMILY_ADMIN_LIMIT = new HttpCode(2111, "The maximum number of administrators has been reached", "وصل عدد الإداريين إلى الحد الأعلى");

    public static final HttpCode CODE_NOT_ALLOW = new HttpCode(2120, "There are sensitive words in the current family name/announcement, please modify it and submit it.",
            "يحتوي اسم العائلة / الإعلان الحالي على كلمات حساسة، يرجى تعديلها ثم تقديمها.");

    public static final HttpCode FAMILY_JOIN_ALREADY = new HttpCode(2121, "You have joined the family.",
            "لقد انضممت إلى العائلة.");
    public static final HttpCode FAMILY_NOT_EXIST = new HttpCode(2122, "The family does not exist",
            "العائلة غير موجودة");
    public static final HttpCode CONTENT_NOT_ALLOW = new HttpCode(2123, "There are sensitive words in the application information, please modify it before applying.",
            "هناك كلمات حساسة في معلومات التطبيق، يرجى تعديلها قبل التقديم.");
    public static final HttpCode FAMILY_DO_NOT_KICK = new HttpCode(2124, "You have been kicked out of the family and cannot apply again within 24 hours.", "لقد تم طردك من العائلة ولا يمكنك التقديم مرة أخرى خلال 24 ساعة.");


    public static final HttpCode FAMILY_JOIN_MY = new HttpCode(2301, "User has joined", "المستخدم قد انضم");
    public static final HttpCode FAMILY_JOIN_OTHER= new HttpCode(2300, "The user has joined other family.", "انضم المستخدم إلى عائلة أخرى.");

}
