package com.quhong.handler;

import com.alibaba.fastjson.JSONObject;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.data.ActorData;
import com.quhong.data.GiftContext;
import com.quhong.data.dto.AdvanceGiftDTO;
import com.quhong.data.dto.SendGiftDTO;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.AdvancedGiftDao;
import com.quhong.mongo.data.AdvancedGiftData;
import com.quhong.msg.obj.GiftInfoObject;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.room.AdvancedGiftMsg;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.mysql.data.GiftData;
import com.quhong.redis.GiftPlayRedis;
import com.quhong.redis.GiftRedis;
import com.quhong.service.MarsMsgService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 进阶礼物处理
 */
@Component
public class AdvancedGiftHandler implements IGiftHandler {
    protected static final Logger logger = LoggerFactory.getLogger(AdvancedGiftHandler.class);

    @Resource
    private AdvancedGiftDao advancedGiftDao;
    @Resource
    private MarsMsgService marsMsgService;
    @Resource
    private AdvancedGiftHandler advancedGiftHandler;
    @Resource
    private GiftRedis giftRedis;
    @Resource
    private GiftPlayRedis giftPlayRedis;
    @Resource
    private ActorDao actorDao;

    @Cacheable(value = "getAdvanceGiftListCache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public AdvanceGiftDTO getAdvanceGiftListCache(){
        AdvanceGiftDTO dto = new AdvanceGiftDTO();
        Map<String, AdvancedGiftData.AdvancedConfig> advanceGiftFirstMap = new HashMap<>();
        Map<String, AdvanceGiftDTO.AdvancedGiftSecondConfig> advanceGiftSecondMap = new HashMap<>();

        for (AdvancedGiftData data : advancedGiftDao.getAllAdvancedGiftList()) {
            if(AdvancedGiftDao.PLAY_TYPE_SEND_NUM.equals(data.getPlayType())){
                for (AdvancedGiftData.AdvancedConfig advancedConfig: data.getConfigList()) {
                    String giftIdNumber = data.getGiftId() + ":" + advancedConfig.getGiftNum();
                    advanceGiftFirstMap.put(giftIdNumber, advancedConfig);
                }

            } else if (AdvancedGiftDao.PLAY_TYPE_ADVANCED.equals(data.getPlayType())) {
                AdvanceGiftDTO.AdvancedGiftSecondConfig advancedGiftSecondConfig = new AdvanceGiftDTO.AdvancedGiftSecondConfig();
                advancedGiftSecondConfig.setObjId(data.get_id().toString());
                advancedGiftSecondConfig.setGiftId(data.getGiftId());
                advancedGiftSecondConfig.setPlayTimeOut(data.getPlayTimeOut());
                advancedGiftSecondConfig.setScreenEn(data.getScreenEn());
                advancedGiftSecondConfig.setScreenAr(data.getScreenAr());
                advancedGiftSecondConfig.setAdvancedConfigList(data.getConfigList());
                for (AdvancedGiftData.AdvancedConfig advancedConfig: data.getConfigList()) {
                    advanceGiftSecondMap.put(String.valueOf(advancedConfig.getGiftId()), advancedGiftSecondConfig);
                }
            }
        }

        dto.setAdvanceGiftFirstMap(advanceGiftFirstMap);
        dto.setAdvanceGiftSecondMap(advanceGiftSecondMap);
        return dto;
    }

    @Override
    public void process(SendGiftDTO req, GiftData giftData, GiftContext context) {
        long timeMillis = System.currentTimeMillis();
        handlerProcess(req, giftData, context);
        logger.info("handler luckyGift Process aid={} cost={}", req.getAid(), System.currentTimeMillis() - timeMillis);

    }




    public void handlerProcess(SendGiftDTO req, GiftData giftData, GiftContext context) {
        GiftData effectGiftData = null;
        try{
            AdvanceGiftDTO dto = advancedGiftHandler.getAdvanceGiftListCache();
            String reqUid = req.getUid();
            ActorData actorData = actorDao.getActorDataFromCache(reqUid);
            if(actorData == null){
                return;
            }
            String userName = actorData.getName();
            String userHead = ImageUrlGenerator.generateRoomUserUrl(actorData.getHead());

            AdvancedGiftMsg advancedGiftMsg = new AdvancedGiftMsg();
            Map<String, AdvancedGiftData.AdvancedConfig> advanceGiftFirstMap = dto.getAdvanceGiftFirstMap();
            Map<String, AdvanceGiftDTO.AdvancedGiftSecondConfig> advanceGiftSecondMap = dto.getAdvanceGiftSecondMap();
            // 进阶玩法1: 发送某个礼物指定数量时修改成另外一个礼物动画
            String giftIdNumber = req.getGiftId() + ":" + req.getNumber();
            AdvancedGiftData.AdvancedConfig advancedConfig = advanceGiftFirstMap.get(giftIdNumber);
            if(advancedConfig != null){
                logger.info("handler advanceGiftFirstMap: {}", JSONObject.toJSONString(advancedConfig));
                effectGiftData = giftRedis.getGiftData(advancedConfig.getGiftId());
                if(!StringUtils.isEmpty(advancedConfig.getScreenEn())){
                    RoomNotificationMsg msg = new RoomNotificationMsg();
                    msg.setUid(reqUid);
                    msg.setUser_name(userName);
                    msg.setUser_head(userHead);
                    msg.setText(advancedConfig.getScreenEn().replace("#username#", userName));
                    msg.setText_ar(advancedConfig.getScreenAr().replace("#username#", userName));
                    List<HighlightTextObject> list = new ArrayList<>();
                    HighlightTextObject object = new HighlightTextObject();
                    object.setText(userName);
                    object.setHighlightColor("#FFE200");
                    list.add(object);
                    msg.setHighlight_text(list);
                    msg.setHighlight_text_ar(list);
                    msg.setWeb_type(1);
                    marsMsgService.asyncSendMsg("all_" + req.getRoomId(), null, msg, false);
                }
                advancedGiftMsg.setG_gift_info(getGiftInfo(effectGiftData, req.getNumber()));
            }

            // 进阶玩法2: 在指定时间内发送一个或多个礼物累计达到指定数量, 满足时修改成另外一个礼物动画
            AdvanceGiftDTO.AdvancedGiftSecondConfig advancedGiftSecondConfig = advanceGiftSecondMap.get(String.valueOf(req.getGiftId()));
            if(advancedGiftSecondConfig != null){
                int totalNum = req.getNumber() * context.getAidSet().size();
                String objId = advancedGiftSecondConfig.getObjId();
                Integer playTimeOut =advancedGiftSecondConfig.getPlayTimeOut();
                giftPlayRedis.incAdvancedGiftHashNum(objId, reqUid, String.valueOf(req.getGiftId()), totalNum, playTimeOut);

                int finishProcess = 0;
                int totalProcess = 0;
                Map<String, Integer> allHashMap = giftPlayRedis.getAllAdvancedGiftValue(objId, reqUid);
                for (AdvancedGiftData.AdvancedConfig config: advancedGiftSecondConfig.getAdvancedConfigList()) {
                    Integer taskNum = config.getGiftNum();
                    Integer currentNum = allHashMap.getOrDefault(String.valueOf(config.getGiftId()), 0);
                    if(currentNum >= taskNum){
                        finishProcess += 1;
                    }
                    totalProcess += 1;
                }

                if(finishProcess >= totalProcess){
                    giftPlayRedis.removeAdvancedGiftKey(objId, reqUid);
                    effectGiftData = giftRedis.getGiftData(advancedGiftSecondConfig.getGiftId());
                    if(!StringUtils.isEmpty(advancedGiftSecondConfig.getScreenEn())){
                        RoomNotificationMsg msg = new RoomNotificationMsg();
                        msg.setUid(reqUid);
                        msg.setUser_name(userName);
                        msg.setUser_head(userHead);
                        msg.setText(advancedGiftSecondConfig.getScreenEn().replace("#username#", userName));
                        msg.setText_ar(advancedGiftSecondConfig.getScreenAr().replace("#username#", userName));
                        List<HighlightTextObject> list = new ArrayList<>();
                        HighlightTextObject object = new HighlightTextObject();
                        object.setText(userName);
                        object.setHighlightColor("#FFE200");
                        list.add(object);
                        msg.setHighlight_text(list);
                        msg.setHighlight_text_ar(list);
                        msg.setWeb_type(1);
                        marsMsgService.asyncSendMsg("all_" + req.getRoomId(), null, msg, false);
                    }
                    GiftInfoObject giftInfoObject = getGiftInfo(effectGiftData, req.getNumber());
                    logger.info("handler advanceGiftSecondMap giftInfoObject {}", JSONObject.toJSONString(giftInfoObject));
                    advancedGiftMsg.setG_gift_info(giftInfoObject);
                }
            }

            if(advancedGiftMsg.getG_gift_info() != null){
                marsMsgService.asyncSendMsg(req.getRoomId(), null, advancedGiftMsg, true);
            }

        }catch (Exception e){
            logger.error("handlerAdvancedGift error uid={}, giftId={}, e={}", req.getUid(), req.getGiftId(), e.getMessage(), e);
        }
    }

    private GiftInfoObject getGiftInfo(GiftData giftData, int number) {
        GiftInfoObject giftInfoObject = new GiftInfoObject();
        giftInfoObject.setGiftId(giftData.getRid());
        giftInfoObject.setGiftType(giftData.getGatype() == 6 ? 1 : giftData.getGatype());
        giftInfoObject.setGiftNumber(number);
        giftInfoObject.setGiftIcon(giftData.getGicon());
        giftInfoObject.setGiftTime(giftData.getGtime());
        giftInfoObject.setGiftPrice(giftData.getPrice());
        return giftInfoObject;
    }

}
