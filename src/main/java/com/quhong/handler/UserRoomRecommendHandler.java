package com.quhong.handler;

import com.quhong.data.CommonMqTopicData;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.redis.NewUserRoomRecommendRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Component
public class UserRoomRecommendHandler implements MqMessageHandler{

    private static final Logger logger = LoggerFactory.getLogger(UserRoomRecommendHandler.class);
    private static final Map<String, Integer> GAME_TYPE_MAP = new HashMap<>();


    static {
        GAME_TYPE_MAP.put(CommonMqTaskConstant.PLAY_WHEEL, 1);
        GAME_TYPE_MAP.put(CommonMqTaskConstant.PLAY_LUDO, 2);
        GAME_TYPE_MAP.put(CommonMqTaskConstant.PLAY_UMO, 3);
        GAME_TYPE_MAP.put(CommonMqTaskConstant.PLAY_MONSTER_CRUSH, 4);
    }

    @Resource
    private NewUserRoomRecommendRedis newUserRoomRecommendRedis;

    @Override
    public void process(CommonMqTopicData mqData) {
        if (mqData == null || StringUtils.isEmpty(mqData.getUid()) || StringUtils.isEmpty(mqData.getRoomId())) {
            return;
        }
        String uid = mqData.getUid();
        String roomId = mqData.getRoomId();
        String item = mqData.getItem();

        switch (item) {
            case CommonMqTaskConstant.SEND_ROOM_GIFT:
            case CommonMqTaskConstant.ON_MIC_TIME:
                handleUserRoomRecommend(uid, roomId);
                break;
            case CommonMqTaskConstant.PLAY_LUDO:
            case CommonMqTaskConstant.PLAY_WHEEL:
            case CommonMqTaskConstant.PLAY_UMO:
            case CommonMqTaskConstant.PLAY_MONSTER_CRUSH:
                handleUserPlayGameRecommend(uid, roomId, item);
                break;
            default:
                break;
        }
    }

    private void handleUserRoomRecommend(String uid, String roomId) {
        newUserRoomRecommendRedis.addUserOnRoomGiftMicRecord(uid, roomId);
    }

    private void handleUserPlayGameRecommend(String uid, String roomId, String item) {
        Integer gameType = GAME_TYPE_MAP.get(item);
        if(gameType == null){
            return;
        }
        newUserRoomRecommendRedis.incRecommendGameNum(uid, gameType);
    }
}
