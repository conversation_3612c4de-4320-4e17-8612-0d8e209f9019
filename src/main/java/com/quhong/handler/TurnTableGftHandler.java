/*
package com.quhong.handler;

import com.alibaba.fastjson.JSONObject;
import com.quhong.data.GiftContext;
import com.quhong.data.dto.SendGiftDTO;
import com.quhong.mysql.data.GiftData;
import com.quhong.redis.RedisTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

*/
/**
 * 转盘礼物
 *//*

@Component
public class TurnTableGftHandler implements IGiftHandler {
    private static final Logger logger = LoggerFactory.getLogger(TurnTableGftHandler.class);
    private static final int TURNTABLE_GIFT_ID = 77;

    @Resource
    private RedisTaskService redisTaskService;

    @Override
    public void process(SendGiftDTO req, GiftData giftData, GiftContext context) {
        // 转盘礼物
        if (TURNTABLE_GIFT_ID == giftData.getRid() && !StringUtils.isEmpty(req.getRoomId())) {
            logger.info("send turn table gift uid={}", req.getUid());
            JSONObject msgBody = new JSONObject();
            msgBody.put("room_id", req.getRoomId());
            msgBody.put("times", 10);
            redisTaskService.broadcastMessage("turn_table_gift", msgBody);
        }
    }
}
*/
