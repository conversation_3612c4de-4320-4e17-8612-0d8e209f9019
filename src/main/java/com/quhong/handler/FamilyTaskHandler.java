package com.quhong.handler;

import com.quhong.data.CommonMqTopicData;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.mongo.dao.FamilyDevoteDao;
import com.quhong.mysql.dao.FamilyMemberDao;
import com.quhong.mysql.dao.RoomEventDao;
import com.quhong.redis.FamilyTaskRedis;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Set;

@Component
public class FamilyTaskHandler {

    private static final Logger logger = LoggerFactory.getLogger(FamilyTaskHandler.class);
    @Resource
    private FamilyDevoteDao familyDevoteDao;
    @Resource
    private FamilyMemberDao familyMemberDao;
    @Resource
    private FamilyTaskRedis familyTaskRedis;
    @Resource
    private RoomEventDao roomEventDao;


    // @Override
    public void process(CommonMqTopicData mqData) {
        if (mqData == null || StringUtils.isEmpty(mqData.getUid())) {
            return;
        }
        String uid = mqData.getUid();
        String item = mqData.getItem();

        switch (item) {
            case CommonMqTaskConstant.SEND_ROOM_GIFT:
                boolean familyGift = "true".equals(mqData.getJsonData());
                // 礼物发送者，在房间内赠送非家族礼物1钻石=1战力值；赠送家族礼物1钻石=2战力值
                handleFamilyDevote(mqData.getUid(), mqData.getValue() * (familyGift ? 2 : 1));
                // 礼物接收者，在房间内收礼物1钻石=1战力值
                if (null != mqData.getDataList()) {
                    for (String aid : mqData.getDataList()) {
                        handleFamilyDevote(aid, mqData.getRemainValue());
                        // 计次任务
                        if (familyGift) {
                            familyTaskRedis.receiveRoomGift(aid);
                        }
                    }
                }
                // 计次任务
                if (familyGift) {
                    familyTaskRedis.sendRoomGift(uid);
                }
                // 在家族活动中送礼
                if (isSameFamily(RoomUtils.getRoomHostId(mqData.getRoomId()), uid)) {
                    Set<String> inProgressRoomSet = roomEventDao.getInProgressRoomSet();
                    if (inProgressRoomSet.contains(mqData.getRoomId())) {
                        familyTaskRedis.sendGiftInEvent(uid);
                    }
                }
                break;
            case CommonMqTaskConstant.ON_MIC_TIME:
                // 在家族房间上麦
                if (!isSameFamily(RoomUtils.getRoomHostId(mqData.getRoomId()), uid)) {
                    return;
                }
                familyTaskRedis.takeMic(uid, mqData.getValue());
                break;
            case CommonMqTaskConstant.PLAY_LUDO:
            case CommonMqTaskConstant.PLAY_UMO:
            case CommonMqTaskConstant.PLAY_MONSTER_CRUSH:
            case CommonMqTaskConstant.PLAY_DOMINO:
            case CommonMqTaskConstant.PLAY_CARROM_POOL:
                // 在家族房间玩游戏
                if (!isSameFamily(RoomUtils.getRoomHostId(mqData.getRoomId()), uid)) {
                    return;
                }
                familyTaskRedis.playGame(uid);
                break;
            default:
                break;
        }
    }

    private boolean isSameFamily(String uid, String aid) {
        int fromFamilyId = familyMemberDao.getAllMemberFromCache().getOrDefault(uid, 0);
        int toFamilyId = familyMemberDao.getAllMemberFromCache().getOrDefault(aid, 0);
        if (0 == fromFamilyId || 0 == toFamilyId) {
            return false;
        }
        return fromFamilyId == toFamilyId;
    }

    private void handleFamilyDevote(String uid, int devote) {
        int familyId = familyMemberDao.getAllMemberFromCache().getOrDefault(uid, 0);
        if (familyId == 0) {
            return;
        }
        familyDevoteDao.incrDevote(uid, familyId, devote);
    }
}
