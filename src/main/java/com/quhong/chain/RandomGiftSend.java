package com.quhong.chain;

import com.alibaba.fastjson.JSON;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.data.GiftContext;
import com.quhong.data.dto.SendGiftDTO;
import com.quhong.data.vo.SendGiftVO;
import com.quhong.data.vo.ZipInfoVO;
import com.quhong.msg.obj.GiftInfoObject;
import com.quhong.mysql.data.GiftData;
import com.quhong.redis.RandomGiftRedis;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/3/29
 */
@Component
public class RandomGiftSend extends AbstractGiftSend {

    private static final Map<Integer, String> RANDOM_GIFT_CONFIG_MAP = new HashMap<>();

    @Resource
    private RandomGiftRedis randomGiftRedis;

    @Override
    protected void send(SendGiftDTO req, SendGiftVO resp, GiftData giftData, GiftContext context) {
        if (giftData.getBlindBox() != 1) {
            return;
        }
        initPoolCheck(giftData);
        // 从盲盒奖池中获取礼物
        int randomGiftId = randomGiftRedis.getPoolGiftId(giftData.getRid());
        GiftData randomGiftData = giftRedis.getGiftData(randomGiftId);
        if (randomGiftData != null) {
            resp.setGiftInfo(getGiftInfo(randomGiftData, giftData));
            context.setGiftInfo(getGiftInfo(randomGiftData));
        }
    }

    private GiftInfoObject getGiftInfo(GiftData randomGiftData, GiftData giftData) {
        GiftInfoObject giftInfoObject = new GiftInfoObject();
        giftInfoObject.setGiftId(randomGiftData.getRid());
        giftInfoObject.setGiftType(randomGiftData.getGatype() == 6 ? 1 : randomGiftData.getGatype());
        giftInfoObject.setGiftNumber(1);
        giftInfoObject.setGiftIcon(giftData.getGicon());
        giftInfoObject.setGiftTime(giftData.getGtime());
        giftInfoObject.setGiftPrice(giftData.getPrice());
        giftInfoObject.setGiftRandom(1);
        giftInfoObject.setGiftRandomIcon(randomGiftData.getGicon());
        return giftInfoObject;
    }

    private GiftInfoObject getGiftInfo(GiftData giftData) {
        GiftInfoObject giftInfoObject = new GiftInfoObject();
        giftInfoObject.setGiftId(giftData.getRid());
        giftInfoObject.setGiftType(giftData.getGatype() == 6 ? 1 : giftData.getGatype());
        giftInfoObject.setGiftNumber(1);
        giftInfoObject.setGiftIcon(giftData.getGicon());
        giftInfoObject.setGiftTime(giftData.getGtime());
        giftInfoObject.setGiftPrice(giftData.getPrice());
        giftInfoObject.setGiftRandom(1);
        giftInfoObject.setGiftRandomIcon("");
        return giftInfoObject;
    }

    /**
     * 检查是否需要初始化奖池
     */
    private void initPoolCheck(GiftData giftData) {
        if (configChanged(giftData)) {
            randomGiftRedis.clearPool(giftData.getRid());
            initPoolSize(giftData);
        } else {
            int poolSize = randomGiftRedis.getPoolSize(giftData.getRid());
            if (poolSize <= 0) {
                initPoolSize(giftData);
            } else if (poolSize <= 100) {
                BaseTaskFactory.getFactory().addSlow(new Task() {
                    @Override
                    protected void execute() {
                        initPoolSize(giftData);
                    }
                });
            }
        }
    }

    /**
     * 校验gift的zipInfo是否有改变
     */
    private boolean configChanged(GiftData giftData) {
        String strConfig = giftData.getZipInfo();
        if (RANDOM_GIFT_CONFIG_MAP.containsKey(giftData.getRid())) {
            if (strConfig.equals(RANDOM_GIFT_CONFIG_MAP.get(giftData.getRid()))) {
                return false;
            } else {
                RANDOM_GIFT_CONFIG_MAP.put(giftData.getRid(), strConfig);
                return true;
            }
        } else {
            RANDOM_GIFT_CONFIG_MAP.put(giftData.getRid(), strConfig);
            return false;
        }
    }

    /**
     * 初始化奖池
     */
    public void initPoolSize(GiftData giftData) {
        ZipInfoVO zipInfoVO = JSON.parseObject(giftData.getZipInfo(), ZipInfoVO.class);
        if (zipInfoVO == null || CollectionUtils.isEmpty(zipInfoVO.getRandomGiftList())) {
            logger.error("can not find random gift config. giftId={}", giftData.getRid());
            return;
        }
        List<Integer> poolGiftIdList = new ArrayList<>();
        for (ZipInfoVO.RandomGift randomGift : zipInfoVO.getRandomGiftList()) {
            int awardSize = (int) (randomGift.getProb() * 0.01 * zipInfoVO.getRandomPoolSize());
            for (int i = 0; i < awardSize; i++) {
                poolGiftIdList.add(randomGift.getGiftId());
            }
        }
        Collections.shuffle(poolGiftIdList);
        randomGiftRedis.initPool(giftData.getRid(), poolGiftIdList);
        logger.info("initPoolSize={}", poolGiftIdList.size());
    }

    private static class GiftPoolConfig {
        /**
         * 盲盒奖池大小
         */
        private int randomPoolSize;
        /**
         * 盲盒中奖礼物配置
         */
        private List<ZipInfoVO.RandomGift> randomGiftList;

        public int getRandomPoolSize() {
            return randomPoolSize;
        }

        public void setRandomPoolSize(int randomPoolSize) {
            this.randomPoolSize = randomPoolSize;
        }

        public List<ZipInfoVO.RandomGift> getRandomGiftList() {
            return randomGiftList;
        }

        public void setRandomGiftList(List<ZipInfoVO.RandomGift> randomGiftList) {
            this.randomGiftList = randomGiftList;
        }

        @Override
        public String toString() {
            return "RandomGiftConfig{" +
                    "randomPoolSize=" + randomPoolSize +
                    ", randomGiftList=" + randomGiftList +
                    '}';
        }
    }
}
