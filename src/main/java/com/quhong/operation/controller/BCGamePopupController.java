package com.quhong.operation.controller;

import com.alibaba.fastjson.JSON;
import com.quhong.datas.HttpResult;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.BCGamePopupService;
import com.quhong.operation.share.condition.BCGamePopupCondition;
import com.quhong.operation.share.dto.BCGamePopupDTO;
import com.quhong.operation.share.vo.BCGamePopupVo;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.utils.OSSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * BC游戏数值弹窗控制器
 *
 * <AUTHOR>
 * @date 2025/9/12 10:06
 */
@RestController
@RequestMapping("/bc_game_popup")
public class BCGamePopupController extends BaseController {

    private final static Logger logger = LoggerFactory.getLogger(BCGamePopupController.class);

    @Resource
    private BCGamePopupService bcGamePopupService;
    private final static String FILE_PATH = "bc_popup";

    /**
     * 分页查询列表
     */
    @RequireRole
    @RequestMapping("/list")
    public HttpResult<PageResultVO<BCGamePopupVo>> selectPageList(@RequestBody BCGamePopupCondition condition) {
        logger.info("get bc game popup list condition={}", JSON.toJSONString(condition));
        return HttpResult.getOk(bcGamePopupService.selectPageList(condition));
    }

    /**
     * 新增或修改
     */
    @RequireRole
    @RequestMapping("/save")
    public HttpResult<Object> insertOrUpdate(@RequestBody BCGamePopupDTO dto, @RequestParam String uid) {
        logger.info("save bc game popup. dto={} uid={}", JSON.toJSONString(dto), uid);
        bcGamePopupService.insertOrUpdate(dto, uid);
        return HttpResult.getOk();
    }

    /**
     * 上架下架
     *
     * @param uid    操作者
     * @param id     弹窗id
     * @param status 状态，0，下架（无效），1，上架（有效）
     * @return
     */
    @RequireRole
    @RequestMapping("/online_offline")
    public HttpResult<Object> onlineOffline(@RequestParam String uid, @RequestParam Integer id, @RequestParam Integer status) {
        logger.info("onlineOffline.   id={},uid={},status={}", id, uid, status);
        bcGamePopupService.onlineOffline(uid, id, status);
        return HttpResult.getOk();
    }

    /**
     * 图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {
        return OSSUploadUtils.upload(file, FILE_PATH);
    }

}