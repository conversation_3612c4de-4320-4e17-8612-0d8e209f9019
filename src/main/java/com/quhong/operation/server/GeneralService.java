package com.quhong.operation.server;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mongodb.client.result.UpdateResult;
import com.quhong.constant.BannerSwitchConstant;
import com.quhong.constant.FcmMsgTypeConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.SendFcmDTO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IFcmService;
import com.quhong.feign.IMomentService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.room.RoomCommonScrollMsg;
import com.quhong.msg.room.UserCommonPopupMessage;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.UserRecommendNoticeDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.mongo.dao.FcmPushConfigDataDao;
import com.quhong.mysql.data.UserRecommendNoticeData;
import com.quhong.mysql.data.UserRecommendPopData;
import com.quhong.operation.dao.MomentOpDao;
import com.quhong.operation.dao.RealIncomeDao;
import com.quhong.operation.share.condition.PageCondition;
import com.quhong.operation.share.condition.ResourceCondition;
import com.quhong.operation.share.condition.ResourceKeyCondition;
import com.quhong.operation.share.condition.RoomOptCondition;
import com.quhong.operation.share.dto.MomentDTO;
import com.quhong.mongo.data.FcmPushConfigData;
import com.quhong.operation.share.dto.RealIncomeDTO;
import com.quhong.operation.share.dto.UserRecommendNoticeDTO;
import com.quhong.operation.share.vo.*;
import com.quhong.operation.utils.StringUtil;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.share.mongobean.Manager;
import com.quhong.redis.OperationConfigRedis;
import com.quhong.redis.OperationCommonRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.service.ResourceKeyHandlerService;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.PageUtils;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.PageVO;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class GeneralService implements ResourceService {
    private static final Logger logger = LoggerFactory.getLogger(GeneralService.class);
    private static final Pattern pattern = Pattern.compile("^\\d+(\\.\\d+)?$"); // 匹配正整数和正浮点数包含0

    @Resource
    private CommonConfig commonConfig;
    @Resource
    private MomentOpDao momentOpDao;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private ActorDao actorDao;
    @Resource
    private BadgeSourceService badgeSourceService;
    @Resource
    private MicFrameService micFrameService;
    @Resource
    private JoinSourceService joinSourceService;
    @Resource
    private GiftService giftService;
    @Resource
    private BubbleSourceService bubbleSourceService;
    @Resource
    private RippleSourceService rippleSourceService;
    @Resource
    private FloatScreenService floatScreenService;
    @Resource
    private RoomBackGroundService roomBackGroundService;
    @Resource
    private ResourceConfigService resourceConfigService;
    @Resource
    private RechargeCouponService rechargeCouponService;
    @Resource
    private GeneralService generalService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private GiftDao giftDao;
    @Resource
    private RocketRewardConfigDao rocketRewardConfigDao;
    @Resource
    private SysConfigDao sysConfigDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private IFcmService iFcmService;
    @Resource
    private RoomGuideConfigDao roomGuideConfigDao;
    @Resource
    private FcmPushConfigDataDao fcmPushConfigDataDao;
    @Resource
    private OperationConfigRedis operationConfigRedis;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private IMomentService iMomentService;
    @Resource
    private MongoThemeDao mongoThemeDao;
    @Resource
    private ManagerDao managerDao;
    @Resource
    private OperationCommonRedis operationCommonRedis;
    @Resource
    private RealIncomeDao realIncomeDao;
    @Resource
    private UserRecommendNoticeDao userRecommendNoticeDao;

    public PageResultVO<ItemConditionVO> itemList() {
        PageResultVO<ItemConditionVO> pageVO = new PageResultVO<>();

        List<ItemConditionVO> voList = new ArrayList<>();

        voList.add(new ItemConditionVO("默认相关", 0));
        voList.add(new ItemConditionVO("奖励相关", 1));
        voList.add(new ItemConditionVO("荣誉相关", 2));
        voList.add(new ItemConditionVO("Tycoon相关", 3));
        voList.add(new ItemConditionVO("用户等级相关", 4));
        voList.add(new ItemConditionVO("商店购买", 5));
        voList.add(new ItemConditionVO("签到相关", 6));
        voList.add(new ItemConditionVO("女王相关", 7));
        voList.add(new ItemConditionVO("新vip相关", 8));
        voList.add(new ItemConditionVO("每日任务相关", 9));

        pageVO.setList(voList);
        pageVO.setTotal(0);
        return pageVO;
    }


    public PageResultVO<BannerSwitchVO> bannerSwitch() {
        PageResultVO<BannerSwitchVO> pageVO = new PageResultVO<>();
        List<BannerSwitchVO> voList = new ArrayList<>();
        JSONObject jsonObject = commonConfig.getSwitchRealConfig();
        for (Map.Entry<String, String> entry : BannerSwitchConstant.BANNER_SWITCH_CN_MAP.entrySet()) {
            BannerSwitchVO vo = new BannerSwitchVO();
            vo.setSwitchName(entry.getKey());
            vo.setSwitchCNName(entry.getValue());
            vo.setSwitchStatus(jsonObject.getIntValue(entry.getKey()));
            voList.add(vo);

        }
        pageVO.setList(voList);
        return pageVO;
    }


    public void bannerSwitchSet(String uid, BannerSwitchVO dto) {

        JSONObject jsonObject = commonConfig.getSwitchRealConfig();
        jsonObject.put(dto.getSwitchName(), dto.getSwitchStatus());
        Update update = new Update();
        update.set("sconfig", jsonObject);
        commonConfig.updateSwitchConfigData(CommonConfig.SWITCH_CONFIG, update);
    }

    public MomentOpDao.MomentData getMomentInfo(String momentId) {
        return momentOpDao.findMomentOne(momentId);
    }

    public void roomScrollPush(String roomId) {
        String fromUid = "5cdf784961d047a4adf44064";

        RoomCommonScrollMsg msg = new RoomCommonScrollMsg();

        String activityNameEn = "Carnival of Star";
        String activityNameAr = "كرنفال النجوم";
        ActorData actorData = actorDao.getActorDataFromCache(fromUid);
        msg.setUid(fromUid);
        msg.setPrizeIcon("https://cdn3.qmovies.tv/gift/op_1715765331_03_icon.png");
        msg.setPrizeTextEn(String.format("Congratulations to %s for getting %s (worth 100000\uD83D\uDC8E) in Carnival of Star.", actorData.getName(), activityNameEn));
        msg.setPrizeTextAr(String.format("تهانينا لـ %s للحصول على %s (بقيمة 100000\uD83D\uDC8E) في كرنفال النجوم.", actorData.getName(), activityNameAr));

        List<HighlightTextObject> list = new ArrayList<>();
        HighlightTextObject object = new HighlightTextObject();
        object.setText(actorData.getName());
        object.setHighlightColor("#FFE200");
        list.add(object);
        object = new HighlightTextObject();
        object.setText(activityNameEn);
        object.setHighlightColor("#FFE200");
        list.add(object);
        object = new HighlightTextObject();
        object.setText(activityNameAr);
        object.setHighlightColor("#FFE200");
        list.add(object);
        msg.setHighlightTextEn(list);
        msg.setHighlightTextAr(list);
        msg.setActionType(19);
        msg.setActionValue("https://test2.qmovies.tv/clowns_magician2024/?activityId=6615f34f523b1acf8b6a7e08");
        roomWebSender.sendRoomWebMsg(roomId, fromUid, msg, false);

    }

    public void userCommonPopup(String userId, int actionType) {

        String activityNameEn = "Carnival of Star";
        String activityNameAr = "كرنفال النجوم";
        ActorData actorData = actorDao.getActorDataFromCache(userId);

        UserCommonPopupMessage userMsg = new UserCommonPopupMessage();
        userMsg.setUid(userId);
        userMsg.setIcon("https://cdn3.qmovies.tv/gift/op_1715765331_03_icon.png");
        userMsg.setTitleEn(activityNameEn);
        userMsg.setTitleAr(activityNameAr);
        userMsg.setTextEn(String.format("Congratulations to %s for getting %s (worth 100000\uD83D\uDC8E) in Carnival of Star.", actorData.getName(), activityNameEn));
        userMsg.setTextAr(String.format("تهانينا لـ %s للحصول على %s (بقيمة 100000\uD83D\uDC8E) في كرنفال النجوم.", actorData.getName(), activityNameAr));
        userMsg.setActionType(actionType);
        userMsg.setActionValue("https://test2.qmovies.tv/clowns_magician2024/?activityId=6615f34f523b1acf8b6a7e08");
        roomWebSender.sendPlayerWebMsg("", userId, userId, userMsg, false);
    }


    public PageResultVO<ResourceVO> getResourcePage(ResourceCondition condition) {
        ResourceService resourceService = null;
        switch (condition.getResourceType()) {
            case BaseDataResourcesConstant.TYPE_BADGE:
                resourceService = badgeSourceService;
                break;
            case BaseDataResourcesConstant.TYPE_MIC:
                resourceService = micFrameService;
                break;
            case BaseDataResourcesConstant.TYPE_RIDE:
                resourceService = joinSourceService;
                break;
            case BaseDataResourcesConstant.TYPE_BAG_GIFT:
                resourceService = giftService;
                break;
            case BaseDataResourcesConstant.TYPE_BUDDLE:
                resourceService = bubbleSourceService;
                break;
            case BaseDataResourcesConstant.TYPE_RIPPLE:
                resourceService = rippleSourceService;
                break;
            case BaseDataResourcesConstant.TYPE_FLOAT_SCREEN:
                resourceService = floatScreenService;
                break;
            case BaseDataResourcesConstant.TYPE_MINE_BACKGROUND:
                resourceService = roomBackGroundService;
                break;
            case BaseDataResourcesConstant.TYPE_RECHARGE_COUPON:
                resourceService = rechargeCouponService;
                break;
            case BaseDataResourcesConstant.TYPE_ENTRY_EFFECT:
            case BaseDataResourcesConstant.TYPE_HONOR_TITLE:
            case BaseDataResourcesConstant.TYPE_TICKET:
            case BaseDataResourcesConstant.TYPE_VIP_CARD:
                resourceService = resourceConfigService;
                break;
            case BaseDataResourcesConstant.TYPE_OTHER:
            case BaseDataResourcesConstant.TYPE_DIAMOND:
            case BaseDataResourcesConstant.TYPE_COIN:
            case BaseDataResourcesConstant.TYPE_TASK_CENTER_TICKET:
            case BaseDataResourcesConstant.TYPE_ROOM_LOCK:
                resourceService = generalService;
                break;
            default:
                break;
        }
        if (resourceService == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "资源不存在");
        }
        return resourceService.getGoodsList(condition);
    }

    @Override
    public PageResultVO<ResourceVO> getGoodsList(ResourceCondition condition) {

        PageResultVO<ResourceVO> pageVO = new PageResultVO<>();
        List<ResourceVO> voList = new ArrayList<>();
        switch (condition.getResourceType()) {
            case BaseDataResourcesConstant.TYPE_OTHER:
                if (!StringUtils.isEmpty(condition.getQuerySource()) && "carnival".equals(condition.getQuerySource())) {
                    voList.add(new ResourceVO("Gift Pack", "هدية", "https://cdn3.qmovies.tv/gift/op_1694762871_lovebox.png", 0, condition.getResourceType(), 0));
                } else {
                    voList.add(new ResourceVO("Others", "مشاكل أخرى", "https://cdn3.qmovies.tv/youstar/op_1715268656_banner.png", 0, condition.getResourceType(), 0));
                }
                pageVO.setList(voList);
                pageVO.setTotal(voList.size());
                return pageVO;
            case BaseDataResourcesConstant.TYPE_DIAMOND:
                voList.add(new ResourceVO("Diamonds", "الماس", "https://cdn3.qmovies.tv/youstar/diamonds.png", 0, condition.getResourceType(), 0));
                voList.add(new ResourceVO("Diamonds", "الماس", "https://cdn3.qmovies.tv/game/op_1745396594_2.png", 0, condition.getResourceType(), 0));
                voList.add(new ResourceVO("Diamonds", "الماس", "https://cdn3.qmovies.tv/game/op_1745396587_3.png", 0, condition.getResourceType(), 0));
                voList.add(new ResourceVO("Diamonds", "الماس", "https://cdn3.qmovies.tv/game/op_1745396608_4.png", 0, condition.getResourceType(), 0));
                voList.add(new ResourceVO("Diamonds", "الماس", "https://cdn3.qmovies.tv/youstar/op_1696746385_50.png", 0, condition.getResourceType(), 0));
                voList.add(new ResourceVO("Diamonds", "الماس", "https://cdn3.qmovies.tv/game/op_1745396618_5.png", 0, condition.getResourceType(), 0));
                voList.add(new ResourceVO("Diamonds", "الماس", "https://cdn3.qmovies.tv/youstar/op_1696746385_500.png", 0, condition.getResourceType(), 0));
                pageVO.setList(voList);
                pageVO.setTotal(voList.size());
                return pageVO;
            case BaseDataResourcesConstant.TYPE_COIN:
                voList.add(new ResourceVO("Coins", "كوينزات", "https://cdn3.qmovies.tv/youstar/op_sys_1658145686_coins.png", 0, condition.getResourceType(), 0));
                pageVO.setList(voList);
                pageVO.setTotal(voList.size());
                return pageVO;
            case BaseDataResourcesConstant.TYPE_TASK_CENTER_TICKET:
                voList.add(new ResourceVO("Ticket", "تذكرة", "https://cdn3.qmovies.tv/youstar/op_1695610269_lotteryTicket.png", 0, condition.getResourceType(), 0));
                pageVO.setList(voList);
                pageVO.setTotal(voList.size());
                return pageVO;
            case BaseDataResourcesConstant.TYPE_ROOM_LOCK:
                voList.add(new ResourceVO("Room Lock", "قفل الغرفة", "https://cdn3.qmovies.tv/youstar/op_1749542723_roomLock3x.png", 0, condition.getResourceType(), 0));
                pageVO.setList(voList);
                pageVO.setTotal(voList.size());
                return pageVO;
            default:
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "资源不存在");
        }
    }


    public PageResultVO<ResourceKeyVO> resourceKeyPage(ResourceKeyCondition condition) {
        PageResultVO<ResourceKeyVO> pageVO = new PageResultVO<>();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;

        List<ResourceKeyConfigData> resourceKeyConfigList = resourceKeyConfigDao.selectPage(condition.getKeyType(), search, start, pageSize);
        List<ResourceKeyVO> voList = new ArrayList<>();
        for (ResourceKeyConfigData data : resourceKeyConfigList) {
            ResourceKeyVO vo = new ResourceKeyVO();
            BeanUtils.copyProperties(data, vo);
            vo.setResourceMetaList(data.getResourceMetaList().stream().filter(item -> item.getStatus() > 0).collect(Collectors.toList()));
            vo.setDocId(data.get_id().toString());
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(resourceKeyConfigDao.selectCount(condition.getKeyType(), search));
        return pageVO;
    }


    private void resourceParamCheck(ResourceKeyVO dto, boolean insertEntry) {

        int keyType = dto.getKeyType();
        List<ResourceKeyConfigData.ResourceMeta> resourceMetaList = dto.getResourceMetaList();
        if (resourceMetaList == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "奖励/中奖列表不能为空");
        }
        String key = dto.getKey();
        if (StringUtil.isContainChinese(key)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "资源key不能设置中文");
        }

        for (ResourceKeyConfigData.ResourceMeta resourceMeta : resourceMetaList) {
            int resourceType = resourceMeta.getResourceType();
            int resourceId = resourceMeta.getResourceId();
            int resourceTime = resourceMeta.getResourceTime();
            String resourceIcon = resourceMeta.getResourceIcon();

            if (StringUtils.isEmpty(resourceMeta.getMetaId()) || insertEntry) {
                resourceMeta.setMetaId(new ObjectId().toString());
            }

            if (keyType > 0 && StringUtils.isEmpty(resourceMeta.getRateNumber())) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "概率未配置");
            }

            if (keyType > 0 && !pattern.matcher(resourceMeta.getRateNumber()).matches()) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "概率只能是整数或浮点数");
            }

            if (resourceType == -1) {   // 其他资源
                continue;
            }

            if (resourceType == BaseDataResourcesConstant.TYPE_MINE_BACKGROUND) {
                MongoThemeData mongoThemeData = mongoThemeDao.findData(resourceId);
                if (mongoThemeData == null) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "背景资源设置有误");
                }
                if (mongoThemeData.getType() == 0 || mongoThemeData.getType() == 1) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "默认背景或VIP背景资源不能设置");
                }
            }

            if (resourceType == BaseDataResourcesConstant.TYPE_DIAMOND || resourceType == BaseDataResourcesConstant.TYPE_COIN || resourceType == BaseDataResourcesConstant.TYPE_TASK_CENTER_TICKET) {
                if (resourceMeta.getResourceNumber() <= 0) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "该资源类型【下发数量】不能小于等于0");
                }
            } else if (resourceType == BaseDataResourcesConstant.TYPE_ROOM_LOCK) {
                if (resourceMeta.getResourceTime() <= 0) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "未设置资源下发时长");
                }
            } else {
                if (resourceId <= 0) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "未设置资源id");
                }

                if (resourceType != BaseDataResourcesConstant.TYPE_BADGE && resourceTime <= 0) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "未设置资源下发时长");
                }

                if (resourceType == BaseDataResourcesConstant.TYPE_BADGE && resourceTime < 0) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "未设置资源下发时长");
                }

                // if (resourceType == BaseDataResourcesConstant.TYPE_BAG_GIFT && ServerConfig.isProduct()) {
                //     GiftData extraGift = giftDao.getGiftFromDb(resourceId);
                //     if (extraGift == null || extraGift.getBagGift() <= 0) {
                //         throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "所选择的礼物不是背包礼物，请先将该礼物添加到背包礼物列表【资源管理->礼物】");
                //     }
                // }
            }
        }
    }

    public void addResourceKey(ResourceKeyVO dto) {

        if (StringUtils.isEmpty(dto.getKey()) || StringUtils.isEmpty(dto.getName())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "key或名称未设置");
        }

        dto.setKey(dto.getKey().trim());
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.selectByKey(dto.getKey());
        if (resourceKeyConfigData != null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "设置的key已存在, 请修改");
        }
        resourceParamCheck(dto, true);
        ResourceKeyConfigData data = new ResourceKeyConfigData();
        BeanUtils.copyProperties(dto, data);
        resourceKeyConfigDao.insert(data);
    }

    /**
     * 修改
     */
    public void updateResourceKey(ResourceKeyVO dto) {

        if (StringUtils.isEmpty(dto.getKey()) || StringUtils.isEmpty(dto.getName())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "key或名称未设置");
        }
        dto.setKey(dto.getKey().trim());
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findById(dto.getDocId());
        if (resourceKeyConfigData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "设置的key不存在");
        }
        ResourceKeyConfigData keyConfigData = resourceKeyConfigDao.selectByKey(dto.getKey());
        if (keyConfigData != null && !keyConfigData.get_id().toString().equals(resourceKeyConfigData.get_id().toString())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "设置的key已存在, 请修改");
        }
        resourceParamCheck(dto, false);

        Update update = new Update();
        update.set("key", dto.getKey());
        update.set("name", dto.getName());
        update.set("keyType", dto.getKeyType());
        update.set("bestResourceMetaId", dto.getBestResourceMetaId());
        update.set("resourceMetaList", dto.getResourceMetaList());
        resourceKeyConfigDao.updateData(dto.getDocId(), update);
    }

    public void sendResourceKey(ResourceKeyVO dto) {
        if (!StringUtils.hasLength(dto.getStrRid()) || !StringUtils.hasLength(dto.getRewardTitle())) {
            throw new CommonH5Exception(new HttpCode(1, "用户id或奖励标题为空"));
        }
        String[] rids = dto.getStrRid().trim().replace("，", ",").split(",");
        StringBuilder errorMsgSb = new StringBuilder();
        List<String> aidList = new ArrayList<>();
        for (String strRid : rids) {
            try {
                ActorData actorData = actorDao.getActorByStrRid(strRid);
                if (null == actorData) {
                    errorMsgSb.append("rid:").append(strRid).append("找不到对应的用户 \n");
                    continue;
                }
                aidList.add(actorData.getUid());

            } catch (Exception e) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "账号输入有误");
            }
        }
        String errorMsg = errorMsgSb.toString();
        if (StringUtils.hasLength(errorMsg)) {
            throw new CommonH5Exception(new HttpCode(1, errorMsg));
        }

        String rewardTitle = dto.getRewardTitle();
        for (String aid : aidList) {
            resourceKeyHandlerService.sendResourceData(aid, dto.getKey(), rewardTitle, rewardTitle, rewardTitle, "", "");
        }
    }


    public void addRocketRewardConfigV2(RocketConfigV2VO dto) {
        RocketRewardConfigData rocketRewardConfigDataL = rocketRewardConfigDao.findV2Data(dto.getRocketLevel());
        if (rocketRewardConfigDataL != null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "设置的火箭等级已存在, 请修改");
        }
        rocketParamCheck(dto);
        RocketRewardConfigData data = new RocketRewardConfigData();
        BeanUtils.copyProperties(dto, data);
        data.setRocketVer(2);
        rocketRewardConfigDao.insert(data);
    }


    public void updateRocketRewardConfigV2(RocketConfigV2VO dto) {
        if (StringUtils.isEmpty(dto.getDocId())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "docId 不能为空");
        }
        RocketRewardConfigData rocketRewardConfigData = rocketRewardConfigDao.findById(dto.getDocId());
        if (rocketRewardConfigData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "当前火箭配置不存在");
        }
        RocketRewardConfigData rocketRewardConfigDataL = rocketRewardConfigDao.findV2Data(dto.getRocketLevel());
        if (rocketRewardConfigDataL != null && !rocketRewardConfigDataL.get_id().toString().equals(rocketRewardConfigData.get_id().toString())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "rocketLevel与docId不匹配，请修改");
        }
        rocketParamCheck(dto);
        Update update = new Update();
        update.set("rocketVer", 2);
        update.set("rocketLaunchLimit", dto.getRocketLaunchLimit());
        update.set("allRoomBroadcast", dto.getAllRoomBroadcast());
        update.set("superMaxCount", dto.getSuperMaxCount());
        update.set("roomTop3MetaList", dto.getRoomTop3MetaList());
        update.set("roomOwnerMetaList", dto.getRoomOwnerMetaList());
        update.set("superMetaList", dto.getSuperMetaList());
        rocketRewardConfigDao.updateData(dto.getDocId(), update);
    }

    public RocketV2PageResultVO<RocketConfigV2VO> rocketRewardConfigListV2(ResourceKeyCondition condition) {
        RocketV2PageResultVO<RocketConfigV2VO> pageVO = new RocketV2PageResultVO<>();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;
        List<RocketRewardConfigData> rocketRewardConfigList = rocketRewardConfigDao.findV2All();
        List<RocketConfigV2VO> voList = new ArrayList<>();
        for (RocketRewardConfigData data : rocketRewardConfigList) {
            RocketConfigV2VO vo = new RocketConfigV2VO();
            BeanUtils.copyProperties(data, vo);
            vo.setRoomOwnerMetaList(toMetaList(data.getRoomOwnerMetaList()));
            vo.setRoomTop3MetaList(toMetaList(data.getRoomTop3MetaList()));
            vo.setSuperMetaList(toMetaList(data.getSuperMetaList()));
            vo.setDocId(data.get_id().toString());
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(rocketRewardConfigList.size());
        pageVO.setRocketSwitch(sysConfigDao.getIntValue(SysConfigDao.ROCKET_CONFIG,
                SysConfigDao.ROOM_ROCKET_SWITCH_V2_KEY, false));
        return pageVO;
    }

    public RocketV2PageResultVO<Object> updateRocketSwitchV2(RocketV2PageResultVO<Object> dto) {
        RocketV2PageResultVO<Object> pageVO = new RocketV2PageResultVO<>();
        logger.info("updateRocketSwitchV2 status={}", dto.getRocketSwitch());
        UpdateResult updateResult = sysConfigDao.updateMapValue(SysConfigDao.ROCKET_CONFIG,
                SysConfigDao.ROOM_ROCKET_SWITCH_V2_KEY, dto.getRocketSwitch());
        if (null == updateResult || updateResult.getModifiedCount() == 0) {
            String msg = dto.getRocketSwitch() == 1 ? "房间火箭V2活动开启失败" : "房间火箭V2活动关闭失败";
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), msg);
        } else {
            pageVO.setRocketSwitch(dto.getRocketSwitch());
        }

        return pageVO;
    }


    private List<RocketConfigV2VO.ResourceMeta> toMetaList(List<RocketRewardConfigData.ResourceMeta> srcList) {
        List<RocketRewardConfigData.ResourceMeta> destList = srcList.stream().filter(item -> item.getStatus() > 0)
                .collect(Collectors.toList());
        List<RocketConfigV2VO.ResourceMeta> metaList = new ArrayList<>();
        destList.forEach(item -> {
            RocketConfigV2VO.ResourceMeta mete = new RocketConfigV2VO.ResourceMeta();
            BeanUtils.copyProperties(item, mete);
            metaList.add(mete);
        });
        return metaList;
    }


    private void rocketParamCheck(RocketConfigV2VO dto) {
        if (dto.getRocketLevel() <= 0 || dto.getRocketLaunchLimit() <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "火箭等级或者火箭发射限制值必须大于0");
        }
        if (dto.getSuperMaxCount() == null || dto.getSuperMaxCount() <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "公共奖励资源最大中取人数必须大于0");
        }

        List<RocketRewardConfigData.ResourceMeta> superMetaList = dto.getSuperMetaList();
        if (superMetaList == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "公共奖池列表不能为空");
        }
        checkResourceMetaList(superMetaList, 1);

        List<RocketRewardConfigData.ResourceMeta> roomTop3MetaList = dto.getRoomTop3MetaList();
        if (roomTop3MetaList == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "top3奖池列表不能为空");
        }
        checkResourceMetaList(superMetaList, 0);

        List<RocketRewardConfigData.ResourceMeta> roomOwnerMetaList = dto.getRoomOwnerMetaList();
        if (roomOwnerMetaList == null) {
            roomOwnerMetaList = new ArrayList<>();
            dto.setRoomOwnerMetaList(roomOwnerMetaList);
        } else {
            checkResourceMetaList(roomOwnerMetaList, 0);
        }
    }


    private void checkResourceMetaList(List<RocketRewardConfigData.ResourceMeta> resourceMetaList, int keyType) {
        for (RocketRewardConfigData.ResourceMeta resourceMeta : resourceMetaList) {
            int resourceType = resourceMeta.getResourceType();
            int resourceId = resourceMeta.getResourceId();
            int resourceTime = resourceMeta.getResourceTime();

            if (StringUtils.isEmpty(resourceMeta.getMetaId())) {
                resourceMeta.setMetaId(new ObjectId().toString());
            }

            if (keyType > 0 && StringUtils.isEmpty(resourceMeta.getRateNumber())) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "概率未配置");
            }

            if (keyType > 0 && !pattern.matcher(resourceMeta.getRateNumber()).matches()) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "概率只能是整数或浮点数");
            }

            if (resourceType == -1) {   // 其他资源
                continue;
            }

            if (resourceType == -2 || resourceType == -3) {
                if (resourceMeta.getResourceNumber() <= 0) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "钻石资源/金币资源【数量】不能小于等于0");
                }
            } else {
                if (resourceId <= 0) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "未设置资源id");
                }

                if (resourceType != BaseDataResourcesConstant.TYPE_BADGE && resourceTime <= 0) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "未设置资源下发时长");
                }

                if (resourceType == BaseDataResourcesConstant.TYPE_BADGE && resourceTime < 0) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "未设置资源下发时长");
                }

                if (resourceType == BaseDataResourcesConstant.TYPE_BAG_GIFT) {
                    GiftData extraGift = giftDao.getGiftFromDb(resourceId);
                    if (extraGift == null || extraGift.getBagGift() <= 0) {
                        throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "所选择的礼物不是背包礼物，请先将该礼物添加到背包礼物列表【资源管理->礼物】");
                    }
                }
            }
        }
    }


    public void fcmMessagePush(String userId, int stepType, String actionValue) {
        ActorData actorData = actorDao.getActorByStrRid(userId);
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "用户不存在");
        }

        String roomId = ServerConfig.isProduct() ? "r:5cdf784961d047a4adf44064" : "r:655c4c24b661b86b85455f3b";
        MongoRoomData roomData = mongoRoomDao.getDataFromCache(roomId);
        Map<String, String> paramMap = new HashMap<>();
        JSONObject jsonObject = new JSONObject();

        if (stepType > 0) {
            paramMap.put(FcmMsgTypeConstant.STEP_ACTION_TYPE_KEY, stepType + "");
            if (StringUtils.hasLength(actionValue)) {
                jsonObject.put(FcmMsgTypeConstant.ACTION_VALUE_KEY, actionValue);
                if (FcmMsgTypeConstant.PRIVATE_DETAIL_MSG.equals(stepType + "")) {
                    ActorData toActorData = actorDao.getActorData(actionValue);
                    if (toActorData == null) {
                        throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "指定私聊对象用户不存在");
                    }
                    jsonObject.put(FcmMsgTypeConstant.ACTION_HEAD_KEY, ImageUrlGenerator.generateRoomUserUrl(toActorData.getHead()));
                }
            }
            paramMap.put(FcmMsgTypeConstant.ACTION_CONFIG_KEY, JSONObject.toJSONString(jsonObject));
        } else {
            jsonObject.put("roomId", roomId);
            paramMap.put("t", "8");
            paramMap.put("custom", JSONObject.toJSONString(jsonObject));

            JSONObject jsonObject2 = new JSONObject();
            paramMap.put(FcmMsgTypeConstant.STEP_ACTION_TYPE_KEY, FcmMsgTypeConstant.VOICE_ROOM);
            jsonObject2.put(FcmMsgTypeConstant.ACTION_VALUE_KEY, roomId);
            paramMap.put(FcmMsgTypeConstant.ACTION_CONFIG_KEY, JSONObject.toJSONString(jsonObject2));
        }

        SendFcmDTO sendFcmDTO = new SendFcmDTO();
        sendFcmDTO.setToUid(actorData.getUid());
        sendFcmDTO.setTitle(roomData.getName());
        sendFcmDTO.setTitleAr(roomData.getName());
        sendFcmDTO.setParamMap(paramMap);
        sendFcmDTO.setBody("EN您关注的这个房间正在聊有趣的话题，have a look!");
        sendFcmDTO.setBodyAr("AR您关注的这个房间正在聊有趣的话题，have a look!");
        sendFcmDTO.setImg(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
        iFcmService.sendFcmMsg(sendFcmDTO);
    }


    public PageResultVO<RecommendGuidConfigVO> recommendGuidConfigList(ResourceKeyCondition condition) {
        PageResultVO<RecommendGuidConfigVO> pageVO = new RocketV2PageResultVO<>();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;
        int roomType = condition.getRoomType();
        List<RoomGuideConfigData> configList = roomGuideConfigDao.findListByRoomType(roomType, 0, 1000);
        List<RecommendGuidConfigVO> voList = new ArrayList<>();
        for (RoomGuideConfigData data : configList) {
            RecommendGuidConfigVO vo = new RecommendGuidConfigVO();
            BeanUtils.copyProperties(data, vo);
            vo.setDocId(data.get_id().toString());
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(configList.size());
        return pageVO;
    }

    public void addRecommendGuidConfig(RecommendGuidConfigVO dto) {
        recommendGuidParamCheck(dto);
        RoomGuideConfigData data = new RoomGuideConfigData();
        BeanUtils.copyProperties(dto, data);
        int now = (int) (System.currentTimeMillis() / 1000);
        data.setCtime(now);
        data.setMtime(now);
        roomGuideConfigDao.insert(data);
    }

    public void updateRecommendGuidConfig(RecommendGuidConfigVO dto) {
        if (StringUtils.isEmpty(dto.getDocId())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "docId 不能为空");
        }
        RoomGuideConfigData configData = roomGuideConfigDao.getDataByID(dto.getDocId());
        if (configData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "当前引导弹窗配置不存在");
        }
        recommendGuidParamCheck(dto);
        Update update = new Update();
        update.set("userType", dto.getUserType());
        update.set("scene", dto.getScene());
        update.set("homeKeepTime", dto.getHomeKeepTime());
        update.set("voiceRoomKeepTime", dto.getVoiceRoomKeepTime());
        update.set("enterRoomCount", dto.getEnterRoomCount());
        update.set("oneDayCount", dto.getOneDayCount());
        update.set("totalCountDay", dto.getTotalCountDay());
        update.set("recommendGameTypeList", dto.getRecommendGameTypeList());
        update.set("recommendRoomType", dto.getRecommendRoomType());
        update.set("status", dto.getStatus());
        update.set("area", dto.getArea());
        update.set("gender", dto.getGender());
//        update.set("roomType", dto.getRoomType());
//        update.set("ctime", dto.getCtime());
        update.set("mtime", (int) (System.currentTimeMillis() / 1000));
        roomGuideConfigDao.updateData(dto.getDocId(), update);
    }

    public void removeRecommendGuidConfig(RecommendGuidConfigVO dto) {
        if (StringUtils.isEmpty(dto.getDocId())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "docId 不能为空");
        }
        RoomGuideConfigData configData = roomGuideConfigDao.getDataByID(dto.getDocId());
        if (configData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "当前引导弹窗配置不存在");
        }
        roomGuideConfigDao.removeByDocId(dto.getDocId());
    }

    private void recommendGuidParamCheck(RecommendGuidConfigVO dto) {
        if (dto.getUserType() <= 0 || dto.getScene() <= 0 || dto.getOneDayCount() <= 0
                || dto.getTotalCountDay() <= 0 || dto.getRoomType() <= 0 || dto.getStatus() < 0
                || dto.getArea() <= 0 || dto.getGender() <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "参数必须大于0");
        }
        if (dto.getHomeKeepTime() <= 0 && dto.getVoiceRoomKeepTime() <= 0 && dto.getEnterRoomCount() <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "3个可选参数必须有一个大于0");
        }
        if (dto.getRoomType() == 1 && dto.getRecommendRoomType() <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "推荐语音房必须选择推荐房间");
        }
//        if (dto.getRoomType() == 2 && !CollectionUtils.isEmpty(dto.getRecommendGameTypeList())) {
//            for (Integer gameType : dto.getRecommendGameTypeList()) {
//            }
//            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "推荐游戏房，推荐的游戏不存在");
//        }

    }


    public PageResultVO<FcmPushConfigDataVO> fcmPushConfigList(ResourceKeyCondition condition) {
        PageResultVO<FcmPushConfigDataVO> pageVO = new PageResultVO<>();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;
        int status = condition.getStatus();
        List<FcmPushConfigData> configList = fcmPushConfigDataDao.selectPage(status, start, pageSize);
        List<FcmPushConfigDataVO> voList = new ArrayList<>();
        for (FcmPushConfigData data : configList) {
            FcmPushConfigDataVO vo = new FcmPushConfigDataVO();
            BeanUtils.copyProperties(data, vo);
            vo.setDocId(data.get_id().toString());
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(configList.size());
        return pageVO;
    }

    public void addFcmPushConfig(FcmPushConfigDataVO dto) {
        fcmPushConfigParamCheck(dto);
        FcmPushConfigData data = new FcmPushConfigData();
        BeanUtils.copyProperties(dto, data);
        int now = DateHelper.getNowSeconds();
        data.setCtime(now);
        data.setMtime(now);
        fcmPushConfigDataDao.insert(data);
    }

    public void updateFcmPushConfig(FcmPushConfigDataVO dto) {
        if (StringUtils.isEmpty(dto.getDocId())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "docId 不能为空");
        }
        FcmPushConfigData configData = fcmPushConfigDataDao.getDataByID(dto.getDocId());
        if (configData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "当前引导弹窗配置不存在");
        }
        fcmPushConfigParamCheck(dto);

        Update update = new Update();
        update.set("userType", dto.getUserType());
        update.set("ridList", dto.getRidList());
        update.set("pushStartDate", dto.getPushStartDate());
        update.set("pushEndDate", dto.getPushEndDate());
        update.set("pushEndDate", dto.getPushEndDate());
        update.set("pushHMS", dto.getPushHMS());
        update.set("actionType", dto.getActionType());
        update.set("actionValue", dto.getActionValue());
        update.set("title", dto.getTitle());
        update.set("content", dto.getContent());
        update.set("imgUrl", dto.getImgUrl());
        update.set("notes", dto.getStatus());
        update.set("mtime", DateHelper.getNowSeconds());
        fcmPushConfigDataDao.updateData(dto.getDocId(), update);
    }

    public void removeFcmPushConfig(FcmPushConfigDataVO dto) {
        if (StringUtils.isEmpty(dto.getDocId())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "docId 不能为空");
        }
        FcmPushConfigData configData = fcmPushConfigDataDao.getDataByID(dto.getDocId());
        if (configData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "当前配置不存在");
        }
        fcmPushConfigDataDao.removeByDocId(dto.getDocId());
    }

    private void fcmPushConfigParamCheck(FcmPushConfigDataVO dto) {
        if (dto.getUserType() <= 0 || StringUtils.isEmpty(dto.getPushStartDate()) || StringUtils.isEmpty(dto.getPushEndDate())
                || dto.getActionType() <= 0 || StringUtils.isEmpty(dto.getTitle())
                || StringUtils.isEmpty(dto.getContent())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "参数必须大于0,或者title，content不能为空");
        }
        if (dto.getUserType() == 11 && StringUtils.isEmpty(dto.getRidList())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "指定批量RID，ridList不能为空");
        }
        if (dto.getActionType() >= 8 && dto.getActionType() <= 15 && dto.getActionType() != 13
                && StringUtils.isEmpty(dto.getActionValue())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "该跳转类型，必须有actionValue");
        }

    }

    public NewRoomConfigVO listNewRoomConfig() {
        return operationConfigRedis.getAllNewRoomScoreWeight().toJavaObject(NewRoomConfigVO.class);
    }

    public void updateNewRoomConfig(NewRoomConfigVO dto) {
        operationConfigRedis.saveAllNewRoomScoreWeight(JSON.toJSONString(dto));
    }

    public PageResultVO<HighQualityRoomVO> highQualityRoomList(PageCondition condition) {
        return toPageVO(condition, operationConfigRedis.highQualityRoomList());
    }

    private PageResultVO<HighQualityRoomVO> toPageVO(PageCondition condition, Set<String> roomList) {
        List<HighQualityRoomVO> highQualityRoomList = new ArrayList<>();
        for (String roomId : roomList) {
            HighQualityRoomVO vo = new HighQualityRoomVO();
            vo.setRoomId(roomId);
            ActorData hostActor = actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(roomId));
            vo.setOwnerRid(String.valueOf(hostActor.getRid()));
            MongoRoomData mongoRoomData = mongoRoomDao.findData(roomId);
            vo.setOwnerName(null == mongoRoomData ? "unknown" : mongoRoomData.getName());
            vo.setOwnerCountry(hostActor.getCountry());
            Set<String> inRoomUserSet = roomPlayerRedis.getRoomActors(roomId);
            int onlineNewCount = (int) inRoomUserSet.stream().filter(item -> ActorUtils.isNewRegisterActor(item, 7)).count();
            vo.setOnline(String.valueOf(inRoomUserSet.size()));
            vo.setOnlineNew(String.valueOf(onlineNewCount));
            highQualityRoomList.add(vo);
        }
        highQualityRoomList.sort(Comparator.comparing(HighQualityRoomVO::getOnline).reversed());
        PageResultVO<HighQualityRoomVO> pageVO = new PageResultVO<>();
        PageUtils.PageData<HighQualityRoomVO> pageData = PageUtils.getPageData(highQualityRoomList, condition.getPage(), condition.getPageSize());
        pageVO.setList(pageData.list);
        pageVO.setTotal(pageData.totalSize);
        return pageVO;
    }

    public void highQualityRoomOpt(RoomOptCondition dto) {
        if (dto.getOptType() == 1) {
            Set<String> uidSet = getAllRoomIdByRids(dto);
            Long ret = operationConfigRedis.addAllQualityRoomByRedis(uidSet);
            logger.info("uidSet size:{} ret:{}", uidSet.size(), ret);
        } else if (dto.getOptType() == 2) {
            if (ObjectUtils.isEmpty(dto.getRoomId())) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "roomId为空");
            }
            operationConfigRedis.removeQualityRoomList(dto.getRoomId());
        }
    }

    public PageResultVO<HighQualityRoomVO> bigRRoomList(PageCondition condition) {
        return toPageVO(condition, operationConfigRedis.bigRRoomList());
    }

    public void bigRRoomListOpt(RoomOptCondition dto) {
        if (dto.getOptType() == 1) {
            Set<String> uidSet = getAllRoomIdByRids(dto);
            Long ret = operationConfigRedis.addAllBigRRoomByRedis(uidSet);
            logger.info("uidSet size:{} ret:{}", uidSet.size(), ret);
        } else if (dto.getOptType() == 2) {
            if (ObjectUtils.isEmpty(dto.getRoomId())) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "roomId为空");
            }
            operationConfigRedis.removeBigRRoomList(dto.getRoomId());
        }
    }

    private Set<String> getAllRoomIdByRids(RoomOptCondition dto) {
        if (!StringUtils.hasLength(dto.getOwnerRid())) {
            throw new CommonH5Exception(new HttpCode(1, "ownerRid 不能为空"));
        }

        Set<String> ridSet;
        if (StringUtils.hasLength(dto.getOwnerRid())) {
            String[] rids = dto.getOwnerRid().trim().replace("，", ",").split(",");
            int ridsLength = rids.length;
            ridSet = new HashSet<>(Arrays.asList(rids));
            logger.info("rids.length:{} ridSet.size():{} ", ridsLength, ridSet.size());
        } else {
            ridSet = new HashSet<>();
        }

        StringBuilder errorMsgSb = new StringBuilder();
        Set<String> uidSet = new HashSet<>();

        for (String strRid : ridSet) {
            try {
                ActorData actorData = actorDao.getActorByStrRid(strRid);
                if (null == actorData) {
                    errorMsgSb.append("rid:").append(strRid).append("找不到对应的用户,请更改后重新验证 \n");
                    continue;
                }
                uidSet.add(actorData.getUid());
            } catch (Exception e) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "账号输入有误");
            }
        }

        String errorMsg = errorMsgSb.toString();
        if (StringUtils.hasLength(errorMsg)) {
            throw new CommonH5Exception(new HttpCode(1, errorMsg));
        }

        uidSet = uidSet.stream()
                .map(s -> "r:" + s)
                .collect(Collectors.toSet());
        return uidSet;
    }

    public PageVO<MomentVO.MomentDetailVO> getMomentList(MomentDTO.MomentListDTO dto) {
        String aid = null;
        if (null != dto.getRid()) {
            ActorData actorByRid = actorDao.getActorByRid(dto.getRid());
            if (null != actorByRid) {
                aid = actorByRid.getUid();
            }
        }
        Integer startTime = null;
        Integer endTime = null;
        if (StringUtils.hasLength(dto.getStartDate())) {
            startTime = DateHelper.ARABIAN.stringDateToStampSecond(dto.getStartDate());
        }
        if (StringUtils.hasLength(dto.getEndDate())) {
            endTime = DateHelper.ARABIAN.stringDateToStampSecond(dto.getEndDate()) + 86400;
        }
        int pageSize = 12;
        List<String> topMoment = momentOpDao.getTopMoment();
        List<MomentOpDao.MomentData> momentList = momentOpDao.getPublicMomentList(aid, startTime, endTime, dto.getPage(), pageSize);
        PageVO<MomentVO.MomentDetailVO> vo = new PageVO<>(new ArrayList<>(pageSize));
        for (MomentOpDao.MomentData moment : momentList) {
            vo.getList().add(toMomentDetailVO(moment, topMoment));
        }
        vo.setNextUrl(dto.getPage(), pageSize);
        return vo;
    }

    private MomentVO.MomentDetailVO toMomentDetailVO(MomentOpDao.MomentData moment, List<String> topMoment) {
        MomentVO.MomentDetailVO detail = new MomentVO.MomentDetailVO();
        BeanUtils.copyProperties(moment, detail);
        detail.setMid(moment.get_id().toString());
        detail.setIs_top(topMoment.contains(detail.getMid()) ? 1 : 0);
        ActorData actorData = actorDao.getActorDataFromCache(moment.getUid());
        if (null == actorData) {
            return null;
        }
        detail.setName(actorData.getName());
        detail.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        detail.setRid(actorData.getStrRid());
        if (moment.getComments() > 0) {
            fillCommentList(moment, detail);
        }
        return detail;
    }

    private void fillCommentList(MomentOpDao.MomentData moment, MomentVO.MomentDetailVO detail) {
        List<MomentOpDao.Comment> commentDetails = momentOpDao.getComments(moment.get_id().toString());
        for (MomentOpDao.Comment commentDetail : commentDetails) {
            MomentVO.CommentDetailVO commentDetailVO = new MomentVO.CommentDetailVO();
            BeanUtils.copyProperties(commentDetail, commentDetailVO);
            detail.getCommentList().add(commentDetailVO);
            commentDetailVO.setCid(commentDetail.get_id().toString());
            ActorData actorData = actorDao.getActorDataFromCache(commentDetail.getCommentator());
            if (null == actorData) {
                continue;
            }
            commentDetailVO.setName(actorData.getName());
            commentDetailVO.setRid(actorData.getStrRid());
            commentDetailVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        }
    }

    public void pinMoment(String opUid, String mid, Integer opType, Integer endTime) {
        MomentOpDao.MomentData momentData = momentOpDao.findMomentOne(mid);
        if (null == momentData) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        ApiResult<HttpCode> apiResult = iMomentService.pinMoment(mid, opType, endTime);
        if (apiResult.isError()) {
            throw new CommonH5Exception(apiResult.getCode());
        }
    }

    private static final String HUAWEI_CHECK_SWITCH_KEY = "huawei_check_switch";
    private static final String HUAWEI_CHECK_VERSION_KEY = "huawei_check_version";
    private static final String HUAWEI_SWITCH_LOG_KEY = "huawei_switch_log";
    private static final int MAX_LOG_SIZE = 30;

    /**
     * 设置华为检查开关
     */
    public void huaweiCheckSwitchSet(String uid, HuaWeiCheckVO dto) {
        logger.info("huaweiCheckSwitchSet uid={}, dto={}", uid, JSONObject.toJSONString(dto));
        if (dto.getHuaweiCheckSwitch() < 0 || dto.getHuaweiCheckSwitch() > 1) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "huaweiCheckSwitch值不合法");
        }
        if (dto.getHuaweiCheckVersion() < 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "huaweiCheckVersion值不合法");
        }

        // 获取当前配置
        JSONObject jsonObject = commonConfig.getSwitchRealConfig();

        // 更新配置
        jsonObject.put(HUAWEI_CHECK_SWITCH_KEY, dto.getHuaweiCheckSwitch());
        jsonObject.put(HUAWEI_CHECK_VERSION_KEY, dto.getHuaweiCheckVersion());

        Update update = new Update();
        update.set("sconfig", jsonObject);
        commonConfig.updateSwitchConfigData(CommonConfig.SWITCH_CONFIG, update);

        // 记录操作日志
        saveHuaweiSwitchLog(uid, dto.getHuaweiCheckSwitch(), dto.getHuaweiCheckVersion());

        logger.info("huaweiCheckSwitchSet success uid={}", uid);
    }

    /**
     * 获取华为检查开关配置
     */
    public HuaWeiCheckVO huaweiCheckSwitchList(String uid) {
        logger.info("huaweiCheckSwitchList uid={}", uid);

        HuaWeiCheckVO vo = new HuaWeiCheckVO();

        // 获取当前配置
        JSONObject jsonObject = commonConfig.getSwitchRealConfig();
        vo.setHuaweiCheckSwitch(jsonObject.getIntValue(HUAWEI_CHECK_SWITCH_KEY));
        vo.setHuaweiCheckVersion(jsonObject.getInteger(HUAWEI_CHECK_VERSION_KEY));

        // 获取操作日志
        List<HuaWeiCheckVO.HuaWeiSwitchLog> logList = getHuaweiSwitchLogList();
        vo.setLogList(logList);

        logger.info("huaweiCheckSwitchList success uid={}, result={}", uid, JSONObject.toJSONString(vo));
        return vo;
    }

    /**
     * 保存华为开关操作日志
     */
    private void saveHuaweiSwitchLog(String uid, int huaweiCheckSwitch, int huaweiCheckVersion) {
        try {
            HuaWeiCheckVO.HuaWeiSwitchLog log = new HuaWeiCheckVO.HuaWeiSwitchLog();
            log.setOpUid(uid);

            // 获取操作人名称
            Manager manager = managerDao.getDataByUid(uid);
            log.setOpName(manager != null ? manager.getAccount() : "unknown");

            log.setHuaweiCheckSwitch(huaweiCheckSwitch);
            log.setHuaweiCheckVersion(huaweiCheckVersion);
            log.setcTime(DateHelper.getNowSeconds());

            // 序列化为JSON并存储到Redis List
            String logJson = JSONObject.toJSONString(log);
            operationCommonRedis.addCommonListRecord(HUAWEI_SWITCH_LOG_KEY, logJson);

            logger.info("saveHuaweiSwitchLog success uid={}, log={}", uid, logJson);
        } catch (Exception e) {
            logger.error("saveHuaweiSwitchLog error uid={}", uid, e);
        }
    }

    /**
     * 获取华为开关操作日志列表
     */
    private List<HuaWeiCheckVO.HuaWeiSwitchLog> getHuaweiSwitchLogList() {
        try {
            List<String> logJsonList = operationCommonRedis.getCommonListRecord(HUAWEI_SWITCH_LOG_KEY, MAX_LOG_SIZE);
            if (logJsonList == null || logJsonList.isEmpty()) {
                return new ArrayList<>();
            }

            List<HuaWeiCheckVO.HuaWeiSwitchLog> logList = new ArrayList<>();
            for (String logJson : logJsonList) {
                try {
                    HuaWeiCheckVO.HuaWeiSwitchLog log = JSONObject.parseObject(logJson, HuaWeiCheckVO.HuaWeiSwitchLog.class);
                    if (log != null) {
                        logList.add(log);
                    }
                } catch (Exception e) {
                    logger.error("parse huawei switch log error, logJson={}", logJson, e);
                }
            }

            return logList;
        } catch (Exception e) {
            logger.error("getHuaweiSwitchLogList error", e);
            return new ArrayList<>();
        }
    }


    /**
     * 批量删除财务对账记录
     */
    public RealIncomeDTO deleteByIdList(RealIncomeDTO dto) {
        if (dto == null || CollectionUtils.isEmpty(dto.getIdSet())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "idSet不能为空");
        }
        long count = realIncomeDao.deleteByIdList(dto.getIdSet());
        RealIncomeDTO vo = new RealIncomeDTO();
        vo.setCount(count);
        logger.info("delete real income record count={}", count);
        return dto;
    }

    public PageVO<UserRecommendNoticeData> userRecommendNoticeList(UserRecommendNoticeDTO condition) {
        PageVO<UserRecommendNoticeData> pageVO = new PageVO<>();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 20 : condition.getPageSize();
        int start = (page - 1) * pageSize;
        List<UserRecommendNoticeData> dataList = userRecommendNoticeDao.selectPage(start, pageSize, condition);
        pageVO.setList(dataList);
        pageVO.setNextUrl(page, pageSize);
        return pageVO;
    }

    public void userRecommendNoticeAdd(UserRecommendNoticeDTO dto) {
        // 参数校验
        validateUserRecommendNoticeParams(dto, true);

        // 自动计算popLimit为所有popNumber之和
        calculateAndSetPopLimit(dto);

        UserRecommendNoticeData data = new UserRecommendNoticeData();
        BeanUtils.copyProperties(dto, data);

        // 设置创建时间和修改时间
        int now = (int) (System.currentTimeMillis() / 1000);
        data.setCtime(now);
        data.setMtime(now);

        userRecommendNoticeDao.insert(data);
        logger.info("userRecommendNoticeAdd success, id={}, popLimit={}", data.getId(), data.getPopLimit());
    }

    public void userRecommendNoticeUpdate(UserRecommendNoticeDTO dto) {
        if (dto.getId() == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "id不能为空");
        }

        UserRecommendNoticeData existingData = userRecommendNoticeDao.selectOne(dto.getId());
        if (existingData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "id不存在");
        }

        // 参数校验
        validateUserRecommendNoticeParams(dto, false);

        // 自动计算popLimit为所有popNumber之和
        calculateAndSetPopLimit(dto);

        // 复制属性到现有数据
        BeanUtils.copyProperties(dto, existingData);

        // 更新修改时间
        existingData.setMtime((int) (System.currentTimeMillis() / 1000));

        userRecommendNoticeDao.update(existingData);
        logger.info("userRecommendNoticeUpdate success, id={}, popLimit={}", dto.getId(), existingData.getPopLimit());
    }

    public void userRecommendNoticeDelete(UserRecommendNoticeDTO dto) {
        if (dto.getId() == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "id不能为空");
        }

        UserRecommendNoticeData data = userRecommendNoticeDao.selectOne(dto.getId());
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "id不存在");
        }

        userRecommendNoticeDao.delete(dto.getId());
        logger.info("userRecommendNoticeDelete success, id={}", dto.getId());
    }

    /**
     * 用户推荐通知参数校验
     */
    private void validateUserRecommendNoticeParams(UserRecommendNoticeDTO dto, boolean isAdd) {
        // 基本参数校验（popLimit不需要用户配置，会自动计算）
        if (dto.getUserType() == null || dto.getGender() == null || dto.getArea() == null ||
                dto.getScene() == null || dto.getTriggerMethod() == null || dto.getTriggerCondition() == null|| dto.getStatus() == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "必填参数不能为空");
        }

        // popList校验：必须至少有一条记录
        if (CollectionUtils.isEmpty(dto.getPopList())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "推荐弹窗列表不能为空，至少需要一条记录");
        }

        // 校验popList中的每个UserRecommendPopData
        validatePopList(dto.getPopList());

        // 流失用户类型校验
        if (dto.getUserType() == 1) {
            // 流失用户不能设置dayLimit，固定为15天（使用15-16格式表示15天开始）
            dto.setDayLimit("15-999");
        } else if (dto.getUserType() == 0) {
            // 注册新用户必须设置dayLimit
            if (!StringUtils.hasText(dto.getDayLimit())) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "注册新用户必须设置天数限制");
            }
            // 校验dayLimit格式
            validateDayLimitFormat(dto.getDayLimit());
        }

        // 检查用户群性别重叠
        Integer excludeId = isAdd ? null : dto.getId();
        List<UserRecommendNoticeData> existingGenderRecords = userRecommendNoticeDao.checkGenderOverlap(
                dto.getScene(), dto.getUserType(), null, dto.getArea(), dto.getGender(), excludeId);
        if (!existingGenderRecords.isEmpty()) {

            List<Integer> existingIds = existingGenderRecords.stream().map(UserRecommendNoticeData::getId).collect(Collectors.toList());
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "用户群性别重叠 existingIds:" + existingIds);
        }

        // 检查用户群地区重叠
        List<UserRecommendNoticeData> existingAreaRecords = userRecommendNoticeDao.checkAreaOverlap(
                dto.getScene(), dto.getUserType(), null, dto.getGender(), dto.getArea(), excludeId);
        if (!existingAreaRecords.isEmpty()) {

            List<Integer> existingIds = existingAreaRecords.stream().map(UserRecommendNoticeData::getId).collect(Collectors.toList());
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "用户群地区重叠 existingIds:" + existingIds);
        }
    }

    /**
     * 自动计算并设置popLimit为所有popNumber之和
     */
    private void calculateAndSetPopLimit(UserRecommendNoticeDTO dto) {
        if (!CollectionUtils.isEmpty(dto.getPopList())) {
            int totalPopNumber = dto.getPopList().stream()
                    .mapToInt(UserRecommendPopData::getPopNumber)
                    .sum();
            dto.setPopLimit(totalPopNumber);
            logger.info("自动计算popLimit: {}", totalPopNumber);
        } else {
            dto.setPopLimit(1);
        }
    }

    /**
     * 校验dayLimit格式：必须是{int}-{int}格式，且start < end
     */
    private void validateDayLimitFormat(String dayLimit) {
        if (!StringUtils.hasText(dayLimit)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "dayLimit不能为空");
        }

        // 检查是否包含"-"分隔符
        if (!dayLimit.contains("-")) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "dayLimit格式错误，必须是{int}-{int}格式，如：1-15");
        }

        String[] parts = dayLimit.split("-");
        if (parts.length != 2) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "dayLimit格式错误，必须是{int}-{int}格式，如：1-15");
        }

        try {
            int start = Integer.parseInt(parts[0].trim());
            int end = Integer.parseInt(parts[1].trim());

            // 校验数字范围：0-99999
            if (start < 0 || start > 99999 || end < 0 || end > 99999) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "dayLimit中的数字必须在0-99999范围内");
            }

            // 校验start < end
            if (start >= end) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "dayLimit格式错误，第一个数字必须小于第二个数字");
            }

            logger.info("dayLimit格式校验通过: start={}, end={}", start, end);

        } catch (NumberFormatException e) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "dayLimit格式错误，必须是{int}-{int}格式，如：1-15");
        }
    }

    /**
     * 校验popList中的每个UserRecommendPopData
     */
    private void validatePopList(List<UserRecommendPopData> popList) {
        for (UserRecommendPopData popData : popList) {
            // 检查必填字段
            if (popData.getPopType() == null || popData.getPopNumber() == null || popData.getPopParams() == null) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "popType、popNumber、popParams为必填字段");
            }

            // 校验popType范围：1~7
            if (popData.getPopType() < 1 || popData.getPopType() > 7) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "popType必须在1~7范围内");
            }

            // 校验popNumber范围：1~100000
            if (popData.getPopNumber() < 1 || popData.getPopNumber() > 100000) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "popNumber必须在1~100000范围内");
            }

            // 如果metaId为空，设置新的ObjectId
            if (!StringUtils.hasText(popData.getMetaId())) {
                popData.setMetaId(new ObjectId().toString());
                logger.info("设置新的metaId: {}", popData.getMetaId());
            }
        }
    }
}
