package com.quhong.operation.server;

import com.quhong.core.utils.DateHelper;
import com.quhong.data.ResourceGroupData;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.mongo.dao.BubbleDao;
import com.quhong.mongo.dao.BuddleSourceDao;
import com.quhong.mongo.data.BuddleSourceData;
import com.quhong.operation.share.condition.ItemCondition;
import com.quhong.operation.share.condition.ResourceCondition;
import com.quhong.operation.share.dto.BubbleSourceDTO;
import com.quhong.operation.share.vo.BubbleSourceVO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.ResourceVO;
import com.quhong.redis.GoodsListHomeRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class BubbleSourceService implements ResourceService{
    private static final Logger logger = LoggerFactory.getLogger(BubbleSourceService.class);
    public static final int TYPE_BUDDLE = 6; // 气泡

    @Resource
    private BuddleSourceDao buddleSourceDao;
    @Resource
    private BubbleDao bubbleDao;
    @Resource
    private GoodsListHomeRedis goodsListHomeRedis;



    public PageResultVO<BubbleSourceVO> bubbleList(ItemCondition condition){
        PageResultVO<BubbleSourceVO> pageVO = new PageResultVO<>();
        Integer status = condition.getStatus();
        Integer itemType = condition.getItemType();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;

        List<BuddleSourceData> buddleSourceList = buddleSourceDao.selectBubbleSourcePage(itemType, status, search, start, pageSize);
        // List<Integer> buddleIdList = buddleSourceList.stream().map(BuddleSourceData::getBuddle_id).collect(Collectors.toList());
        // Map<Integer, Integer> resourceGroupMap = bubbleDao.findResourceGroupList(buddleIdList).stream().collect(Collectors.toMap(ResourceGroupData::getResourceId, ResourceGroupData::getCount));

        List<BubbleSourceVO> voList = new ArrayList<>();
        for(BuddleSourceData data: buddleSourceList){
            BubbleSourceVO vo = new BubbleSourceVO();
            BeanUtils.copyProperties(data, vo);
            vo.setDocId(data.get_id().toString());
            // vo.setOwnUser(resourceGroupMap.getOrDefault(data.getBuddle_id(), 0));
            vo.setOwnUser(0);
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(buddleSourceDao.selectCount(itemType, status, search));
        return pageVO;
    }


    public void addBubbleSourceData(BubbleSourceDTO dto){

        if(StringUtils.isEmpty(dto.getBuddle_icon()) || StringUtils.isEmpty(dto.getBuddle_color())){
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "气泡图标或颜色未设置");
        }

        if(StringUtils.isEmpty(dto.getAndroid_buddle_source_1x()) || StringUtils.isEmpty(dto.getAndroid_buddle_source())
                || StringUtils.isEmpty(dto.getAndroid_buddle_source_3x())){
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "android资源图未完整上传");
        }


        if(StringUtils.isEmpty(dto.getIos_buddle_source()) || StringUtils.isEmpty(dto.getIos_buddle_source_2x()) ||
                StringUtils.isEmpty(dto.getIos_buddle_source_3x())){
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "ios资源图未完整上传");
        }

        BuddleSourceData data = new BuddleSourceData();
        BeanUtils.copyProperties(dto, data);
        BuddleSourceData lastData = buddleSourceDao.getLastBubbleSourceData();
        int nextId = lastData != null ? lastData.getBuddle_id() + 1 : 1;
        data.setBuddle_id(nextId);
        data.setC_time(DateHelper.getNowSeconds());
        buddleSourceDao.insert(data);

        if(data.getItem_type() == 5 && data.getStatus() == 1){
            goodsListHomeRedis.addNewGoodsRankingScore(TYPE_BUDDLE, nextId);
        }


    }

    public void updateBubbleSourceData(BubbleSourceDTO dto) {

        BuddleSourceData data = buddleSourceDao.getBubbleSourceByID(dto.getDocId());
        if(data == null){
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        if(data.getItem_type() == 5 && data.getItem_type() != dto.getItem_type()){
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "已上传的资源不能修改类型");
        }

        Update update = new Update();
        update.set("name", dto.getName() != null ? dto.getName() : "");
        update.set("namear", dto.getNamear() != null ? dto.getNamear(): "");
        update.set("status", dto.getStatus());
        update.set("item_type", dto.getItem_type());
        update.set("is_new", dto.getIs_new());
        update.set("forder", dto.getForder());
        update.set("buy_type", dto.getBuy_type());
        update.set("beans", dto.getBeans());
        update.set("days", dto.getDays());
        update.set("buddle_color", dto.getBuddle_color());

        if(!StringUtils.isEmpty(dto.getBuddle_icon())){
            update.set("buddle_icon", dto.getBuddle_icon());
        }
        if(!StringUtils.isEmpty(dto.getBuddle_preview())){
            update.set("buddle_preview", dto.getBuddle_preview());
        }

        if(!StringUtils.isEmpty(dto.getAndroid_buddle_source())){
            update.set("android_buddle_source", dto.getAndroid_buddle_source());
        }

        if(!StringUtils.isEmpty(dto.getAndroid_buddle_source_1x())){
            update.set("android_buddle_source_1x", dto.getAndroid_buddle_source_1x());
        }

        if(!StringUtils.isEmpty(dto.getAndroid_buddle_source_3x())){
            update.set("android_buddle_source_3x", dto.getAndroid_buddle_source_3x());
        }

        if(!StringUtils.isEmpty(dto.getIos_buddle_source())){
            update.set("ios_buddle_source", dto.getIos_buddle_source());
        }

        if(!StringUtils.isEmpty(dto.getIos_buddle_source_2x())){
            update.set("ios_buddle_source_2x", dto.getIos_buddle_source_2x());
        }

        if(!StringUtils.isEmpty(dto.getIos_buddle_source_3x())){
            update.set("ios_buddle_source_3x", dto.getIos_buddle_source_3x());
        }


        // 选择性填写
        update.set("buddle_tl", dto.getBuddle_tl());
        update.set("buddle_tr", dto.getBuddle_tr());
        update.set("buddle_bl", dto.getBuddle_bl());
        update.set("buddle_br", dto.getBuddle_br());

        buddleSourceDao.updateData(dto.getDocId(), update);

        if(data.getItem_type() == 5){
            if(dto.getStatus() == 1){
                goodsListHomeRedis.addNewGoodsRankingScore(TYPE_BUDDLE, data.getBuddle_id());
            }

            if(dto.getStatus() == 0){
                goodsListHomeRedis.deleteItemNewGoodsRanking(TYPE_BUDDLE, data.getBuddle_id());
                goodsListHomeRedis.deleteItemHotGoodsRanking(TYPE_BUDDLE, data.getBuddle_id());
            }
        }
    }


    @Override
    public PageResultVO<ResourceVO> getGoodsList(ResourceCondition condition) {
        PageResultVO<ResourceVO> pageVO = new PageResultVO<>();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;

        List<BuddleSourceData> buddleSourceList = buddleSourceDao.selectBubbleSourcePage(-1, condition.getStatus(), search, start, pageSize);
        List<ResourceVO> voList = new ArrayList<>();
        for(BuddleSourceData data: buddleSourceList){
            ResourceVO vo = new ResourceVO();
            vo.setResourceId(data.getBuddle_id());
            vo.setResourceIcon(data.getBuddle_icon());
            vo.setResourceNameEn(data.getName());
            vo.setResourceNameAr(data.getNamear());
            vo.setResourceType(condition.getResourceType());
            vo.setResourcePrice(data.getBeans());
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(buddleSourceDao.selectCount(-1, condition.getStatus(), search));
        return pageVO;
    }
}
