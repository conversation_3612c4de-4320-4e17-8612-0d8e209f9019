package com.quhong.operation.server;

import com.quhong.core.utils.DateHelper;
import com.quhong.data.ResourceGroupData;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.FloatScreenDao;
import com.quhong.mongo.dao.FloatScreenSourceDao;
import com.quhong.mongo.data.FloatScreenSourceData;
import com.quhong.operation.share.condition.ItemCondition;
import com.quhong.operation.share.condition.ResourceCondition;
import com.quhong.operation.share.dto.FloatScreenSourceDTO;
import com.quhong.operation.share.vo.FloatScreenVO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.ResourceVO;
import com.quhong.operation.utils.StringUtil;
import com.quhong.redis.GoodsListHomeRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class FloatScreenService implements ResourceService{
    private static final Logger logger = LoggerFactory.getLogger(FloatScreenService.class);
    public static final int TYPE_FLOAT_SCREEN = 8; // 浮屏

    @Resource
    private FloatScreenSourceDao floatScreenSourceDao;
    @Resource
    private FloatScreenDao floatScreenDao;
    @Resource
    private GoodsListHomeRedis goodsListHomeRedis;



    public PageResultVO<FloatScreenVO> floatScreenSourceList(ItemCondition condition){
        PageResultVO<FloatScreenVO> pageVO = new PageResultVO<>();
        Integer status = condition.getStatus();
        Integer itemType = condition.getItemType();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;

        List<FloatScreenSourceData> floatScreenList = floatScreenSourceDao.selectFloatScreenSourcePage(itemType, status, search, start, pageSize);
        // List<Integer> floatScreenIdList = floatScreenList.stream().map(FloatScreenSourceData::getScreen_id).collect(Collectors.toList());
        // Map<Integer, Integer> resourceGroupMap = floatScreenDao.findResourceGroupList(floatScreenIdList).stream().collect(Collectors.toMap(ResourceGroupData::getResourceId, ResourceGroupData::getCount));
        List<FloatScreenVO> voList = new ArrayList<>();
        for(FloatScreenSourceData data: floatScreenList){
            FloatScreenVO vo = new FloatScreenVO();
            BeanUtils.copyProperties(data, vo);
            vo.setDocId(data.get_id().toString());
            vo.setCache_screen(data.getScreenSourceVap());
            // vo.setOwnUser(resourceGroupMap.getOrDefault(data.getScreen_id(), 0));
            vo.setOwnUser(0);
            voList.add(vo);
        }

        pageVO.setList(voList);
        pageVO.setTotal(floatScreenSourceDao.selectCount(itemType, status, search));
        return pageVO;
    }

    private void formatCheck(String strUrl) {
        try {
            URL url = new URL(strUrl);
            String fileName = url.getFile();
            String extension = fileName.substring(fileName.lastIndexOf(".") + 1);
            if (!"mp4".equals(extension)) {
                throw new CommonH5Exception(1, "仅支持mp4格式");
            }
        } catch (Exception e) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
    }


    public void addFloatScreenSourceData(FloatScreenSourceDTO dto){

        if(StringUtils.isEmpty(dto.getScreen_icon()) || StringUtils.isEmpty(dto.getScreenSourceVap())){
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "资源图未上传");
        }
        formatCheck(dto.getScreenSourceVap());

        FloatScreenSourceData data = new FloatScreenSourceData();
        BeanUtils.copyProperties(dto, data);
        FloatScreenSourceData lastData = floatScreenSourceDao.getLastScreenSourceData();
        int nextId = lastData != null ? lastData.getScreen_id() + 1 : 1;
        data.setScreen_id(nextId);
        data.setC_time(DateHelper.getNowSeconds());
        floatScreenSourceDao.insert(data);

        if(data.getItem_type() == 5 && data.getStatus() == 1){
            goodsListHomeRedis.addNewGoodsRankingScore(TYPE_FLOAT_SCREEN, nextId);
        }


    }

    public void updateFloatScreenData(FloatScreenSourceDTO dto) {

        FloatScreenSourceData data = floatScreenSourceDao.getScreenSourceDataByID(dto.getDocId());
        if(data == null){
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        if(data.getItem_type() == 5 && data.getItem_type() != dto.getItem_type()){
            throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "已上传的资源不能修改类型");
        }

        Update update = new Update();
        update.set("name", dto.getName() !=null ? dto.getName(): "");
        update.set("namear", dto.getNamear() !=null ? dto.getNamear(): "");
        update.set("label_name", dto.getLabel_name() !=null ? dto.getLabel_name(): "");
        update.set("label_namear", dto.getLabel_namear() !=null ? dto.getLabel_namear(): "");
        update.set("status", dto.getStatus());
        update.set("seven_times_cost", dto.getSeven_times_cost());
        update.set("forder", dto.getForder());
        update.set("buy_type", dto.getBuy_type());
        update.set("beans", dto.getBeans());
        update.set("days", dto.getDays());
        update.set("is_new", dto.getIs_new());
        update.set("item_type", dto.getItem_type());

        if (!StringUtil.isEmpty(dto.getScreen_icon())){
            update.set("screen_icon", dto.getScreen_icon());
        }

        if (!StringUtil.isEmpty(dto.getScreenSourceVap())){
            update.set("screenSourceVap", dto.getScreenSourceVap());
            formatCheck(dto.getScreenSourceVap());
        }

        floatScreenSourceDao.updateData(dto.getDocId(), update);

        if(data.getItem_type() == 5){
            if(dto.getStatus() == 1){
                goodsListHomeRedis.addNewGoodsRankingScore(TYPE_FLOAT_SCREEN, data.getScreen_id());
            }

            if(dto.getStatus() == 0){
                goodsListHomeRedis.deleteItemNewGoodsRanking(TYPE_FLOAT_SCREEN, data.getScreen_id());
                goodsListHomeRedis.deleteItemHotGoodsRanking(TYPE_FLOAT_SCREEN, data.getScreen_id());
            }
        }

    }


    @Override
    public PageResultVO<ResourceVO> getGoodsList(ResourceCondition condition) {
        PageResultVO<ResourceVO> pageVO = new PageResultVO<>();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;

        List<FloatScreenSourceData> floatScreenList = floatScreenSourceDao.selectFloatScreenSourcePage(-1, condition.getStatus(), search, start, pageSize);
        List<ResourceVO> voList = new ArrayList<>();
        for(FloatScreenSourceData data: floatScreenList){
            ResourceVO vo = new ResourceVO();
            vo.setResourceId(data.getScreen_id());
            vo.setResourceIcon(data.getScreen_icon());
            vo.setResourceNameEn(data.getName());
            vo.setResourceNameAr(data.getNamear());
            vo.setResourcePrice(data.getBeans());
            vo.setResourceType(condition.getResourceType());
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(floatScreenSourceDao.selectCount(-1, condition.getStatus(), search));
        return pageVO;
    }
}
