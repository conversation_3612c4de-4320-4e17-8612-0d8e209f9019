package com.quhong.operation.server;

import com.quhong.constant.TaskRankConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.TaskRankTemplateDao;
import com.quhong.mongo.data.TaskRankTemplateData;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.vo.PageResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class TaskRankTemplateService {
    private static final Logger logger = LoggerFactory.getLogger(TaskRankTemplateService.class);

    @Resource
    private TaskRankTemplateDao taskRankTemplateDao;
    @Resource
    private GiftDao giftDao;

    /**
     * 任务榜单列表操作
     */
    public PageResultVO<TaskRankTemplateData> list(BaseCondition condition) {
        PageResultVO<TaskRankTemplateData> pageVO = new PageResultVO<>();
        int status = condition.getStatus();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;
        List<TaskRankTemplateData> pageData = taskRankTemplateDao.selectTaskRankTemplatePage(search, status, start, pageSize);
        pageVO.setList(pageData);
        pageVO.setTotal(taskRankTemplateDao.selectCount(search, status));
        return pageVO;
    }

    public void addData(TaskRankTemplateData dto) {
        paramCheck(dto);
        TaskRankTemplateData data = new TaskRankTemplateData();
        BeanUtils.copyProperties(dto, data);
        int currentTime = DateHelper.getNowSeconds();
        data.setStatus(TaskRankConstant.STATUS_INIT);
        data.setCtime(currentTime);
        data.setMtime(currentTime);
        taskRankTemplateDao.insert(data);
    }

    public void updateData(TaskRankTemplateData dto) {
        TaskRankTemplateData data = taskRankTemplateDao.getDataByID(dto.getActivityId());
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "数据不存在");
        }
        paramCheck(dto);
        int now = DateHelper.getNowSeconds();
        if (data.getStatus() != TaskRankConstant.STATUS_INIT && data.getStartTime() < now && data.getEndTime() > now) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "活动已开始，无法修改");
        }
        if (data.getStatus() == TaskRankConstant.STATUS_OFFLINE) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "活动已结束，无法更新");
        }

        Update update = new Update();
        update.set("nameEn", dto.getNameEn());
        update.set("nameAr", dto.getNameAr());
        update.set("url", dto.getUrl());
        update.set("startTime", dto.getStartTime());
        update.set("endTime", dto.getEndTime());
        update.set("status", dto.getStatus());
        update.set("testStatus", dto.getTestStatus());
        update.set("headUrl", dto.getHeadUrl());
        update.set("headGifUrl", dto.getHeadGifUrl());
        update.set("webStyle", dto.getWebStyle());
        update.set("country", dto.getCountry());
        update.set("gender", dto.getGender());
        update.set("giftEnable", dto.isGiftEnable());
        update.set("giftList", dto.getGiftList());
        update.set("dailyTaskEnable", dto.isDailyTaskEnable());
        update.set("dailyTaskList", dto.getDailyTaskList());
        update.set("periodTaskEnable", dto.isPeriodTaskEnable());
        update.set("periodTaskList", dto.getPeriodTaskList());
        update.set("rankConfigEnable", dto.isRankConfigEnable());
        update.set("rankConfig", dto.getRankConfig());
        update.set("activityRule", dto.getActivityRule());
        update.set("mtime", DateHelper.getNowSeconds());
        taskRankTemplateDao.updateData(dto.getActivityId(), update);
    }

    private void paramCheck(TaskRankTemplateData dto) {
        String nameEn = dto.getNameEn();
        String nameAr = dto.getNameAr();
        if (ObjectUtils.isEmpty(nameEn) || ObjectUtils.isEmpty(nameAr)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "活动名不能为空");
        }

        if (dto.getStartTime() <= 0 || dto.getEndTime() <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "活动时间配置有误");
        }

        if (dto.isGiftEnable() && CollectionUtils.isEmpty(dto.getGiftList())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "活动礼物配置为空");
        }
        if (dto.getEndTime() - DateHelper.getNowSeconds() <= 10 * 60) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "活动结束时间距离现在太近，请检查");
        }
        if (dto.isGiftEnable()) {
            for (TaskRankTemplateData.ActivityGift activityGift : dto.getGiftList()) {
                GiftData giftData = giftDao.getGiftFromCache(activityGift.getGiftId());
                if (giftData == null) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), String.format("活动礼物不存在, 礼物id: %s", activityGift.getGiftId()));
                }
            }
        }

        // 日任务check
        if (dto.isDailyTaskEnable() && CollectionUtils.isEmpty(dto.getDailyTaskList())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "日任务配置为空");
        }

        if (dto.isDailyTaskEnable()) {
            for (TaskRankTemplateData.TaskRankConfig taskConfig : dto.getDailyTaskList()) {
                if (ObjectUtils.isEmpty(taskConfig.getTitle()) || ObjectUtils.isEmpty(taskConfig.getResourceKey())) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "任务标题、奖励资源key配置为空");
                }

                Integer totalProcess = taskConfig.getTotalProcess();
                if (totalProcess == null || totalProcess <= 0) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "任务值配置有误");
                }
            }
            findDuplicateKeys(dto.getDailyTaskList());
        }

        // 周期任务check
        if (dto.isPeriodTaskEnable() && CollectionUtils.isEmpty(dto.getPeriodTaskList())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "里程碑任务配置为空");
        }
        if (dto.isPeriodTaskEnable()) {
            for (TaskRankTemplateData.TaskRankConfig taskConfig : dto.getPeriodTaskList()) {
                if (ObjectUtils.isEmpty(taskConfig.getTitle()) || ObjectUtils.isEmpty(taskConfig.getResourceKey())) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "任务标题、奖励资源key配置为空");
                }

                Integer totalProcess = taskConfig.getTotalProcess();
                if (totalProcess == null || totalProcess <= 0) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "任务值配置有误");
                }
            }
            findDuplicateKeys(dto.getPeriodTaskList());
        }

        // 榜单参数check
        if (dto.isRankConfigEnable() && ObjectUtils.isEmpty(dto.getRankConfig())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "排行榜配置为空");
        }
        if (dto.isRankConfigEnable()) {
            TaskRankTemplateData.TaskRankConfig rankConfig = dto.getRankConfig();
            if (CollectionUtils.isEmpty(rankConfig.getRankRewardList())) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "排行榜奖励配置为空");
            }
        }
    }


    public void findDuplicateKeys(List<TaskRankTemplateData.TaskRankConfig> taskRankConfigList) {
        Map<String, Long> keyCounts = taskRankConfigList.stream()
                .map(taskRankConfig -> getDetailTaskKey(taskRankConfig.getTaskRankKey(),
                        taskRankConfig.getTotalProcess(),
                        taskRankConfig.getExtraParam()))
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

        Map<String, Long> duplicateKeys = keyCounts.entrySet().stream()
                .filter(entry -> entry.getValue() >= 2)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        if (!duplicateKeys.isEmpty()) {
            logger.info("Duplicate keys found: {}", duplicateKeys);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "重复的任务:" + duplicateKeys);
        }
    }

    private String getDetailTaskKey(String taskKey, int totalProcess, String extraParam) {
        return ObjectUtils.isEmpty(extraParam) ? String.format("%s:%s", taskKey, totalProcess)
                : String.format("%s:%s:%s", taskKey, totalProcess, extraParam);
    }
}
