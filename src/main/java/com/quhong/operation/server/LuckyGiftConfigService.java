package com.quhong.operation.server;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.LuckyGiftConfigDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.mysql.data.LuckyGiftConfigData;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.vo.GiftVO;
import com.quhong.operation.share.vo.LuckyGiftConfigVO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.redis.LuckyGiftNewRedis;
import com.quhong.redis.LuckyGiftRedis;
import com.quhong.utils.CollectionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;

@Component
public class LuckyGiftConfigService {
    private static final Logger logger = LoggerFactory.getLogger(LuckyGiftConfigService.class);
    private static final Set<String> SUPPORT_RESOURCE = new HashSet<>(Arrays.asList("mic", "buddle", "ride", "ripple",
            "diamond", "badge", "gift", "float_screen", "thanks",ResourceConstant.BACK_GROUND,ResourceConstant.HONOR_TITLE
    ,ResourceConstant.ENTRY_EFFECT));

    @Resource
    private LuckyGiftConfigDao luckyGiftConfigDao;
    @Resource
    private GiftDao giftDao;
    @Resource
    private LuckyGiftRedis luckyGiftRedis;
    @Resource
    private LuckyGiftNewRedis luckyGiftNewRedis;

    public PageResultVO<LuckyGiftConfigVO> getDataList(BaseCondition condition) {
        PageResultVO<LuckyGiftConfigVO> pageVO = new PageResultVO<>();
        IPage<GiftData> pageGift = giftDao.selectLuckGiftPageList(condition.getStatus(), 1, 999);
        List<LuckyGiftConfigVO> voList = new ArrayList<>();
        List<GiftData> jackpotLuckyGift = new ArrayList<>();
        for (GiftData data : pageGift.getRecords()) {
            LuckyGiftConfigVO vo = new LuckyGiftConfigVO();
            GiftVO.ZipInfoVO zipInfoVO = JSONObject.parseObject(data.getZipInfo(), GiftVO.ZipInfoVO.class);
            if (zipInfoVO.getJackpot() == 1) {
                jackpotLuckyGift.add(data);
                continue;
            }
            vo.setZipInfo(zipInfoVO);
            vo.setGiftList(Collections.singletonList(new LuckyGiftConfigVO.ConfigGiftVO(data.getRid(), data.getPrice(), data.getGicon())));
            vo.setType(1);
            vo.setStatus(data.getStatus());
            vo.setLuckyGiftLotteryList(luckyGiftConfigDao.selectList(data.getRid()));
            voList.add(vo);
        }
        if (!jackpotLuckyGift.isEmpty()) {
            LuckyGiftConfigVO vo = new LuckyGiftConfigVO();
            vo.setType(2);
            vo.setStatus(1);
            vo.setGiftList(new ArrayList<>());
            vo.setZipInfo(JSONObject.parseObject(jackpotLuckyGift.get(0).getZipInfo(), GiftVO.ZipInfoVO.class));
            voList.add(0, vo);
            for (GiftData data : jackpotLuckyGift) {
                vo.getGiftList().add(new LuckyGiftConfigVO.ConfigGiftVO(data.getRid(), data.getPrice(), data.getGicon()));
            }
            vo.setLuckyGiftLotteryList(luckyGiftConfigDao.selectList(0));
        }
        pageVO.setList(voList);
        pageVO.setTotal(voList.size());
        return pageVO;
    }

    private void paramCheck(LuckyGiftConfigVO dto, boolean save) {
        // 设置jackpot参数
        dto.getZipInfo().setJackpot(dto.getType() == 2 ? 1 : 0);
        if (null == dto.getGiftList()) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        if (save) {
            for (LuckyGiftConfigVO.ConfigGiftVO configGiftVO : dto.getGiftList()) {
                GiftData giftData = giftDao.selectOne(configGiftVO.getRid());
                if (giftData == null) {
                    throw new CommonException(HttpCode.PARAM_ERROR);
                }
                if (giftData.getGamePlay() > 0) {
                    logger.info("giftData gamePlay already set {}", giftData.getGamePlay());
                    throw new CommonException(1, "礼物" + giftData.getRid() + "已经配置礼物玩法");
                }
            }
        }
        if (dto.getType() != 1 && dto.getType() != 2) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        if (dto.getType() == 1 && dto.getGiftList().size() > 1) {
            throw new CommonException(1, "道具版幸运礼物不能多选");
        }
        List<LuckyGiftConfigData> luckyGiftConfigList = dto.getLuckyGiftLotteryList();
        if (null != luckyGiftConfigList) {
            for (LuckyGiftConfigData configData : luckyGiftConfigList) {
                if (!SUPPORT_RESOURCE.contains(configData.getRewardType())) {
                    logger.info("rewardType not support {}", configData.getRewardType());
                    throw new CommonException(HttpCode.PARAM_ERROR);
                }
                if (0 == dto.getZipInfo().getJackpot() && ObjectUtils.isEmpty(configData.getIcon())) {
                    throw new CommonException(1, "奖励图标不能为空");
                }
                if (ResourceConstant.DIAMOND.equals(configData.getRewardType())) {
                    if (null == configData.getRewardNum()) {
                        logger.info("钻石数量不能为空 {}", configData.getRewardType());
                        throw new CommonException(HttpCode.PARAM_ERROR);
                    }
                } else if (ResourceConstant.HEART.equals(configData.getRewardType())) {
                    if (null == configData.getRewardNum()) {
                        logger.info("金币数量不能为空 {}", configData.getRewardType());
                        throw new CommonException(HttpCode.PARAM_ERROR);
                    }
                } else if (ResourceConstant.THANKS.equals(configData.getRewardType())) {
                    logger.info("无中奖上传");

                } else {
                    if (null == configData.getSourceId()) {
                        logger.info("资源id不能为空 {}", configData.getRewardType());
                        throw new CommonException(HttpCode.PARAM_ERROR);
                    }
                    if (null == configData.getRewardTime()) {
                        logger.info("资源时长不能为空 {}", configData.getRewardType());
                        throw new CommonException(HttpCode.PARAM_ERROR);
                    }
                }
            }
        }
    }

    public void addData(LuckyGiftConfigVO dto) {
        paramCheck(dto, true);
        // 设置礼物数据
        for (LuckyGiftConfigVO.ConfigGiftVO configGiftVO : dto.getGiftList()) {
            updateGift(configGiftVO, dto);
        }
        // 钻石版配置，清除钻石版奖池
        if (1 == dto.getZipInfo().getJackpot()) {
            luckyGiftRedis.clearLuckyGiftPool(0);
        }
        // 新增奖池配置
        List<LuckyGiftConfigData> luckyGiftConfigList = dto.getLuckyGiftLotteryList();
        if (null != luckyGiftConfigList) {
            for (LuckyGiftConfigData configData : luckyGiftConfigList) {
                configData.setId(null);
                configData.setCtime(DateHelper.getNowSeconds());
                if (1 == dto.getZipInfo().getJackpot()) {
                    configData.setGiftId(0);
                    configData.setNameEn("jackpot lucky gift");
                    configData.setNameAr("jackpot lucky gift");
                    configData.setSourceId(0);
                    configData.setRewardType("");
                    configData.setIcon("");
                    configData.setBigPush(0);
                    configData.setRewardTime(0);
                } else {
                    configData.setGiftId(dto.getGiftList().get(0).getRid());
                }
            }
            luckyGiftConfigDao.insertMany(luckyGiftConfigList);
        }
    }

    private void updateGift(LuckyGiftConfigVO.ConfigGiftVO configGiftVO, LuckyGiftConfigVO dto) {
        GiftData giftData = giftDao.selectOne(configGiftVO.getRid());
        giftData.setGamePlay(GiftDao.GAME_PLAY_LUCKY_GIFT);
        GiftVO.ZipInfoVO zipInfoVO = JSONObject.parseObject(giftData.getZipInfo(), GiftVO.ZipInfoVO.class);
        zipInfoVO.setZtype(GiftDao.GAME_PLAY_LUCKY_GIFT);
        zipInfoVO.setWebType(1);
        zipInfoVO.setShowDetail(1);
        zipInfoVO.setDesc(dto.getZipInfo().getDesc());
        zipInfoVO.setDescAr(dto.getZipInfo().getDescAr());
        zipInfoVO.setPropIcon(dto.getZipInfo().getPropIcon());
        zipInfoVO.setDescUrl(dto.getZipInfo().getDescUrl());
        zipInfoVO.setHeight(dto.getZipInfo().getHeight());
        zipInfoVO.setWidth(dto.getZipInfo().getWidth());
        zipInfoVO.setJackpot(dto.getZipInfo().getJackpot());
        if (1 == dto.getZipInfo().getJackpot()) {
            zipInfoVO.setShowDetail(3);
        }
        giftData.setZipInfo(JSON.toJSONString(zipInfoVO));
        giftDao.updateOne(giftData);
    }

    public void updateData(LuckyGiftConfigVO dto) {
        paramCheck(dto, false);
        // 钻石版本可能会增加删除礼物
        if (dto.getType() == 2) {
            Set<Integer> updateGift = CollectionUtil.listToPropertySet(dto.getGiftList(), LuckyGiftConfigVO.ConfigGiftVO::getRid);
            IPage<GiftData> pageGift = giftDao.selectLuckGiftPageList(-1, 1, 999);
            for (GiftData data : pageGift.getRecords()) {
                GiftVO.ZipInfoVO zipInfoVO = JSONObject.parseObject(data.getZipInfo(), GiftVO.ZipInfoVO.class);
                if (zipInfoVO.getJackpot() == 1) {
                    if (!updateGift.contains(data.getRid())) {
                        // 已被删除
                        resetGiftData(data);
                    }
                }
            }
        }
        // 更新礼物数据
        for (LuckyGiftConfigVO.ConfigGiftVO configGiftVO : dto.getGiftList()) {
            updateGift(configGiftVO, dto);
            if (null == dto.getZipInfo().getJackpot() || 0 == dto.getZipInfo().getJackpot()) {
                luckyGiftNewRedis.deletePoolSize(configGiftVO.getRid());
            }
            if (1 == dto.getZipInfo().getJackpot()) {
                luckyGiftRedis.clearLuckyGiftPool(0);
            }
        }
        // 更新奖池配置
        List<LuckyGiftConfigData> luckyGiftConfigList = dto.getLuckyGiftLotteryList();
        if (null != luckyGiftConfigList) {
            for (LuckyGiftConfigData configData : luckyGiftConfigList) {
                if (1 == dto.getZipInfo().getJackpot()) {
                    configData.setGiftId(0);
                    configData.setNameEn("jackpot lucky gift");
                    configData.setNameAr("jackpot lucky gift");
                    configData.setSourceId(0);
                    configData.setRewardType("");
                    configData.setRewardTime(0);
                    configData.setBigPush(0);
                    configData.setIcon("");
                } else {
                    configData.setGiftId(dto.getGiftList().get(0).getRid());
                }
                if (configData.getId() == null) {
                    configData.setCtime(DateHelper.getNowSeconds());
                    luckyGiftConfigDao.insertOne(configData);
                } else {
                    luckyGiftConfigDao.updateOne(configData);
                }
            }
        }
        // 删除奖池配置项
        int lotteryGiftId = 1 == dto.getZipInfo().getJackpot() ? 0 : dto.getGiftList().get(0).getRid();
        List<LuckyGiftConfigData> lotteryDataList = luckyGiftConfigDao.selectList(lotteryGiftId);
        Map<Integer, LuckyGiftConfigData> configMap = CollectionUtil.listToKeyMap(luckyGiftConfigList, LuckyGiftConfigData::getId);
        for (LuckyGiftConfigData lotteryData : lotteryDataList) {
            if (!configMap.containsKey(lotteryData.getId())) {
                luckyGiftConfigDao.removeById(lotteryData.getId());
            }
        }
    }

    public void deleteData(LuckyGiftConfigVO dto) {
        // 设置jackpot参数
        dto.getZipInfo().setJackpot(dto.getType() == 2 ? 1 : 0);
        int lotteryGiftId = 1 == dto.getZipInfo().getJackpot() ? 0 : dto.getGiftList().get(0).getRid();
        for (LuckyGiftConfigVO.ConfigGiftVO configGiftVO : dto.getGiftList()) {
            GiftData giftData = giftDao.selectOne(configGiftVO.getRid());
            if (giftData == null || giftData.getGamePlay() != GiftDao.GAME_PLAY_LUCKY_GIFT) {
                throw new CommonException(HttpCode.PARAM_ERROR);
            }
            resetGiftData(giftData);
        }
        luckyGiftConfigDao.deleteData(lotteryGiftId);
    }

    private void resetGiftData(GiftData giftData) {
        GiftVO.ZipInfoVO zipInfoVO = JSONObject.parseObject(giftData.getZipInfo(), GiftVO.ZipInfoVO.class);
        zipInfoVO.setZtype(null);
        zipInfoVO.setWebType(null);
        zipInfoVO.setShowDetail(null);
        zipInfoVO.setDesc(null);
        zipInfoVO.setDescAr(null);
        zipInfoVO.setDescUrl(null);
        zipInfoVO.setPropIcon(null);
        zipInfoVO.setHeight(null);
        zipInfoVO.setWidth(null);
        zipInfoVO.setJackpot(null);
        giftData.setZipInfo(JSON.toJSONString(zipInfoVO));
        giftData.setGamePlay(0);
        giftDao.updateOne(giftData);
        logger.info("resetGiftData giftId={}", giftData.getRid());
    }
}
