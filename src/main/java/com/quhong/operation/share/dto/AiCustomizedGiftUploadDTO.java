package com.quhong.operation.share.dto;

import com.quhong.operation.share.condition.BaseCondition;

import java.util.List;

public class AiCustomizedGiftUploadDTO extends BaseCondition {

    /**
     * 更新用数据唯一id
     */
    private Integer rid;

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 0待审核 1 通过 2 审核不通过
     */
    private Integer state;

    /**
     * 头像url
     */
    private String imageUrl;

    /**
     * 上传资源的月份 yyyy_mm
     */
    private String month;

    /**
     * 人审的备注消息
     */
    private String descNotice;


    private List<Integer> ridList;

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public String getDescNotice() {
        return descNotice;
    }

    public void setDescNotice(String descNotice) {
        this.descNotice = descNotice;
    }

    public List<Integer> getRidList() {
        return ridList;
    }

    public void setRidList(List<Integer> ridList) {
        this.ridList = ridList;
    }
}
