package com.quhong.operation.share.vo.reports;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 *  渠道打钻额度调整记录VO实体
 */
public class ChannelBeansAmountLogVO {

    /**
     * 渠道
     */
    @ExcelProperty("渠道")
    private String channel;

    /**
     * 调整前额度
     */
    @ExcelProperty("调整前额度")
    private Integer beforeAmount;

    /**
     * 消耗钻石数
     */
    @ExcelProperty("消耗钻石数")
    private Integer costAmount;

    /**
     * 调整后额度
     */
    @ExcelProperty("调整后额度")
    private Integer afterAmount;

    /**
     * 操作者
     */
    @ExcelProperty("操作者")
    private String opUser;

    /**
     * 操作时间
     */
    @ExcelProperty("操作时间")
    private String opTime;

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public Integer getBeforeAmount() {
        return beforeAmount;
    }

    public void setBeforeAmount(Integer beforeAmount) {
        this.beforeAmount = beforeAmount;
    }

    public Integer getCostAmount() {
        return costAmount;
    }

    public void setCostAmount(Integer costAmount) {
        this.costAmount = costAmount;
    }

    public Integer getAfterAmount() {
        return afterAmount;
    }

    public void setAfterAmount(Integer afterAmount) {
        this.afterAmount = afterAmount;
    }

    public String getOpUser() {
        return opUser;
    }

    public void setOpUser(String opUser) {
        this.opUser = opUser;
    }

    public String getOpTime() {
        return opTime;
    }

    public void setOpTime(String opTime) {
        this.opTime = opTime;
    }

    @Override
    public String toString() {
        return "ChannelBeansAmountLogVO{" +
                "channel='" + channel + '\'' +
                ", beforeAmount=" + beforeAmount +
                ", costAmount=" + costAmount +
                ", afterAmount=" + afterAmount +
                ", opUser='" + opUser + '\'' +
                ", opTime='" + opTime + '\'' +
                '}';
    }
}
