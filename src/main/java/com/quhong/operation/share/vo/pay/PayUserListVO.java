package com.quhong.operation.share.vo.pay;

import com.alibaba.excel.annotation.ExcelProperty;

public class PayUserListVO {

    @ExcelProperty("排序")
    private Integer order;
    @ExcelProperty("用户价值")
    private String userCn;
    @ExcelProperty("uid")
    private String uid;
    @ExcelProperty("rid")
    private Integer rid;
    @ExcelProperty("靓号记录")
    private String beautifulNum;
    @ExcelProperty("近30天内充值金额")
    private String last30Charge;
    @ExcelProperty("荣誉积分")
    private long honorExp;
    @ExcelProperty("流失等级")
    private String lossCn;
    @ExcelProperty("最后一次登录日期")
    private String lastLiTime;
    @ExcelProperty("近15天在房间内时长")
    private int last15RTime;
    @ExcelProperty("近7天发礼物钻石")
    private long last7Send;
    @ExcelProperty("近7天收礼物钻石")
    private long last7Recv;
    @ExcelProperty("最近一次充值日期")
    private String lastCgTime;
    @ExcelProperty("钻石余额")
    private int beans;


    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getUserCn() {
        return userCn;
    }

    public void setUserCn(String userCn) {
        this.userCn = userCn;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getBeautifulNum() {
        return beautifulNum;
    }

    public void setBeautifulNum(String beautifulNum) {
        this.beautifulNum = beautifulNum;
    }

    public String getLast30Charge() {
        return last30Charge;
    }

    public void setLast30Charge(String last30Charge) {
        this.last30Charge = last30Charge;
    }

    public long getHonorExp() {
        return honorExp;
    }

    public void setHonorExp(long honorExp) {
        this.honorExp = honorExp;
    }

    public String getLossCn() {
        return lossCn;
    }

    public void setLossCn(String lossCn) {
        this.lossCn = lossCn;
    }

    public String getLastLiTime() {
        return lastLiTime;
    }

    public void setLastLiTime(String lastLiTime) {
        this.lastLiTime = lastLiTime;
    }

    public int getLast15RTime() {
        return last15RTime;
    }

    public void setLast15RTime(int last15RTime) {
        this.last15RTime = last15RTime;
    }

    public long getLast7Send() {
        return last7Send;
    }

    public void setLast7Send(long last7Send) {
        this.last7Send = last7Send;
    }

    public long getLast7Recv() {
        return last7Recv;
    }

    public void setLast7Recv(long last7Recv) {
        this.last7Recv = last7Recv;
    }

    public String getLastCgTime() {
        return lastCgTime;
    }

    public void setLastCgTime(String lastCgTime) {
        this.lastCgTime = lastCgTime;
    }

    public int getBeans() {
        return beans;
    }

    public void setBeans(int beans) {
        this.beans = beans;
    }

}
