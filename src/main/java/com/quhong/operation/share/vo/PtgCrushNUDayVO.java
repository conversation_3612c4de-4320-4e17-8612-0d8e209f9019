package com.quhong.operation.share.vo;

import com.alibaba.excel.annotation.ExcelProperty;

public class PtgCrushNUDayVO {

    @ExcelProperty("日期")
    private String date;
    @ExcelProperty("日活")
    private int dau;
    @ExcelProperty("新增人数")
    private int newlyNum;
    @ExcelProperty("推荐人数")
    private int crushNum;
    @ExcelProperty("推荐次数")
    private int crushCount;
    @ExcelProperty("收到skip人数")
    private int skipNum;
    @ExcelProperty("收到skip次数")
    private int skipCount;
    @ExcelProperty("收到Like人数")
    private int likeNum;
    @ExcelProperty("收到Like次数")
    private int likeCount;
    @ExcelProperty("匹配成功人数")
    private int crushOkNum;
    @ExcelProperty("匹配成功率")
    private String crushOkRate = "0%";
    @ExcelProperty("在线时匹配成功人数")
    private int onlineCrushOkNum;
    @ExcelProperty("在线时匹配成功次数")
    private int onlineCrushOkCount;
    @ExcelProperty("离线时匹配成功人数")
    private int offlineCrushOkNum;
    @ExcelProperty("离线时匹配成功次数")
    private int offlineCrushOkCount;
    @ExcelProperty("离线24h内匹配成功人数")
    private int offline24CrushOkNum;
    @ExcelProperty("离线24h内匹配成功次数")
    private int offline24CrushOkCount;
    @ExcelProperty("离线48h内匹配成功人数")
    private int offline48CrushOkNum;
    @ExcelProperty("离线48h内匹配成功次数")
    private int offline48CrushOkCount;
    @ExcelProperty("发送消息人数")
    private int sendMsgNum;
    @ExcelProperty("发送消息率")
    private String sendMsgRate = "0%";

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public int getDau() {
        return dau;
    }

    public void setDau(int dau) {
        this.dau = dau;
    }

    public int getNewlyNum() {
        return newlyNum;
    }

    public void setNewlyNum(int newlyNum) {
        this.newlyNum = newlyNum;
    }

    public int getCrushNum() {
        return crushNum;
    }

    public void setCrushNum(int crushNum) {
        this.crushNum = crushNum;
    }

    public int getCrushCount() {
        return crushCount;
    }

    public void setCrushCount(int crushCount) {
        this.crushCount = crushCount;
    }

    public int getSkipNum() {
        return skipNum;
    }

    public void setSkipNum(int skipNum) {
        this.skipNum = skipNum;
    }

    public int getSkipCount() {
        return skipCount;
    }

    public void setSkipCount(int skipCount) {
        this.skipCount = skipCount;
    }

    public int getLikeNum() {
        return likeNum;
    }

    public void setLikeNum(int likeNum) {
        this.likeNum = likeNum;
    }

    public int getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(int likeCount) {
        this.likeCount = likeCount;
    }

    public int getCrushOkNum() {
        return crushOkNum;
    }

    public void setCrushOkNum(int crushOkNum) {
        this.crushOkNum = crushOkNum;
    }

    public String getCrushOkRate() {
        return crushOkRate;
    }

    public void setCrushOkRate(String crushOkRate) {
        this.crushOkRate = crushOkRate;
    }

    public int getOnlineCrushOkNum() {
        return onlineCrushOkNum;
    }

    public void setOnlineCrushOkNum(int onlineCrushOkNum) {
        this.onlineCrushOkNum = onlineCrushOkNum;
    }

    public int getOnlineCrushOkCount() {
        return onlineCrushOkCount;
    }

    public void setOnlineCrushOkCount(int onlineCrushOkCount) {
        this.onlineCrushOkCount = onlineCrushOkCount;
    }

    public int getOfflineCrushOkNum() {
        return offlineCrushOkNum;
    }

    public void setOfflineCrushOkNum(int offlineCrushOkNum) {
        this.offlineCrushOkNum = offlineCrushOkNum;
    }

    public int getOfflineCrushOkCount() {
        return offlineCrushOkCount;
    }

    public void setOfflineCrushOkCount(int offlineCrushOkCount) {
        this.offlineCrushOkCount = offlineCrushOkCount;
    }

    public int getOffline24CrushOkNum() {
        return offline24CrushOkNum;
    }

    public void setOffline24CrushOkNum(int offline24CrushOkNum) {
        this.offline24CrushOkNum = offline24CrushOkNum;
    }

    public int getOffline24CrushOkCount() {
        return offline24CrushOkCount;
    }

    public void setOffline24CrushOkCount(int offline24CrushOkCount) {
        this.offline24CrushOkCount = offline24CrushOkCount;
    }

    public int getOffline48CrushOkNum() {
        return offline48CrushOkNum;
    }

    public void setOffline48CrushOkNum(int offline48CrushOkNum) {
        this.offline48CrushOkNum = offline48CrushOkNum;
    }

    public int getOffline48CrushOkCount() {
        return offline48CrushOkCount;
    }

    public void setOffline48CrushOkCount(int offline48CrushOkCount) {
        this.offline48CrushOkCount = offline48CrushOkCount;
    }

    public int getSendMsgNum() {
        return sendMsgNum;
    }

    public void setSendMsgNum(int sendMsgNum) {
        this.sendMsgNum = sendMsgNum;
    }

    public String getSendMsgRate() {
        return sendMsgRate;
    }

    public void setSendMsgRate(String sendMsgRate) {
        this.sendMsgRate = sendMsgRate;
    }
}
