package com.quhong.operation.share.vo;

import com.alibaba.excel.annotation.ExcelProperty;


public class StaffWelcomeVO {

    @ExcelProperty("ID")
    private int id;
    @ExcelProperty("昵称")
    private String name;
    @ExcelProperty("房间停留时长")
    private String stayRoomTime;
    @ExcelProperty("上麦时长")
    private String upMicTime;
    @ExcelProperty("欢迎人数")
    private int welcomeUsers;
    @ExcelProperty("邀请上麦人数")
    private int inviteUsers;
    @ExcelProperty("收新人礼物人数")
    private int receiveRookiesGift;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStayRoomTime() {
        return stayRoomTime;
    }

    public void setStayRoomTime(String stayRoomTime) {
        this.stayRoomTime = stayRoomTime;
    }

    public String getUpMicTime() {
        return upMicTime;
    }

    public void setUpMicTime(String upMicTime) {
        this.upMicTime = upMicTime;
    }

    public int getWelcomeUsers() {
        return welcomeUsers;
    }

    public void setWelcomeUsers(int welcomeUsers) {
        this.welcomeUsers = welcomeUsers;
    }

    public int getInviteUsers() {
        return inviteUsers;
    }

    public void setInviteUsers(int inviteUsers) {
        this.inviteUsers = inviteUsers;
    }

    public int getReceiveRookiesGift() {
        return receiveRookiesGift;
    }

    public void setReceiveRookiesGift(int receiveRookiesGift) {
        this.receiveRookiesGift = receiveRookiesGift;
    }
}
