package com.quhong.operation.dao;

import com.quhong.core.utils.DateHelper;
import com.quhong.datas.DayTimeData;
import com.quhong.mysql.slave_mapper.ustar_log.VideoStatLogMapper;
import com.quhong.operation.share.data.MysqlStatData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class VideoStatDao {

    private static final Logger logger = LoggerFactory.getLogger(VideoStatDao.class);

    @Autowired
    private VideoStatLogMapper videoStatLogMapper;

    /**
     * 获取视频房间的统计数据
     * @param dayTimeData
     * @return
     */
    public MysqlStatData getStatData(DayTimeData dayTimeData){
        String tableSuffix = DateHelper.ARABIAN.getTableSuffix(DateHelper.formatDate(dayTimeData.getTime()));
        return videoStatLogMapper.selectStatData(dayTimeData.getTime(),dayTimeData.getEndTime(),tableSuffix);
    }

}
