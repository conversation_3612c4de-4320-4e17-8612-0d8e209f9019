package com.quhong.operation.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.datas.DayTimeData;
import com.quhong.mysql.data.VideoOptData;
import com.quhong.mysql.slave_mapper.ustar_log.VideoOptSlaveMapper;
import com.quhong.operation.share.data.AggStatData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
public class VideoOptDao {

    private static final Logger logger = LoggerFactory.getLogger(VideoOptDao.class);

    @Autowired
    private VideoOptSlaveMapper videoOptSlaveMapper;

    /**
     * 获取每一天的统计数据
     *
     * @param uidSet
     * @param dayTimeData
     * @param os
     * @return
     */
    public List<AggStatData> getStatData(Set<String> uidSet, DayTimeData dayTimeData, int os) {
        QueryWrapper<VideoOptData> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("COUNT(1) count,uid");
        queryWrapper.gt("ctime", dayTimeData.getTime());
        queryWrapper.lt("ctime", dayTimeData.getEndTime());
        if (!CollectionUtils.isEmpty(uidSet)) {
            queryWrapper.in("uid", uidSet);
        }
        if (os != -1) {
            queryWrapper.eq("os", os);
        }
        queryWrapper.groupBy("uid");
        List<Map<String, Object>> list = videoOptSlaveMapper.selectMaps(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            List<AggStatData> aggStatDataList = new ArrayList<>();
            for (Map<String, Object> map : list) {
                AggStatData aggStatData = new AggStatData();
                Object countObj = map.get("count");
                if (countObj == null) {
                    aggStatData.setCount(0);
                } else {
                    aggStatData.setCount(Integer.parseInt(countObj.toString()));
                }
                String uid = (String) map.get("uid");
                aggStatData.setUid(uid);
                aggStatDataList.add(aggStatData);
            }
            return aggStatDataList;
        }
        return new ArrayList<>();
    }

}
