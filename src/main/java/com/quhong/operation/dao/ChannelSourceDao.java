package com.quhong.operation.dao;

import com.quhong.mysql.mapper.ustar.ChannelSourceMapper;
import com.quhong.operation.share.mysql.ChannelSourceData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ChannelSourceDao {

    private static final Logger logger = LoggerFactory.getLogger(ChannelSourceDao.class);

    @Autowired
    private ChannelSourceMapper channelSourceMapper;

    public List<ChannelSourceData> listAll(){
        return channelSourceMapper.selectList();
    }

    public ChannelSourceData getDataBySourceId(int sourceId){
        return channelSourceMapper.selectById(sourceId);
    }

}
