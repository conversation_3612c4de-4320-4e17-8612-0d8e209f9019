package com.quhong.operation.dao;

import com.quhong.datas.DayTimeData;
import com.quhong.mysql.dao.MonthShardingDao;
import com.quhong.mysql.slave_mapper.ustar_log.GiftSendRecordMapper;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.utils.MongoUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/8/3
 */
@Component
public class GiftSendRecordDao extends MonthShardingDao<GiftSendRecordMapper> {

    private final static Logger logger = LoggerFactory.getLogger(GiftSendRecordDao.class);

    @Autowired
    private GiftSendRecordMapper giftSendRecordMapper;

    public GiftSendRecordDao() {
        super("t_gift_send_record");
    }

    public Set<String> giftRookieRoomNewUsers(DayTimeData dayTimeData, Set<String> ridSet) {
        int startTime = dayTimeData.getTime();
        int endTime = dayTimeData.getEndTime();
        Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(dayTimeData.getDate());
        String[] uidRange = getUidWhereRange(time);
        logger.info("giftRookieRoomNewUsers startTime={} endTime={} startUid={} endUid={} ridSet={}",startTime, endTime,uidRange[0],uidRange[1],ridSet.size());
        List<String> tableSuffixList = DateHelper.ARABIAN.getTableSuffixArr(startTime, endTime);
        Set<String> uidSet = new HashSet<>();
        for (String tableSuffix : tableSuffixList) {
            if (checkExist(tableSuffix)) {
                List<String> uidList = giftSendRecordMapper.giftRookieRoomNewUsers(tableSuffix, ridSet,startTime*1000L, endTime*1000L,uidRange[0],uidRange[1]);
                if (!CollectionUtils.isEmpty(uidList)) {
                    uidSet.addAll(uidList);
                }
            }
        }
        return uidSet;
    }

    /**
     * 获取过滤的uid范围
     *
     * @param time 时间范围
     * @return uid范围
     */
    private String[] getUidWhereRange(Integer[] time) {
        String startUid = MongoUtils.create_idBySecond(time[0]);
        String endUid = MongoUtils.create_idBySecond(time[1]);
        return new String[]{startUid, endUid};
    }

}
