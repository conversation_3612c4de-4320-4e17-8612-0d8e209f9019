package com.quhong;

import com.quhong.config.BaseAppConfig;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableDubbo
@EnableFeignClients
@EnableScheduling
@EnableAsync
@ServletComponentScan
@ImportResource("classpath*:spring-context.xml")
@ImportAutoConfiguration({BaseAppConfig.class})
@SpringBootApplication(exclude = {RedisAutoConfiguration.class, MongoAutoConfiguration.class,
        ElasticsearchDataAutoConfiguration.class, DataSourceAutoConfiguration.class})
public class NoticeApplication {
    private static final Logger logger = LoggerFactory.getLogger(NoticeApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(NoticeApplication.class, args);
        logger.info("============= notice start ===============================");
    }
}
