package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR>
 * @date 2023/4/17
 */
@TableName("t_daily_task_award")
public class DailyTaskAwardData {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 对应taskId
     */
    private Integer taskId;

    /**
     * 奖励类型
     */
    private String stype;

    /**
     * 资源id
     */
    private Integer sourceId;

    /**
     * 数量还是天数 1 天数  2数量
     */
    private Integer atype;

    /**
     * 图标
     */
    private String icon;

    /**
     * 钻石title
     */
    private String title;

    /**
     * 奖励钻石时的atype
     */
    private Integer actType;

    /**
     * 奖励钻石时的描述
     */
    private String actDesc;

    /**
     * 奖励资源数
     */
    private Integer reward;

    /**
     * 是否有效
     */
    private Integer status;

    /**
     * 获取奖励时图标
     */
    private String awardIcon;

    /**
     * 获取奖励时标题
     */
    private String awardTitle;

    /**
     * 获取奖励时阿语标题
     */
    private String awardArtitle;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getStype() {
        return stype;
    }

    public void setStype(String stype) {
        this.stype = stype;
    }

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public Integer getAtype() {
        return atype;
    }

    public void setAtype(Integer atype) {
        this.atype = atype;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getActType() {
        return actType;
    }

    public void setActType(Integer actType) {
        this.actType = actType;
    }

    public String getActDesc() {
        return actDesc;
    }

    public void setActDesc(String actDesc) {
        this.actDesc = actDesc;
    }

    public Integer getReward() {
        return reward;
    }

    public void setReward(Integer reward) {
        this.reward = reward;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getAwardIcon() {
        return awardIcon;
    }

    public void setAwardIcon(String awardIcon) {
        this.awardIcon = awardIcon;
    }

    public String getAwardTitle() {
        return awardTitle;
    }

    public void setAwardTitle(String awardTitle) {
        this.awardTitle = awardTitle;
    }

    public String getAwardArtitle() {
        return awardArtitle;
    }

    public void setAwardArtitle(String awardArtitle) {
        this.awardArtitle = awardArtitle;
    }
}
