package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR>
 * @date 2023/4/14
 */
@TableName("t_daily_task")
public class DailyTaskData {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 英文描述
     */
    private String content;

    /**
     * 阿语描述
     */
    private String arContent;

    /**
     * 任务限定值
     */
    private Integer taskValue;

    /**
     * 奖励资源数
     */
    private Integer reward;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 跳转回调
     */
    private String jumpCall;

    /**
     * 一次性任务状态
     */
    private Integer onceStatus;

    /**
     * hot_task
     */
    private Integer hotTask;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getArContent() {
        return arContent;
    }

    public void setArContent(String arContent) {
        this.arContent = arContent;
    }

    public Integer getTaskValue() {
        return taskValue;
    }

    public void setTaskValue(Integer taskValue) {
        this.taskValue = taskValue;
    }

    public Integer getReward() {
        return reward;
    }

    public void setReward(Integer reward) {
        this.reward = reward;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getJumpCall() {
        return jumpCall;
    }

    public void setJumpCall(String jumpCall) {
        this.jumpCall = jumpCall;
    }

    public Integer getOnceStatus() {
        return onceStatus;
    }

    public void setOnceStatus(Integer onceStatus) {
        this.onceStatus = onceStatus;
    }

    public Integer getHotTask() {
        return hotTask;
    }

    public void setHotTask(Integer hotTask) {
        this.hotTask = hotTask;
    }
}
