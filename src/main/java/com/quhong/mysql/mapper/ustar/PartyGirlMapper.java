package com.quhong.mysql.mapper.ustar;

import com.quhong.operation.share.mysql.PartyGirl;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;


public interface PartyGirlMapper {

    /**
     * 清空party girl表数据
     */
    @Delete("delete from t_party_girl")
    void cleanUpData();

    @Insert("insert into t_party_girl (uid,rid,what_day) values (#{uid},#{rid},#{whatDay})")
    int insert(PartyGirl partyGirl);

}
