package com.quhong.mysql.slave_mapper.ustar;

import com.quhong.operation.share.mysql.AppleSub;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/18
 */
public interface AppleSubMapper {

    /**
     * 获取apple订阅记录
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @return 订阅记录
     */
    List<AppleSub> getAppleSubInfo(@Param("startTime") Long startTime,
                                   @Param("endTime") Long endTime);

}
