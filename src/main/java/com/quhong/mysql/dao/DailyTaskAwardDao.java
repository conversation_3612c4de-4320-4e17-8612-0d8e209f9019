package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.mysql.data.DailyTaskAwardData;
import com.quhong.mysql.mapper.ustar.DailyTaskAwardMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/17
 */
@Component
public class DailyTaskAwardDao {

    @Resource
    private DailyTaskAwardMapper dailyTaskAwardMapper;

    public List<DailyTaskAwardData> selectListByTaskId(Integer taskId) {
        QueryWrapper<DailyTaskAwardData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_id", taskId);
        queryWrapper.eq("status", 1);
        return dailyTaskAwardMapper.selectList(queryWrapper);
    }
}
