package com.quhong.mysql.dao;

import com.quhong.mysql.data.TurntablePlayerRecordData;
import com.quhong.mysql.mapper.ustar_log.TurntablePlayerRecordMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/31
 */
@Component
public class TurntablePlayerRecordDao {

    @Resource
    private TurntablePlayerRecordMapper turntablePlayerRecordMapper;

    public void insert(TurntablePlayerRecordData data) {
        turntablePlayerRecordMapper.insert(data);
    }

    public void batchInsert(List<TurntablePlayerRecordData> list) {
        turntablePlayerRecordMapper.batchInsert(list);
    }
}
