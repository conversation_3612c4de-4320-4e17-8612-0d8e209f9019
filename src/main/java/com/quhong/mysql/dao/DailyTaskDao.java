package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.mysql.data.DailyTaskData;
import com.quhong.mysql.mapper.ustar.DailyTaskMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/4/14
 */
@Component
public class DailyTaskDao {

    @Resource
    private DailyTaskMapper dailyTaskMapper;

    public DailyTaskData selectOne(int id) {
        QueryWrapper<DailyTaskData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        queryWrapper.eq("status", 1);
        return dailyTaskMapper.selectOne(queryWrapper);
    }

    public DailyTaskData selectOne(int id, Integer onceStatus, Integer status) {
        QueryWrapper<DailyTaskData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        if (onceStatus != null) {
            queryWrapper.eq("once_status", onceStatus);
        }
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        return dailyTaskMapper.selectOne(queryWrapper);
    }
}
