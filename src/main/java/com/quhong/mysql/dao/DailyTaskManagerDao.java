package com.quhong.mysql.dao;

import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.DailyTaskManager;
import com.quhong.mysql.mapper.ustar_log.DailyTaskManagerMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;



@Component
public class DailyTaskManagerDao extends MonthShardingDao<DailyTaskManagerMapper> {
    private static final Logger logger = LoggerFactory.getLogger(DailyTaskManagerDao.class);

    public DailyTaskManagerDao() {
        super("t_daily_task_record");
    }


    public DailyTaskManager getMissionTaskByUid(String uid, String dateKey) {
        try {
            long startTimeSec = DateHelper.DEFAULT.getTodayStartTime() / 1000;
            String tableSuffix = DateHelper.DEFAULT.getTableSuffix(DateHelper.formatDate(startTimeSec));
            createTable(tableSuffix);
            return tableMapper.getMissionTaskByUid(tableSuffix, uid, dateKey);
        } catch (Exception e) {
            logger.error("getMissionTaskByUid error. uid={} dateKey={}", uid, dateKey, e);
            return null;
        }
    }

    public DailyTaskManager getTaskById(String uid, String dateKey, Integer taskId) {
        try {
            long startTimeSec = DateHelper.DEFAULT.getTodayStartTime() / 1000;
            String tableSuffix = DateHelper.DEFAULT.getTableSuffix(DateHelper.formatDate(startTimeSec));
            createTable(tableSuffix);
            return tableMapper.getTaskById(tableSuffix, uid, dateKey, taskId);
        } catch (Exception e) {
            logger.error("getTaskById error. uid={} dateKey={} taskId={}", uid, dateKey, taskId, e);
            return null;
        }
    }

    public void insert(DailyTaskManager newRecord) {
        try {
            long startTimeSec = DateHelper.DEFAULT.getTodayStartTime() / 1000;
            String tableSuffix = DateHelper.DEFAULT.getTableSuffix(DateHelper.formatDate(startTimeSec));
            createTable(tableSuffix);
            tableMapper.insert(tableSuffix, newRecord);
        } catch (Exception e) {
            logger.error("insert data error. uid={} dateKey={} taskId={}", newRecord.getUid(), newRecord.getDateKey(), newRecord.getTaskId(), e);
        }
    }

    public void update(DailyTaskManager newRecord) {
        try {
            long startTimeSec = DateHelper.DEFAULT.getTodayStartTime() / 1000;
            String tableSuffix = DateHelper.DEFAULT.getTableSuffix(DateHelper.formatDate(startTimeSec));
            tableMapper.update(tableSuffix, newRecord);
        } catch (Exception e) {
            logger.error("insert data error. uid={} dateKey={} taskId={}", newRecord.getUid(), newRecord.getDateKey(), newRecord.getTaskId(), e);
        }
    }
}
