package com.quhong.config;

import com.quhong.data.ResourceConfigData;
import com.quhong.data.UpdateLevelData;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Component
@PropertySource(value = "classpath:user_exp.yml", encoding = "UTF-8", factory = YamlPropertySourceFactory.class)
@ConfigurationProperties(prefix = "user")
public class UserExpConfig {

    // 用户200级以内的经验值
    private List<Long> expList;
    // 用户等级英语H5明细页面
    private Map<String, List<UpdateLevelData>> updateLevelMapData;
    // 用户等级阿语H5明细页面
    private Map<String, List<UpdateLevelData>> updateLevelMapDataAr;
    // 用户等级勋章，用户等级页面展示
    private List<ResourceConfigData> levelBadges;
    // 聊天气泡，用户等级页面展示
    private List<ResourceConfigData> levelChatBubbles;
    // 用户等级奖励下发
    private Map<Integer, List<ResourceConfigData>> levelRewards;

    public List<Long> getExpList() {
        return new ArrayList<>(expList);
    }

    public List<Long> getUnsafeExpList() {
        return expList;
    }

    public void setExpList(List<Long> expList) {
        this.expList = expList;
    }

    public Map<String, List<UpdateLevelData>> getUpdateLevelMapData() {
        Map<String, List<UpdateLevelData>> map = new HashMap<>();
        for (Map.Entry<String, List<UpdateLevelData>> entry : updateLevelMapData.entrySet()) {
            List<UpdateLevelData> list = new ArrayList<>();
            for (UpdateLevelData source : entry.getValue()) {
                UpdateLevelData target = new UpdateLevelData();
                BeanUtils.copyProperties(source, target);
                list.add(target);
            }
            map.put(entry.getKey(), list);
        }
        return map;
    }

    public void setUpdateLevelMapData(Map<String, List<UpdateLevelData>> updateLevelMapData) {
        this.updateLevelMapData = updateLevelMapData;
    }

    public Map<String, List<UpdateLevelData>> getUpdateLevelMapDataAr() {
        Map<String, List<UpdateLevelData>> map = new HashMap<>();
        for (Map.Entry<String, List<UpdateLevelData>> entry : updateLevelMapDataAr.entrySet()) {
            List<UpdateLevelData> list = new ArrayList<>();
            for (UpdateLevelData source : entry.getValue()) {
                UpdateLevelData target = new UpdateLevelData();
                BeanUtils.copyProperties(source, target);
                list.add(target);
            }
            map.put(entry.getKey(), list);
        }
        return map;
    }

    public void setUpdateLevelMapDataAr(Map<String, List<UpdateLevelData>> updateLevelMapDataAr) {
        this.updateLevelMapDataAr = updateLevelMapDataAr;
    }

    public List<ResourceConfigData> getLevelBadges() {
        return levelBadges;
    }

    public List<ResourceConfigData> getLevelChatBubbles() {
        return levelChatBubbles;
    }

    public Map<Integer, List<ResourceConfigData>> getLevelRewards() {
        return levelRewards;
    }

    public void setLevelRewards(Map<Integer, List<ResourceConfigData>> levelRewards) {
        this.levelRewards = levelRewards;
        levelBadges = new ArrayList<>();
        levelChatBubbles = new ArrayList<>();
        for (List<ResourceConfigData> resourceConfigDataList : levelRewards.values()) {
            for (ResourceConfigData resourceConfigData : resourceConfigDataList) {
                if (resourceConfigData.getType().equals("badge")) {
                    levelBadges.add(resourceConfigData);
                } else if (resourceConfigData.getType().equals("buddle")) {
                    levelChatBubbles.add(resourceConfigData);
                }
            }
        }
    }
}
