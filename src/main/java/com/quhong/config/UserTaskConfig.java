package com.quhong.config;

import com.quhong.data.TaskInfo;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/20
 */
@Component
@PropertySource(value = "classpath:user_task_config.yml", encoding = "UTF-8", factory = YamlPropertySourceFactory.class)
@ConfigurationProperties(prefix = "config")
public class UserTaskConfig {

    /**
     * 新人任务
     */
    private List<TaskInfo> newcomerTasks;

    /**
     * 日常任务
     */
    private List<TaskInfo> dailyTasks;

    /**
     * 进阶任务
     */
    private List<TaskInfo> advancedTasks;

    public List<TaskInfo> getNewcomerTasks() {
        return newcomerTasks;
    }

    public void setNewcomerTasks(List<TaskInfo> newcomerTasks) {
        this.newcomerTasks = newcomerTasks;
    }

    public List<TaskInfo> getDailyTasks() {
        return dailyTasks;
    }

    public void setDailyTasks(List<TaskInfo> dailyTasks) {
        this.dailyTasks = dailyTasks;
    }

    public List<TaskInfo> getAdvancedTasks() {
        return advancedTasks;
    }

    public void setAdvancedTasks(List<TaskInfo> advancedTasks) {
        this.advancedTasks = advancedTasks;
    }
}
