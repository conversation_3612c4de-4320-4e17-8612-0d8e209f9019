package com.quhong.config;


import com.quhong.enums.CommonMqTaskConstant;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.RabbitListenerContainerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class HonorMqConfig {

    @Bean
    public Queue rechargeQueue() {
        return new Queue(FirstRechargeConstant.RECHARGE_QUEUE, true);
    }

    @Bean
    public Queue refundQueue() {
        return new Queue(FirstRechargeConstant.REFUND_QUEUE, true);
    }

    @Bean
    public Queue roomLevelQueue() {
        return new Queue(RoomLevelConstant.QUEUE, true);
    }

    @Bean
    public Queue userFriendQueue() {
        return new Queue(RoomLevelConstant.USER_FRIEND_QUEUE, true);
    }



    @Bean
    public TopicExchange topicCommonExchange() {
        return new TopicExchange(FirstRechargeConstant.TOPIC_EXCHANGE, false, false);
    }

    @Bean
    public TopicExchange taskCommonExchange() {
        return new TopicExchange(CommonMqTaskConstant.TASK_COMMON_EXCHANGE, false, false);
    }



    @Bean
    Binding bindingExchangeMessages(Queue rechargeQueue, TopicExchange topicCommonExchange) {
        return BindingBuilder.bind(rechargeQueue).to(topicCommonExchange).with(FirstRechargeConstant.RECHARGE_TOPIC_ROUTING_KEY);
    }

    @Bean
    Binding bindingExchangeRefundMessages(Queue refundQueue, TopicExchange topicCommonExchange) {
        return BindingBuilder.bind(refundQueue).to(topicCommonExchange).with(FirstRechargeConstant.REFUND_TOPIC_ROUTING_KEY);
    }

    @Bean
    Binding bindingRoomLevelExchange(Queue roomLevelQueue, TopicExchange taskCommonExchange) {
        return BindingBuilder.bind(roomLevelQueue).to(taskCommonExchange).with(RoomLevelConstant.ROOM_LEVEL_ROUTING_KEY);
    }

    @Bean
    Binding bindingUserFriendExchange(Queue userFriendQueue, TopicExchange taskCommonExchange) {
        return BindingBuilder.bind(userFriendQueue).to(taskCommonExchange).with(RoomLevelConstant.USER_FRIEND_ROUTING_KEY);
    }

}
