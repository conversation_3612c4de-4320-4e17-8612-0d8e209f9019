package com.quhong.config;

import com.quhong.core.config.ServerConfig;
import com.quhong.vo.TaskVO;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
@PropertySource(value = "classpath:task_turntable_config.yml", encoding = "UTF-8", factory = YamlPropertySourceFactory.class)
@ConfigurationProperties(prefix = "task.turntable")
public class TaskTurntableConfig {


    // 初级转盘
    private List<CommonAwardConfig> primaryTurntableList;

    // 高级转盘
    private List<CommonAwardConfig> advancedTurntableList;


    public static class CommonAwardConfig {
        private String drawType;
        private String iconEn;
        private String iconAr;
        private String nameEn;
        private String nameAr;
        private String numDayEn;
        private String numDayAr;
        private String rewardType;
        private Integer sourceProdId;
        private Integer sourceTestId;
        private Integer rewardTime;
        private Integer rewardNum;
        private Integer screen;
        private Integer rateNum;


        public String getDrawType() {
            return drawType;
        }

        public void setDrawType(String drawType) {
            this.drawType = drawType;
        }

        public String getIconEn() {
            return iconEn;
        }

        public void setIconEn(String iconEn) {
            this.iconEn = iconEn;
        }

        public String getIconAr() {
            return iconAr;
        }

        public void setIconAr(String iconAr) {
            this.iconAr = iconAr;
        }

        public String getNameEn() {
            return nameEn;
        }

        public void setNameEn(String nameEn) {
            this.nameEn = nameEn;
        }

        public String getNameAr() {
            return nameAr;
        }

        public void setNameAr(String nameAr) {
            this.nameAr = nameAr;
        }

        public String getNumDayEn() {
            return numDayEn;
        }

        public void setNumDayEn(String numDayEn) {
            this.numDayEn = numDayEn;
        }

        public String getNumDayAr() {
            return numDayAr;
        }

        public void setNumDayAr(String numDayAr) {
            this.numDayAr = numDayAr;
        }

        public String getRewardType() {
            return rewardType;
        }

        public void setRewardType(String rewardType) {
            this.rewardType = rewardType;
        }

        public Integer getSourceId(){
            if(ServerConfig.isProduct()) {
                return sourceProdId;
            } else {
                return sourceTestId;
            }
        }

        public Integer getSourceProdId() {
            return sourceProdId;
        }

        public void setSourceProdId(Integer sourceProdId) {
            this.sourceProdId = sourceProdId;
        }

        public Integer getSourceTestId() {
            return sourceTestId;
        }

        public void setSourceTestId(Integer sourceTestId) {
            this.sourceTestId = sourceTestId;
        }

        public Integer getRewardTime() {
            return rewardTime;
        }

        public void setRewardTime(Integer rewardTime) {
            this.rewardTime = rewardTime;
        }

        public Integer getRewardNum() {
            return rewardNum;
        }

        public void setRewardNum(Integer rewardNum) {
            this.rewardNum = rewardNum;
        }

        public Integer getScreen() {
            return screen;
        }

        public void setScreen(Integer screen) {
            this.screen = screen;
        }

        public Integer getRateNum() {
            return rateNum;
        }

        public void setRateNum(Integer rateNum) {
            this.rateNum = rateNum;
        }
    }


    public List<CommonAwardConfig> getPrimaryTurntableList() {
        return primaryTurntableList;
    }

    public void setPrimaryTurntableList(List<CommonAwardConfig> primaryTurntableList) {
        this.primaryTurntableList = primaryTurntableList;
    }

    public List<CommonAwardConfig> getAdvancedTurntableList() {
        return advancedTurntableList;
    }

    public void setAdvancedTurntableList(List<CommonAwardConfig> advancedTurntableList) {
        this.advancedTurntableList = advancedTurntableList;
    }
}
