//package com.quhong.config;
//
//import com.quhong.enums.CommonMqTaskConstant;
//import org.springframework.amqp.core.Binding;
//import org.springframework.amqp.core.BindingBuilder;
//import org.springframework.amqp.core.Queue;
//import org.springframework.amqp.core.TopicExchange;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//@Configuration
//public class FcmMqConfig {
//
//    /**
//     * 定义队列
//     */
//    @Bean
//    public Queue fcmQueue() {
//        return new Queue(CommonMqTaskConstant.FCM_MQ_MSG_QUEUE, true);
//    }
//
//    /**
//     * 定义交换机
//     */
//    @Bean
//    public TopicExchange fcmExchange() {
//        return new TopicExchange(CommonMqTaskConstant.TASK_COMMON_EXCHANGE, false, false);
//    }
//
//    /**
//     * 将队列以指定key绑定到交换机上
//     */
//    @Bean
//    Binding bindingExchangeFcm(Queue fcmQueue, TopicExchange fcmExchange) {
//        return BindingBuilder.bind(fcmQueue).to(fcmExchange).with(CommonMqTaskConstant.USER_TASK_ROUTING_KEY);
//    }
//}
