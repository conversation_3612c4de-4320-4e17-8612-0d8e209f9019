package com.quhong.config;

import com.quhong.vo.TaskVO;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
@PropertySource(value = "classpath:daily_task_center.yml", encoding = "UTF-8", factory = YamlPropertySourceFactory.class)
@ConfigurationProperties(prefix = "task")
public class DailyTaskCenterConfig {
    private TaskVO mission_task;
    private List<TaskVO> novice_task;
    private List<TaskVO> daily_task;
    private List<TaskVO> more_task;

    public TaskVO getMission_task() {
        return mission_task;
    }

    public void setMission_task(TaskVO mission_task) {
        this.mission_task = mission_task;
    }

    public List<TaskVO> getNovice_task() {
        return novice_task;
    }

    public void setNovice_task(List<TaskVO> novice_task) {
        this.novice_task = novice_task;
    }

    public List<TaskVO> getDaily_task() {
        return daily_task;
    }

    public void setDaily_task(List<TaskVO> daily_task) {
        this.daily_task = daily_task;
    }

    public List<TaskVO> getMore_task() {
        return more_task;
    }

    public void setMore_task(List<TaskVO> more_task) {
        this.more_task = more_task;
    }
}
