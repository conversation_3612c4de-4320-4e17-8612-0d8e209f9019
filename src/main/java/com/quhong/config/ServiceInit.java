package com.quhong.config;

import com.quhong.api.RoomListService;
import com.quhong.constant.ServiceConstant;
import com.quhong.init.DubboServiceInit;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;


@Component
public class ServiceInit implements DubboServiceInit {

    @DubboReference(lazy = true, providedBy = ServiceConstant.ROOM_LIST, connections = 2, reconnect = "false", retries = 0)
    private RoomListService roomListService;
}
