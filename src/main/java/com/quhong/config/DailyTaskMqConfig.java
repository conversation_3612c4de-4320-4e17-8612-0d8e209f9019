package com.quhong.config;

import com.quhong.dailyTask.DailyTaskService;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.RabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/4/14
 */
@Configuration
public class DailyTaskMqConfig {


    @Bean("dailyTaskListenerFactory")
    public RabbitListenerContainerFactory<SimpleMessageListenerContainer> diamondsListenerFactory(ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory containerFactory = new SimpleRabbitListenerContainerFactory();
        containerFactory.setConnectionFactory(connectionFactory);
        containerFactory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        containerFactory.setMaxConcurrentConsumers(2);
        containerFactory.setPrefetchCount(20);
        return containerFactory;
    }
}
