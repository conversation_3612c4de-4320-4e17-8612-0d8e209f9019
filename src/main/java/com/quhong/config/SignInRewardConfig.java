package com.quhong.config;

import com.quhong.data.SignBadgeInfo;
import com.quhong.data.SignInRewardInfo;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 签到奖励配置
 *
 * <AUTHOR>
 * @date 2022/10/8
 */

@Component
@PropertySource(value = "classpath:sign_in_reward.yml", encoding = "UTF-8", factory = YamlPropertySourceFactory.class)
@ConfigurationProperties(prefix = "config")
public class SignInRewardConfig {

    /**
     * 普通男用户
     */
    private Map<String, List<SignInRewardInfo>> rookieBagDict;

    /**
     * 高潜男用户
     */
    private Map<String, List<SignInRewardInfo>> rookieHighDiveMale;

    /**
     * 女性用户
     */
    private Map<String, List<SignInRewardInfo>> rookieFamaleDict;

    /**
     * 7天内签到的奖励
     */
    private Map<String, List<SignInRewardInfo>> signDayReward;

    private List<SignInRewardInfo> newUserSignReward;

    /**
     * 新普通男用户
     */
    private Map<String, List<SignInRewardInfo>> newRookieBagDict;

    /**
     * 新高潜男用户
     */
    private Map<String, List<SignInRewardInfo>> newRookieHighDiveMale;

    /**
     * 新女性用户
     */
    private Map<String, List<SignInRewardInfo>> newRookieFamaleDict;

    /**
     * 843新人礼包推荐房间配置项
     */
    private Map<String, Integer> newRookieConfigDict;

    private List<SignBadgeInfo> signBadgeInfoList;

    public Map<String, List<SignInRewardInfo>> getRookieBagDict() {
        return rookieBagDict;
    }

    public void setRookieBagDict(Map<String, List<SignInRewardInfo>> rookieBagDict) {
        this.rookieBagDict = rookieBagDict;
    }

    public Map<String, List<SignInRewardInfo>> getRookieHighDiveMale() {
        return rookieHighDiveMale;
    }

    public void setRookieHighDiveMale(Map<String, List<SignInRewardInfo>> rookieHighDiveMale) {
        this.rookieHighDiveMale = rookieHighDiveMale;
    }

    public Map<String, List<SignInRewardInfo>> getRookieFamaleDict() {
        return rookieFamaleDict;
    }

    public void setRookieFamaleDict(Map<String, List<SignInRewardInfo>> rookieFamaleDict) {
        this.rookieFamaleDict = rookieFamaleDict;
    }

    public Map<String, List<SignInRewardInfo>> getSignDayReward() {
        return signDayReward;
    }

    public void setSignDayReward(Map<String, List<SignInRewardInfo>> signDayReward) {
        this.signDayReward = signDayReward;
    }

    public List<SignInRewardInfo> getNewUserSignReward() {
        return newUserSignReward;
    }

    public void setNewUserSignReward(List<SignInRewardInfo> newUserSignReward) {
        this.newUserSignReward = newUserSignReward;
    }

    public Map<String, List<SignInRewardInfo>> getNewRookieBagDict() {
        return newRookieBagDict;
    }

    public void setNewRookieBagDict(Map<String, List<SignInRewardInfo>> newRookieBagDict) {
        this.newRookieBagDict = newRookieBagDict;
    }

    public Map<String, List<SignInRewardInfo>> getNewRookieHighDiveMale() {
        return newRookieHighDiveMale;
    }

    public void setNewRookieHighDiveMale(Map<String, List<SignInRewardInfo>> newRookieHighDiveMale) {
        this.newRookieHighDiveMale = newRookieHighDiveMale;
    }

    public Map<String, List<SignInRewardInfo>> getNewRookieFamaleDict() {
        return newRookieFamaleDict;
    }

    public void setNewRookieFamaleDict(Map<String, List<SignInRewardInfo>> newRookieFamaleDict) {
        this.newRookieFamaleDict = newRookieFamaleDict;
    }

    public Map<String, Integer> getNewRookieConfigDict() {
        return newRookieConfigDict;
    }

    public void setNewRookieConfigDict(Map<String, Integer> newRookieConfigDict) {
        this.newRookieConfigDict = newRookieConfigDict;
    }

    public List<SignBadgeInfo> getSignBadgeInfoList() {
        return signBadgeInfoList;
    }

    public void setSignBadgeInfoList(List<SignBadgeInfo> signBadgeInfoList) {
        this.signBadgeInfoList = signBadgeInfoList;
    }
}
