package com.quhong.config;

import com.quhong.vo.FamilyTaskInfoVO;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
@PropertySource(value = "classpath:family_task_config.yml", encoding = "UTF-8", factory = YamlPropertySourceFactory.class)
@ConfigurationProperties(prefix = "family")
public class FamilyTaskConfig {

    /**
     * 家族任务
     */
    private List<FamilyTaskInfoVO> familyTasks;

    public List<FamilyTaskInfoVO> getFamilyTasks() {
        return familyTasks;
    }

    public void setFamilyTasks(List<FamilyTaskInfoVO> familyTasks) {
        this.familyTasks = familyTasks;
    }
}
