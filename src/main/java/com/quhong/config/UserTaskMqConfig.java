package com.quhong.config;

import com.quhong.enums.CommonMqTaskConstant;
import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/9/21
 */
@Configuration
public class UserTaskMqConfig {

    @Bean
    public Queue userTaskQueue() {
        return new Queue(CommonMqTaskConstant.USER_TASK_QUEUE, true);
    }

    @Bean
    public TopicExchange userTaskExchange() {
        return new TopicExchange(CommonMqTaskConstant.TASK_COMMON_EXCHANGE, false, false);
    }

    @Bean
    Binding bindingExchangeUserTask(Queue userTaskQueue, TopicExchange userTaskExchange) {
        return BindingBuilder.bind(userTaskQueue).to(userTaskExchange).with(CommonMqTaskConstant.USER_TASK_ROUTING_KEY);
    }
}
