package com.quhong.config;

import com.quhong.enums.UserLevelConstant;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.FanoutExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class UserLevelMqConfig {

    @Bean
    public Queue userLevelQueue() {
        return new Queue(UserLevelConstant.QUEUE, true);
    }

    @Bean
    public FanoutExchange userLevelExchange() {
        return new FanoutExchange(UserLevelConstant.FOUNT_NAME, false, false);
    }

    @Bean
    Binding bindingExchange(Queue userLevelQueue, FanoutExchange userLevelExchange) {
        return BindingBuilder.bind(userLevelQueue).to(userLevelExchange);
    }

}
