package com.quhong.config;

import com.quhong.data.TaskInfo;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/20
 */
@Component
@PropertySource(value = "classpath:user_task_config_v2.yml", encoding = "UTF-8", factory = YamlPropertySourceFactory.class)
@ConfigurationProperties(prefix = "task-config")
public class UserTaskV2Config {

    /**
     * 新人任务
     */
    private List<TaskInfo> rookieTasks;

    /**
     * 日常任务
     */
    private List<TaskInfo> dailyTasks;

    public List<TaskInfo> getRookieTasks() {
        return rookieTasks;
    }

    public void setRookieTasks(List<TaskInfo> rookieTasks) {
        this.rookieTasks = rookieTasks;
    }

    public List<TaskInfo> getDailyTasks() {
        return dailyTasks;
    }

    public void setDailyTasks(List<TaskInfo> dailyTasks) {
        this.dailyTasks = dailyTasks;
    }
}
