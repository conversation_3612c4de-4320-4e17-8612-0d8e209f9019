package com.quhong.config;

import com.quhong.enums.CommonMqTaskConstant;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/9/21
 */
@Configuration
public class UserRoomRecommendMqConfig {

    @Bean
    public Queue userRoomRecommendQueue() {
        return new Queue(CommonMqTaskConstant.USER_ROOM_RECOMMEND_QUEUE, true);
    }

    @Bean
    public TopicExchange userRoomRecommendExchange() {
        return new TopicExchange(CommonMqTaskConstant.TASK_COMMON_EXCHANGE, false, false);
    }

    @Bean
    Binding bindingExchangeUserRoomRecommend(Queue userRoomRecommendQueue, TopicExchange userRoomRecommendExchange) {
        return BindingBuilder.bind(userRoomRecommendQueue).to(userRoomRecommendExchange).with(CommonMqTaskConstant.USER_TASK_ROUTING_KEY);
    }
}
