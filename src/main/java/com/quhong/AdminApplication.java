package com.quhong;

import com.quhong.config.BaseAppConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * BaseAppConfig 基础配置
 * NettyConfig 服务发现
 * TCPServerConfig 长连接配置
 * RoomMongoBean 连接room库
 */
@EnableFeignClients
@EnableScheduling
@EnableAsync
@ServletComponentScan
@ImportResource("classpath*:spring-context.xml")
@ImportAutoConfiguration({BaseAppConfig.class})
@SpringBootApplication(exclude = {RedisAutoConfiguration.class, MongoAutoConfiguration.class,
        ElasticsearchDataAutoConfiguration.class, DataSourceAutoConfiguration.class})
public class AdminApplication {
    private static final Logger logger = LoggerFactory.getLogger(AdminApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(AdminApplication.class, args);
        logger.info("============= admin service start ===============================");
    }
}
