package com.quhong.aws.clients;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@Component
public class AWSUploader {
    private static final Logger logger = LoggerFactory.getLogger(AWSUploader.class);

    private AWSClient awsClient;

    public AWSUploader(AWSClient client) {
        this.awsClient = client;
    }

    public String updateLoad(MultipartFile file, String path) {
        try {
            if(file != null && file.getSize() > 0){
                AmazonS3 s3 = this.awsClient.getAmazonS3();
                // Upload a file as a new object with ContentType and title specified.
                String bucketName = awsClient.getBucketName();
                // 设置存储路径
                logger.info("path={},original file name={}", path, file.getOriginalFilename());
                String s3FilePath = path + file.getOriginalFilename();
                logger.info("uploading s3 path={} to S3 bucket name={}", s3FilePath,bucketName);
                ObjectMetadata metadata = new ObjectMetadata();
                metadata.setContentType(file.getContentType());
                metadata.setContentLength(file.getSize());
                metadata.setHeader("x-amz-acl", "public-read");
                logger.info("begin s3 putObject uploading s3 content type={} size ={}",file.getContentType(),file.getSize());
                // Upload Beginning
                s3.putObject(bucketName, s3FilePath, file.getInputStream(), metadata);
                logger.info("finish s3 putObject uploading s3 content type={}",file.getContentType());
                // 获取一个requests
                GeneratePresignedUrlRequest urlRequest = new GeneratePresignedUrlRequest(bucketName, s3FilePath);
                logger.info("finish generate presigned url request uploading s3 path={}",s3FilePath);
                // 生成公用的url
                String url = s3.generatePresignedUrl(urlRequest).toString();
                if (url.contains("?")) {
                    url = url.substring(0, url.indexOf("?"));
                }
                logger.info("uploading {} to S3 bucket {} success.;url={};url length={}", s3FilePath, bucketName, url, url.length());
                return url;
            }
        } catch (IOException e) {
            logger.error("uploading failed {}", e.getMessage(), e);
        }
        return "";
    }
}
