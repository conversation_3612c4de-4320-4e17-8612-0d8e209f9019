package com.quhong.exceptions;

import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;

public class AdminCommonException extends CommonException {

    public AdminCommonException() {
    }

    public AdminCommonException(HttpCode httpCode) {
        this.httpCode = httpCode;
    }

    public AdminCommonException(int code, String msg) {
        httpCode = new HttpCode();
        httpCode.setCode(code);
        httpCode.setMsg(msg);
    }
}
