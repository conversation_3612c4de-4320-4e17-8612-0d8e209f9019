package com.quhong.controllers;

import com.quhong.core.config.ServerConfig;
import com.quhong.dto.DailyTaskDTO;
import com.quhong.enums.HttpCode;
import com.quhong.enums.UserHttpCode;
import com.quhong.handler.WebController;
import com.quhong.service.DailyTaskCenterService;
import com.quhong.service.UserLotteryTicketsService;
import com.quhong.utils.RequestUtils;
import com.quhong.vo.TaskListVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 任务中心
 *
 * <AUTHOR>
 * @date 2023/9/21
 */
@RestController
@RequestMapping("${baseUrl}")
public class DailyTaskController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(DailyTaskController.class);

    @Resource
    private DailyTaskCenterService dailyTaskCenterService;
    @Resource
    private UserLotteryTicketsService userLotteryTicketsService;

    /**
     * 日常任务中心
     */
    @PostMapping("dailyTasksCenter")
    public String dailyTasksCenter(HttpServletRequest request) {
        DailyTaskDTO req = RequestUtils.getSendData(request, DailyTaskDTO.class);
        logger.info("daily task center uid={}", req.getUid());
        return createResult(req, HttpCode.SUCCESS, dailyTaskCenterService.getDailyTasksCenter(req));
    }

    /**
     * 日常任务奖励领取(IOS)
     */
    @PostMapping("dailyTasksAward")
    public String dailyTasksAward(HttpServletRequest request) {
        DailyTaskDTO req = RequestUtils.getSendData(request, DailyTaskDTO.class);
        logger.info("daily task award uid={} taskId={}", req.getUid(), req.getTask_id());
        // return createResult(req, HttpCode.SUCCESS, dailyTaskCenterService.drawRewards(req).getList());
        return createResult(req, UserHttpCode.NEED_UPGRADE, null);
    }

    /**
     * 日常任务奖励领取(安卓)
     */
    @PostMapping("drawRewards")
    public String drawRewards(HttpServletRequest request) {
        DailyTaskDTO req = RequestUtils.getSendData(request, DailyTaskDTO.class);
        logger.info("draw reward. uid={} taskId={} switchNotice={}", req.getUid(), req.getTask_id(), req.getSwitch_notice());
        // return createResult(req, HttpCode.SUCCESS, dailyTaskCenterService.drawRewards(req));
        return createResult(req, UserHttpCode.NEED_UPGRADE, null);
    }

    /**
     * 任务列表
     */
    @PostMapping("taskList")
    public String taskList(HttpServletRequest request) {
        long timeMillis = System.currentTimeMillis();
        DailyTaskDTO req = RequestUtils.getSendData(request, DailyTaskDTO.class);
        logger.info("get task list. uid={}", req.getUid());
        TaskListVO vo = dailyTaskCenterService.taskList(req);
        logger.info("get task list. uid={} cost={}", req.getUid(), System.currentTimeMillis() - timeMillis);
        return createResultNoRef(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 领取奖励
     */
    @PostMapping("getTaskReward")
    public String getTaskReward(HttpServletRequest request) {
        DailyTaskDTO req = RequestUtils.getSendData(request, DailyTaskDTO.class);
        logger.info("get task reward. uid={} taskKey={}", req.getUid(), req.getTaskKey());
        return createResult(req, HttpCode.SUCCESS, dailyTaskCenterService.getTaskReward(req));
    }

    /**
     * 测试服增加抽奖卷
     */
    @PostMapping("addTicketNum")
    public String addTicketNum(HttpServletRequest request) {
        DailyTaskDTO req = RequestUtils.getSendData(request, DailyTaskDTO.class);
        logger.info("addTicketNum test. uid={} nul={}", req.getUid(), req.getNum());
        if (ServerConfig.isProduct()) {
            return createResult(req, HttpCode.PARAM_ERROR, null);
        }
        userLotteryTicketsService.addTicketsNum(req.getUid(), req.getNum(), "test");
        return createResult(req, HttpCode.SUCCESS, null);
    }
}
