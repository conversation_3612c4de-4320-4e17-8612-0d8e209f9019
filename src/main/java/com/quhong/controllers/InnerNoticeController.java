//package com.quhong.controllers;
//
//import com.quhong.data.dto.SendFcmDTO;
//import com.quhong.datas.HttpResult;
//import com.quhong.handler.BaseController;
//import com.quhong.service.FCMPushService;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.http.MediaType;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//
//@RestController
//@RequestMapping(value = "inner/notice/", produces = MediaType.APPLICATION_JSON_VALUE)
//public class InnerNoticeController extends BaseController {
//    private static final Logger logger = LoggerFactory.getLogger(InnerNoticeController.class);
//
//    @Resource
//    private FCMPushService fcmPushService;
//
//
//    /**
//     * 通过toUid 或 toUidSet发送fcm消息
//     * 会查询actor表
//     */
//    @RequestMapping("send_fcm_msg")
//    public HttpResult<?> sendFcmMsg(@RequestBody SendFcmDTO dto) {
//        logger.info("pre inner send fcm msg. {} {}", dto, dto.getRequestId());
//        fcmPushService.sendFcmMsgByUidSet(dto);
//        return HttpResult.getOk();
//    }
//
//
//}
