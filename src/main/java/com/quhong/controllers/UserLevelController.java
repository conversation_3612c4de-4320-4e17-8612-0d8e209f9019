package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.config.UserExpConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ResourceConfigData;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.enums.UserLevelConstant;
import com.quhong.handler.HttpEnvData;
import com.quhong.handler.WebController;
import com.quhong.mongo.dao.UserLevelDao;
import com.quhong.mongo.data.UserLevelData;
import com.quhong.mysql.data.UserExpDetailData;
import com.quhong.service.UserExpDetailDao;
import com.quhong.service.UserLevelService;
import com.quhong.utils.RequestUtils;
import com.quhong.vo.ResourceVo;
import com.quhong.vo.UserLevelDetailVO;
import com.quhong.vo.UserLevelVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;


@RestController
@RequestMapping("${baseUrl}")
public class UserLevelController extends WebController {
    private static final Logger logger = LoggerFactory.getLogger(UserLevelController.class);
    private static String LEVEL_URL = "https://static.youstar.live/upgrade_level/";

    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private UserExpDetailDao userExpDetailDao;
    @Resource
    private UserExpConfig userExpConfig;
    @Resource
    private UserLevelService userLevelService;

    private static final String DESC = "Youstar Loyalist";
    private static final String DESC_AR = "مؤيد مخلص اليوستار";

    @PostConstruct
    private void postInit() {
        if (ServerConfig.isNotProduct()) {
            LEVEL_URL = "https://test2.qmovies.tv/upgrade_level/";
        }
    }

    /**
     * 获取用户等级
     */
    @PostMapping("user_level")
    public String userLevel(HttpServletRequest request) {
        JSONObject paramObj = RequestUtils.getSendData(request);
        HttpEnvData envData = HttpEnvData.create(paramObj);
        logger.info("user level uid={}", envData.getUid());
        UserLevelData userLevelData = userLevelDao.getUserLevelData(envData.getUid());
        if (null == userLevelData) {
            return createError(envData, HttpCode.PARAM_ERROR);
        }
        String todayStr = DateHelper.DEFAULT.formatDateInDay(new Date());
        UserExpDetailData detail = userExpDetailDao.getUserExpDetail(userLevelData.getUid(), todayStr);
        List<Long> expList = userExpConfig.getUnsafeExpList();
        UserLevelVo vo = new UserLevelVo();
        vo.setUid(userLevelData.getUid());
        int level = userLevelData.getLevel();
        vo.setLevel(level);
        int nextLevel = level + 1;
        if (nextLevel == expList.size()) {
            logger.error("user level close to full level uid={}", envData.getUid());
        }
        long curLevelExp = expList.get(userLevelData.getLevel());
        long newExp = userLevelData.getNew_exp();
        long activeExp = userLevelData.getActive_exp();
        long charmExp = userLevelData.getCharm_exp();
        long wealthExp = userLevelData.getWealth_exp();
        long levelUpExp;
        if (userLevelData.getLevel() + 1 >= expList.size()) {
            levelUpExp = newExp;
        } else {
            levelUpExp = expList.get(userLevelData.getLevel() + 1);
        }

        long[] arr = new long[]{activeExp, charmExp, wealthExp};
        vo.setExp(newExp);
        vo.setExpRate(getPercentValue(arr));
        vo.setLevelRate(roundHalfUpRateDiv(newExp - curLevelExp, levelUpExp - curLevelExp));
        vo.setActiveExp(activeExp);
        vo.setCharmExp(charmExp);
        vo.setWealthExp(wealthExp);
        vo.setNextLevel(nextLevel);
        vo.setCurLevelExp(curLevelExp);
        vo.setLevelUpExp(levelUpExp);
        vo.setLevelUpNeedExp(levelUpExp - newExp);
        vo.setTodayExp(null == detail ? 0 : detail.getToday_exp());
        vo.setLevelUrl(LEVEL_URL);

        vo.setLevelBadgeList(getBadgeList(level, vo.getUid(), userExpConfig.getLevelBadges()));
        vo.setLevelChatBubbleList(getChatBubbleList(level, userExpConfig.getLevelChatBubbles()));
        return createResult(envData, HttpCode.SUCCESS, vo);
    }

    private List<ResourceVo> getBadgeList(int level, String uid, List<ResourceConfigData> list) {
        List<ResourceVo> resourceVos = new ArrayList<>();
        ResourceVo resourceVo;
        int slang = userLevelService.getSlang(uid);
        for (ResourceConfigData data : list) {
            resourceVo = new ResourceVo();
            resourceVo.setName(data.getDesc());
            resourceVo.setIcon(data.getIcon());
            if (level >= data.getLevel()) {
                resourceVo.setStatus(1);
            }
            if (slang == SLangType.ARABIC) {
                resourceVo.setDesc(DESC_AR);
            } else {
                resourceVo.setDesc(DESC);
            }
            resourceVos.add(resourceVo);
        }
        return resourceVos;
    }

    private List<ResourceVo> getChatBubbleList(int level, Collection<ResourceConfigData> list) {
        List<ResourceVo> resourceVos = new ArrayList<>();
        ResourceVo resourceVo;
        for (ResourceConfigData data : list) {
            resourceVo = new ResourceVo();
            resourceVo.setIcon(data.getIcon());
            resourceVo.setStartColor(data.getStartColor());
            resourceVo.setEndColor(data.getEndColor());
            resourceVo.setName(data.getDesc());
            if (level >= data.getLevel()) {
                resourceVo.setStatus(1);
            }
            resourceVos.add(resourceVo);
        }
        return resourceVos;
    }

    /**
     * 用户等级信息
     */
    @PostMapping("level_detail")
    public String levelDetail(HttpServletRequest request) {
        JSONObject paramObj = RequestUtils.getSendDataFromCache(request);
        HttpEnvData envData = HttpEnvData.create(paramObj);
        logger.info("user level detail uid={}", envData.getUid());
        UserLevelData userLevelData = userLevelDao.getUserLevelData(envData.getUid());
        if (null == userLevelData) {
            return createError(envData, HttpCode.PARAM_ERROR);
        }
        List<Long> expList = userExpConfig.getUnsafeExpList();
        long newExp = userLevelData.getNew_exp();
        long levelUpExp;
        if (userLevelData.getLevel() + 1 >= expList.size()) {
            levelUpExp = newExp;
        } else {
            levelUpExp = expList.get(userLevelData.getLevel() + 1);
        }
        UserLevelDetailVO vo = new UserLevelDetailVO();
        vo.setLevel(userLevelData.getLevel());
        vo.setExp(newExp);
        vo.setCurLevelExp(expList.get(userLevelData.getLevel()));
        vo.setLevelUpExp(levelUpExp);
        vo.setLevelUpNeedExp(levelUpExp - newExp);
        vo.setPer(UserLevelConstant.CONSUME_PER);
        vo.setPoints(UserLevelConstant.CONSUME_POINT);
        return createResult(envData, HttpCode.SUCCESS, vo);
    }

    /**
     * 获取用户等级-H5访问
     */
    @GetMapping("update_level")
    public String updateLevel(String uid, Integer slang) {
        logger.info("get update level uid={} slang={}", uid, slang);
        if (null == slang) {
            slang = SLangType.ARABIC;
        }
        try {
            return createResult(HttpCode.SUCCESS, userLevelService.getUpdateLevel(uid, slang));
        } catch (Exception e) {
            logger.error("get update level data error uid={}", uid);
            return createResult(HttpCode.SERVER_ERROR, null);
        }
    }

    private double roundHalfUpRateDiv(long divisor, long dividend) {
        if (divisor == 0 || dividend == 0) {
            return 0;
        }
        return BigDecimal.valueOf((double) divisor / dividend * 100)
                .setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    public double[] getPercentValue(long[] arr) {
        // 求和
        double sum = 0;
        for (long j : arr) {
            sum += j;
        }
        // 10的2次幂是100，用于计算精度。
        double digits = 1;
        // 扩大比例100
        double[] votesPerQuota = new double[arr.length];
        for (int i = 0; i < arr.length; i++) {
            double val = arr[i] / sum * digits * 100;
            votesPerQuota[i] = val;
        }
        // 总数,扩大比例意味的总数要扩大
        double targetSeats = digits * 100;
        //再向下取值，组成数组
        double[] seats = new double[arr.length];
        for (int i = 0; i < votesPerQuota.length; i++) {
            seats[i] = Math.floor(votesPerQuota[i]);
        }
        // 再新计算合计，用于判断与总数量是否相同,相同则占比会100%
        double currentSum = 0;
        for (double seat : seats) {
            currentSum += seat;
        }
        // 余数部分的数组:原先数组减去向下取值的数组,得到余数部分的数组
        double[] remainder = new double[arr.length];
        for (int i = 0; i < seats.length; i++) {
            remainder[i] = votesPerQuota[i] - seats[i];
        }
        while (currentSum < targetSeats) {
            double max = 0;
            int maxId = 0;
            for (int i = 0; i < remainder.length; ++i) {
                if (remainder[i] > max) {
                    max = remainder[i];
                    maxId = i;
                }
            }
            // 对最大项余额加1
            ++seats[maxId];
            // 已经增加最大余数加1,则下次判断就可以不需要再判断这个余额数。
            remainder[maxId] = 0;
            // 总的也要加1,为了判断是否总数是否相同,跳出循环。
            ++currentSum;
        }
        // 处理NaN值，ios会报错
        for (int i = 0; i < seats.length; i++) {
            if (new Double(seats[i]).isNaN()) {
                seats[i] = 0;
            }
        }
        // 这时候的seats就会总数占比会100%
        return seats;
    }

}
