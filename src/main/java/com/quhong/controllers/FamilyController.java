package com.quhong.controllers;

import com.quhong.datas.HttpResult;
import com.quhong.dto.FamilyDTO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.HttpEnvData;
import com.quhong.service.FamilyService;
import com.quhong.vo.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 用户模块/家族
 */
@RestController
@RequestMapping("/user/family/")
public class FamilyController {

    private static final Logger logger = LoggerFactory.getLogger(FamilyController.class);

    @Resource
    private FamilyService familyService;


    /**
     * 创建家族
     */
    @RequestMapping("createFamily")
    private HttpResult<FamilyUpdateVO> createFamily(@RequestBody FamilyDTO.UpdateInfo dto) {
        logger.info("createFamily uid={} dto={}", dto.getUid(), dto);
        return HttpResult.getOk(familyService.createFamily(dto));
    }

    /**
     * 家族广场
     */
    @RequestMapping("familySquare")
    private HttpResult<FamilyListVO> familySquare(@RequestBody FamilyDTO.Square dto) {
        return HttpResult.getOk(familyService.familySquare(dto));
    }

    /**
     * 家族信息更新
     */
    @RequestMapping("updateFamilyInfo")
    private HttpResult<FamilyUpdateVO> updateInfo(@RequestBody FamilyDTO.UpdateInfo dto) {
        logger.info("updateFamilyInfo uid={} dto={}", dto.getUid(), dto);
        return HttpResult.getOk(familyService.updateInfo(dto));
    }

    /**
     * 家族信息
     */
    @RequestMapping("manageInfo")
    private HttpResult<ManageInfoVO> manageInfo(@RequestBody FamilyDTO dto) {
        logger.info("manageInfo uid={} dto={}", dto.getUid(), dto);
        return HttpResult.getOk(familyService.manageInfo(dto));
    }

    /**
     * 家族签到
     */
    @RequestMapping("familyCheck")
    private HttpResult<FamilyUniVO.FamilyTaskVO> familyCheck(@RequestBody HttpEnvData dto) {
        logger.info("familyCheck uid={}", dto.getUid());
        return HttpResult.getOk(familyService.familyCheck(dto.getUid()));
    }

    /**
     * 家族详情页
     * 家族基本资料
     */
    @RequestMapping("familyHome")
    private HttpResult<FamilyUniVO.FamilyHomeVO> familyHome(@RequestBody FamilyDTO dto) {
        logger.info("familyHome uid={} dto={}", dto.getUid(), dto);
        return HttpResult.getOk(familyService.familyHome(dto.getFamilyRid(), dto.getUid()));
    }

    /**
     * 家族任务
     */
    @RequestMapping("familyTask")
    private HttpResult<FamilyUniVO.FamilyTaskVO> familyTask(@RequestBody HttpEnvData dto) {
        logger.info("familyTask uid={}", dto.getUid());
        return HttpResult.getOk(familyService.familyTask(dto.getUid()));
    }

    /**
     * 家族资料
     * 家族贡献榜单、成员、及房间列表
     */
    @RequestMapping("familyProfile")
    private HttpResult<FamilyUniVO.FamilyProfileVO> familyProfile(@RequestBody FamilyDTO dto) {
        logger.info("familyProfile uid={} dto={}", dto.getUid(), dto);
        return HttpResult.getOk(familyService.familyProfile(dto.getFamilyRid()));
    }

    /**
     * 家族成员列表
     */
    @RequestMapping("getFamilyMembers")
    private HttpResult<FamilyMembersPageVO> getFamilyMembers(@RequestBody FamilyDTO.Members dto) {
        logger.info("getFamilyMembers uid={} dto={}", dto.getUid(), dto);
        return HttpResult.getOk(familyService.getFamilyMembers(dto));
    }

    /**
     * 申请加入家族
     */
    @RequestMapping("requestFamily")
    private HttpResult<Object> familyRequest(@RequestBody FamilyDTO.Request dto) {
        logger.info("familyRequest familyRid={} uid={} aid={}", dto.getFamilyRid(), dto.getUid(), dto.getAid());
        if (null != dto.getContent() && dto.getContent().length() > 120) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        return HttpResult.getOk(familyService.familyRequest(dto));
    }

    /**
     * 家族管理 - 家族申请列表
     */
    @RequestMapping("requestList")
    private HttpResult<FamilyRequestPageVO> familyRequestList(@RequestBody FamilyDTO.RequestList dto) {
        logger.info("familyRequest familyRid={} uid={} type={}", dto.getFamilyRid(), dto.getUid(), dto.getType());
        return HttpResult.getOk(familyService.familyRequestList(dto));
    }

    /**
     * 家族申请列表审批
     */
    @RequestMapping("approvalFamily")
    private HttpResult<Object> familyApproval(@RequestBody FamilyDTO.Approval dto) {
        logger.info("familyApproval familyRid={} uid={} approvalType={} type={}",
                dto.getFamilyRid(), dto.getUid(), dto.getApprovalType(), dto.getType());
        return HttpResult.getOk(familyService.familyApproval(dto));
    }

    /**
     * 家族管理 -家族成员管理
     */
    @RequestMapping("memberOperateFamily")
    private HttpResult<Object> familyMemberOperate(@RequestBody FamilyDTO.Operate dto) {
        logger.info("familyMemberOperate familyRid={} uid={} operateType={}", dto.getFamilyRid(), dto.getUid(), dto.getOperateType());
        return HttpResult.getOk(familyService.familyMemberOperate(dto));
    }

    /**
     * 家族管理 -退出家族，解散家族
     */
    @RequestMapping("familyQuit")
    private HttpResult<ManageInfoVO> familyQuit(@RequestBody FamilyDTO.Quit dto) {
        logger.info("familyQuit familyRid={} uid={} operateType={}", dto.getFamilyRid(), dto.getUid(), dto.getOperateType());
        return HttpResult.getOk(familyService.familyQuit(dto));
    }
}
