package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.dto.TaskTurntableDTO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.TaskTurntableService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 任务中心转盘
 */
@RestController
@RequestMapping("${baseUrl}")
public class TaskTurntableController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(TaskTurntableController.class);

    @Resource
    private TaskTurntableService taskTurntableService;

    /**
     * 转盘配置
     */
    @RequestMapping("taskTurntableConfig")
    public String taskTurntableConfig(@RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, taskTurntableService.taskTurntableConfig(uid));
    }

    /**
     * 转盘抽奖
     */
    @RequestMapping("taskTurntableDraw")
    public String taskTurntableDraw(@RequestBody TaskTurntableDTO dto) {

        logger.info("taskTurntableDraw dto: {}", JSONObject.toJSONString(dto));
        return createResult(HttpCode.SUCCESS, taskTurntableService.taskTurntableDraw(dto));
    }

    /**
     * 转盘抽奖记录
     */
    @RequestMapping("taskTurntableRecord")
    public String taskTurntableRecord(@RequestParam String uid, @RequestParam(required = false, defaultValue = "1") int page, @RequestParam String turntableType) {
        logger.info("taskTurntableDraw uid: {}, page:{}, turntableType:{}", uid, page, turntableType);
        return createResult(HttpCode.SUCCESS, taskTurntableService.taskTurntableRecord(uid, page, turntableType));
    }

}
