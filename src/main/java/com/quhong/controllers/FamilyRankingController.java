package com.quhong.controllers;

import com.quhong.datas.HttpResult;
import com.quhong.dto.FamilyDTO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.service.FamilyRankService;
import com.quhong.vo.FamilyRankingVO;
import com.quhong.vo.MemberRankingVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 用户模块/家族排行榜
 */
@RestController
@RequestMapping("/user/family/")
public class FamilyRankingController {

    private static final Logger logger = LoggerFactory.getLogger(FamilyRankingController.class);

    @Resource
    private FamilyRankService familyRankService;


    /**
     * 家族贡献排行榜
     */
    @RequestMapping("familyRanking")
    private HttpResult<FamilyRankingVO> familyRanking(@RequestBody FamilyDTO.Ranking dto) {
        logger.info("familyRanking uid={} startDate={} endDate={}", dto.getUid(), dto.getStartDate(), dto.getEndDate());
        if (ObjectUtils.isEmpty(dto.getStartDate()) || ObjectUtils.isEmpty(dto.getEndDate())) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        return HttpResult.getOk(familyRankService.familyRanking(dto.getUid(), dto.getStartDate(), dto.getEndDate(), dto.getSlang()));
    }

    /**
     * 家族成员贡献排行榜
     */
    @RequestMapping("familyMemberRanking")
    private HttpResult<MemberRankingVO> familyMemberRanking(@RequestBody FamilyDTO.Ranking dto) {
        logger.info("memberRanking familyRid={} uid={} rankType={}", dto.getFamilyRid(), dto.getUid(), dto.getRankType());
        if (ObjectUtils.isEmpty(dto.getRankType())) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        return HttpResult.getOk(familyRankService.memberRanking(dto.getUid(), dto.getFamilyRid(), dto.getRankType()));
    }

}
