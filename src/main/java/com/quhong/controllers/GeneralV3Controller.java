package com.quhong.controllers;


import com.alibaba.fastjson.JSONObject;
import com.quhong.data.dto.PainterPictureDTO;
import com.quhong.data.dto.QuizActivityDTO;
import com.quhong.data.dto.ShareActivityDTO;
import com.quhong.data.vo.*;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.*;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 活动接口
 */


@RestController
@RequestMapping(value = "/activity/", produces = MediaType.APPLICATION_JSON_VALUE)
public class GeneralV3Controller extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(GeneralV3Controller.class);

    @Resource
    private DragonTrainService dragonTrainService;
    @Resource
    private GreedyElephantService greedyElephantService;
    @Resource
    private ActiveBadgeService activeBadgeService;
    @Resource
    private LimitedRechargeBenefitService limitedRechargeBenefitService;
    @Resource
    private LuckyGlovesService luckyGlovesService;
    @Resource
    private MBTIService mbtiService;
    @Resource
    private RoomDataCenterService roomDataCenterService;
    @Resource
    private MatchQuizService matchQuizService;
    @Resource
    private MissContestV3Service missContestV3Service;
    @Resource
    private HappySaudiNationalService happySaudiNationalService;
    /**
     * 组队驯龙配置
     */
    @RequestMapping("dragonTrainConfig")
    public HttpResult<DragonTrainVO> dragonTrainConfig(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("dragonTrainConfig activityId: {}, uid:{}", activityId, uid);
        return HttpResult.getOk(dragonTrainService.dragonTrainConfig(activityId, uid));
    }

    /**
     * 驯龙团队榜单
     */
    @RequestMapping("dragonTrainTeamRank")
    public HttpResult<DragonTrainVO> dragonTrainTeamRank(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("dragonTrainTeamRank activityId: {}, uid:{}", activityId, uid);
        return HttpResult.getOk(dragonTrainService.dragonTrainTeamRank(activityId, uid));
    }

    /**
     * 驯龙抽奖
     */
    @RequestMapping("dragonTrainDraw")
    public HttpResult<DragonTrainVO> dragonTrainDraw(@RequestParam String activityId, @RequestParam String uid, @RequestParam int amount) {
        logger.info("dragonTrainDraw activityId: {}, uid:{}, amount={}", activityId, uid, amount);
        return HttpResult.getOk(dragonTrainService.dragonTrainDraw(activityId, uid, amount));
    }

    /**
     * 驯龙抽奖记录
     */
    @RequestMapping("dragonTrainRecord")
    public HttpResult<DragonTrainVO> dragonTrainRecord(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        logger.info("dragonTrainRecord activityId:{}, uid: {}, page:{}", activityId, uid, page);
        return HttpResult.getOk(dragonTrainService.dragonTrainRecord(activityId, uid, page));
    }


    /**
     * 驯龙加入组队
     */
    @RequestMapping("dragonTrainJoinTeam")
    public HttpResult<?> dragonTrainJoinTeam(@RequestParam String activityId, @RequestParam String uid, @RequestParam String captainUid) {
        logger.info("dragonTrainJoinTeam activityId:{}, uid: {}, captainUid:{}", activityId, uid, captainUid);
        dragonTrainService.dragonTrainJoinTeam(activityId, uid, captainUid);
        return HttpResult.getOk();
    }

    /**
     * 驯龙分享给好友
     */
    @RequestMapping("dragonTrainShare")
    public HttpResult<?> dragonTrainShare(@RequestBody ShareActivityDTO dto) {
        logger.info("dragonTrainShare dto:{} ", JSONObject.toJSONString(dto));
        dragonTrainService.dragonTrainShare(dto);
        return HttpResult.getOk();
    }

    /**
     * 贪婪的大象配置
     */
    @RequestMapping("greedyElephantConfig")
    public HttpResult<GreedyElephantVO> greedyElephantConfig(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("greedyElephantConfig activityId: {}, uid:{}", activityId, uid);
        return HttpResult.getOk(greedyElephantService.greedyElephantConfig(activityId, uid));
    }

    /**
     * 活跃等级勋章
     */
    @RequestMapping("activeBadgeConfig")
    public HttpResult<ActiveBadgeVO> activeBadgeConfig(@RequestParam String uid) {
        logger.info("activeBadgeInfo, uid:{}", uid);
        return HttpResult.getOk(activeBadgeService.getActiveBadgeInfo(uid));
    }


    /**
     * 限时充值福利
     */
    @RequestMapping("limitedRechargeConfig")
    public HttpResult<LimitedRechargeVO> limitedRechargeConfig(@RequestParam String activityId, @RequestParam String uid) {
        return HttpResult.getOk(limitedRechargeBenefitService.limitedRechargeConfig(activityId, uid));
    }

    /**
     * 拳套幸运礼物
     */
    @RequestMapping("luckyGlovesConfig")
    public HttpResult<LuckyGlovesVO> luckyGlovesConfig(@RequestParam String activityId, @RequestParam String uid) {
        return HttpResult.getOk(luckyGlovesService.luckyGlovesConfig(activityId, uid));
    }

    /**
     * 获取MBTI题目列表
     */
    @RequestMapping("getMBTIQuestionList")
    public HttpResult<MBTIQuizQuestionVO> getMBTIQuestionList(@RequestParam String activityId, @RequestParam String uid) {
        return HttpResult.getOk(mbtiService.getMBTIQuestionList(activityId, uid));
    }

    /**
     * 提交答题回答
     */
    @RequestMapping("submitMBTIAnswer")
    public HttpResult<MBTIVO> submitMBTIAnswer(@RequestParam String activityId, @RequestParam String uid, @RequestBody QuizActivityDTO dto) {
        return HttpResult.getOk(mbtiService.submitAnswer(activityId, uid, dto));
    }

    /**
     * 海报分享朋友圈
     */
    @RequestMapping("pictureMBTIPush")
    public HttpResult<?> pictureMBTIPush(@RequestParam String activityId, @RequestParam String uid, @RequestBody PainterPictureDTO dto) {
        mbtiService.pictureMBTIPush(activityId, uid, dto);
        return HttpResult.getOk();
    }

    /**
     * 房间数据页统计
     */
    @RequestMapping("roomDataCenterInfo")
    public HttpResult<RoomDataCenterVO> roomDataCenterInfo(@RequestParam String activityId, @RequestParam String uid, @RequestParam String dayStr) {
        return HttpResult.getOk(roomDataCenterService.roomDataCenterInfo(activityId, uid, dayStr));
    }

    /**
     * 默契挑战题目列表
     */
    @RequestMapping("matchQuizQuestionList")
    public HttpResult<QuizQuestionVO> getQuestionList(@RequestParam String activityId, @RequestParam String uid, @RequestParam(required = false) String aid) {
        return HttpResult.getOk(matchQuizService.getQuestionList(activityId, uid, aid));
    }

    /**
     * 提交答题回答
     */
    @RequestMapping("matchQuizSubmitAnswer")
    public HttpResult<MatchQuizVO> submitAnswer(@RequestParam String activityId, @RequestParam String uid, @RequestParam(required = false) String aid, @RequestBody QuizActivityDTO dto) {
        return HttpResult.getOk(matchQuizService.submitAnswer(activityId, uid, aid, dto));
    }

    /**
     * 默契挑战基本信息
     */
    @RequestMapping("matchQuizConfig")
    public HttpResult<MatchQuizVO> matchQuizConfig(@RequestParam String activityId, @RequestParam String uid, @RequestParam(required = false) String aid) {
        return HttpResult.getOk(matchQuizService.matchQuizConfig(activityId, uid,aid));
    }

    /**
     * 分享活动链接给好友-(主要url后面带shareAid)
     */
    @RequestMapping("shareToPrivateMsgByAid")
    public HttpResult<?> shareToPrivateMsgByAid(@RequestParam String activityId, @RequestBody ShareActivityDTO dto) {
        matchQuizService.shareToPrivateMsgByAid(dto);
        return HttpResult.getOk();
    }

    /**
     * 通用加好友
     */
    @RequestMapping("commonAddFriendApply")
    public HttpResult<?> commonAddFriendApply(@RequestParam String activityId, @RequestParam String uid,
                                              @RequestParam String aid,@RequestParam String msg) {
        matchQuizService.addFriendApply(activityId, uid, aid, msg);
        return HttpResult.getOk();
    }

    /**
     * 2025YOU STAR 玫瑰小姐活动
     * missType 0-玫瑰小姐榜 1-骑士榜
     */
    @RequestMapping("missContestConfig2025")
    private HttpResult<MissContestVO> missContestConfig2025(@RequestParam String activityId, @RequestParam String uid, @RequestParam int missType) {
        return HttpResult.getOk(missContestV3Service.missContestConfig(activityId, uid, missType));
    }


    @RequestMapping("isFirstEnterActivity")
    public HttpResult<OtherRankConfigVO> isFirstEnterActivity(@RequestParam String activityId, @RequestParam String uid) {
        return HttpResult.getOk(matchQuizService.isFirstEnterActivity(activityId,uid));
    }

    /**
     * 沙特国庆活动2025
     */

    @RequestMapping("happySaudiConfig")
    public HttpResult<HappySaudiVO> happySaudiConfig(@RequestParam String activityId, @RequestParam String uid) {
        return HttpResult.getOk(happySaudiNationalService.happySaudiConfig(activityId, uid));
    }

    @RequestMapping("happySaudiDraw")
    public HttpResult<HappySaudiVO.LuckyOneVO> happySaudiDraw(@RequestParam String activityId, @RequestParam String uid) {
        return HttpResult.getOk(happySaudiNationalService.happySaudiDraw(activityId, uid));
    }

    @RequestMapping("happySaudiFriendList")
    public HttpResult<PageVO<HappySaudiVO.FriendVO>> happySaudiFriendList(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        return HttpResult.getOk(happySaudiNationalService.getFriendList(activityId, uid, page));
    }

    // 与shareToPrivateMsgByAid接口一致
    @RequestMapping("happySaudiShare")
    public HttpResult<?> shareToPrivateMsg(@RequestBody ShareActivityDTO dto) {
        happySaudiNationalService.shareToPrivateMsg(dto);
        return HttpResult.getOk();
    }
}
