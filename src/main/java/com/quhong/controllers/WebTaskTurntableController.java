package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.datas.HttpResult;
import com.quhong.dto.DailyTaskDTO;
import com.quhong.dto.TaskTurntableDTO;
import com.quhong.service.TaskTurntableService;
import com.quhong.service.WebTaskCenterService;
import com.quhong.vo.TaskListVO;
import com.quhong.vo.TaskRewardVO;
import com.quhong.vo.WebTurntableVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 任务中心转盘
 */
@RestController
@RequestMapping("/user/")
public class WebTaskTurntableController {

    private static final Logger logger = LoggerFactory.getLogger(WebTaskTurntableController.class);

    @Resource
    private TaskTurntableService taskTurntableService;
    @Resource
    private WebTaskCenterService webTaskCenterService;


    /**
     * web任务中心
     */
    @RequestMapping("webTaskList")
    private HttpResult<TaskListVO> webTaskList(@RequestBody DailyTaskDTO dto) {
        logger.info("get webTurntableConfig dto={}", JSONObject.toJSONString(dto));
        return HttpResult.getOk(webTaskCenterService.webTaskList(dto));
    }

    /**
     * web领取奖励
     */
    @RequestMapping("getWebTaskReward")
    private HttpResult<TaskRewardVO> getWebTaskReward(@RequestBody DailyTaskDTO dto) {
        logger.info("getWebTaskReward dto={}", JSONObject.toJSONString(dto));
        return HttpResult.getOk(webTaskCenterService.getTaskReward(dto));
    }

    /**
     * web转盘配置
     */
    @RequestMapping("webTurntableConfig")
    private HttpResult<WebTurntableVO> webTurntableConfig(@RequestParam String uid) {
        logger.info("get webTurntableConfig uid={}", uid);
        return HttpResult.getOk(taskTurntableService.webTurntableConfig(uid));
    }

    /**
     * 转盘抽奖
     */
    @RequestMapping("webTurntableDraw")
    private HttpResult<WebTurntableVO> webTurntableDraw(@RequestBody TaskTurntableDTO dto) {
        logger.info("taskTurntableDraw dto: {}", JSONObject.toJSONString(dto));
        return HttpResult.getOk(taskTurntableService.webTurntableDraw(dto));
    }

    /**
     * 转盘抽奖记录
     */
    @RequestMapping("webTurntableRecord")
    public HttpResult<WebTurntableVO> webTurntableRecord(@RequestParam String uid, @RequestParam int page) {
        logger.info("webTurntableRecord uid: {}, page:{}", uid, page);
        return HttpResult.getOk(taskTurntableService.webTurntableRecord(uid, page));
    }

}
