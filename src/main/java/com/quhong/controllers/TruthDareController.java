package com.quhong.controllers;

import com.quhong.core.distribution.DistributeLock;
import com.quhong.dto.TruthDareDTO;
import com.quhong.enums.GameHttpCode;
import com.quhong.enums.HttpCode;
import com.quhong.exception.GameException;
import com.quhong.handler.WebController;
import com.quhong.service.TruthDareService;
import com.quhong.utils.RequestUtils;
import com.quhong.vo.TruthDareGameVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 真心话大冒险转盘
 *
 * <AUTHOR>
 * @date 2023/3/20
 */
@RestController
@RequestMapping("${baseUrl}/truth_dare_turntable/")
public class TruthDareController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(TruthDareController.class);

    private static final String TURNTABLE_LOCK_KEY = "create_truth_dare_turntable_game_";

    private static final Object NULL = new Object();

    @Resource
    private TruthDareService truthDareService;

    /**
     * 创建真心话转盘游戏
     */
    @PostMapping("create_game")
    public String create(HttpServletRequest request) {
        TruthDareDTO reqDTO = RequestUtils.getSendData(request, TruthDareDTO.class);
        logger.info("create truth dare turntable game. uid={} roomId={} joinBeans={}", reqDTO.getUid(), reqDTO.getRoom_id(), reqDTO.getJoin_beans());
        if (!checkParam(reqDTO)) {
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        DistributeLock lock = new DistributeLock(TURNTABLE_LOCK_KEY + reqDTO.getRoom_id());
        try {
            lock.lock();
            TruthDareGameVO vo = truthDareService.createGame(reqDTO);
            return createResult(reqDTO, GameHttpCode.CREATE_GAME_SUCCESS, vo);
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("create truth dare turntable game error. roomId={} uid={} {}", reqDTO.getRoom_id(), reqDTO.getUid(), e.getMessage(), e);
            throw e;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 加入真心话转盘游戏
     */
    @PostMapping("join_game")
    public String join(HttpServletRequest request) {
        TruthDareDTO reqDTO = RequestUtils.getSendData(request, TruthDareDTO.class);
        logger.info("join truth dare turntable game. uid={} roomId={} joinBeans={} joinSelf={}", reqDTO.getUid(), reqDTO.getRoom_id(), reqDTO.getJoin_beans(), reqDTO.getJoin_self());
        if (StringUtils.isEmpty(reqDTO.getUid()) || StringUtils.isEmpty(reqDTO.getGame_id())) {
            logger.error("uid and gameId can not be empty. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoom_id());
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        DistributeLock lock = new DistributeLock(TURNTABLE_LOCK_KEY + reqDTO.getGame_id());
        try {
            lock.lock();
            TruthDareGameVO vo = truthDareService.joinGame(reqDTO);
            return createResult(reqDTO, GameHttpCode.JOIN_GAME_SUCCESS, vo);
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("join truth dare turntable game error. gameId={} uid={} {}", reqDTO.getGame_id(), reqDTO.getUid(), e.getMessage(), e);
            throw e;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 开始真心话转盘游戏
     */
    @PostMapping("start_game")
    public String start(HttpServletRequest request) {
        TruthDareDTO reqDTO = RequestUtils.getSendData(request, TruthDareDTO.class);
        logger.info("start truth dare turntable game. uid={} gameId={}", reqDTO.getUid(), reqDTO.getGame_id());
        if (StringUtils.isEmpty(reqDTO.getUid()) || StringUtils.isEmpty(reqDTO.getGame_id())) {
            logger.error("uid and gameId can not be empty. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoom_id());
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        DistributeLock lock = new DistributeLock(TURNTABLE_LOCK_KEY + reqDTO.getGame_id());
        try {
            lock.lock();
            TruthDareGameVO vo = truthDareService.startGame(reqDTO);
            return createResult(reqDTO, HttpCode.SUCCESS, vo);
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("start truth dare turntable game error. gameId={} uid={} {}", reqDTO.getGame_id(), reqDTO.getUid(), e.getMessage(), e);
            throw e;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 转盘游戏等待中点击关闭
     */
    @PostMapping("close_at_waiting")
    public String closeAtWaiting(HttpServletRequest request) {
        TruthDareDTO reqDTO = RequestUtils.getSendData(request, TruthDareDTO.class);
        logger.info("close truth dare turntable game. uid={} gameId={}", reqDTO.getUid(), reqDTO.getGame_id());
        if (StringUtils.isEmpty(reqDTO.getUid()) || StringUtils.isEmpty(reqDTO.getGame_id())) {
            logger.error("uid and gameId can not be empty. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoom_id());
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        DistributeLock lock = new DistributeLock(TURNTABLE_LOCK_KEY + reqDTO.getGame_id());
        try {
            lock.lock();
            truthDareService.closeAtWaiting(reqDTO);
            return createResult(reqDTO, HttpCode.SUCCESS, NULL);
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("close truth dare turntable game error. gameId={} uid={} {}", reqDTO.getGame_id(), reqDTO.getUid(), e.getMessage(), e);
            throw e;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 游戏过程中刷新游戏的状态
     */
    @PostMapping("refresh_game")
    public String refreshGame(HttpServletRequest request) {
        TruthDareDTO reqDTO = RequestUtils.getSendData(request, TruthDareDTO.class);
        logger.info("get truth or dare turntable game info. uid={} gameId={} ", reqDTO.getUid(), reqDTO.getGame_id());
        if (StringUtils.isEmpty(reqDTO.getGame_id())) {
            logger.error("gameId is empty. gameId={}", reqDTO.getGame_id());
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        return createResult(reqDTO, HttpCode.SUCCESS, truthDareService.refreshGame(reqDTO));
    }


    /**
     * 校验创建游戏参数
     */
    private boolean checkParam(TruthDareDTO reqDTO) {
        if (StringUtils.isEmpty(reqDTO.getUid()) || StringUtils.isEmpty(reqDTO.getRoom_id())) {
            logger.error("uid and room_id can not be empty. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoom_id());
            return false;
        }
        int joinBeans = reqDTO.getJoin_beans();
        if (joinBeans < 0 || joinBeans > 100) {
            logger.info("param joinBeans error. joinBeans={}", joinBeans);
            return false;
        }
        if (CollectionUtils.isEmpty(reqDTO.getOpt_msg())) {
            logger.info("param optMsg is empty.");
            return false;
        }
        return reqDTO.getJoin_self() == 0 || reqDTO.getJoin_self() == 1;
    }
}
