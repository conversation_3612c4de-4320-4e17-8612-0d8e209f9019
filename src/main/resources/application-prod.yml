baseUrl: /user/
server:
  port: 8080
spring:
  application:
    name: ustar-java-user
  messages:
    basename: i18n/user
    encoding: UTF-8
  cloud:
    kubernetes:
      discovery:
        catalog-services-watch:
          enabled: false
feign:
  hystrix:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 3000
        readTimeout: 60000
hystrix:
  command:
    default:
      execution:
        timeout:
          enabled: true
        isolation:
          thread:
            timeoutInMilliseconds: 10000
dubbo:
  application:
    name: ${spring.application.name}
    qos-enable: false
    metadata-service-port: 20885
  registry:
    address: kubernetes://DEFAULT_MASTER_HOST?registry-type=service&duplicate=false&trustCerts=true&namespace=devops
  protocol:
    name: dubbo
    port: 20880
  metadata-report:
    report-metadata: false
  service:
    shutdown:
      wait: 5000
