package com.quhong.operation.test;

import com.quhong.constant.WelcomeMessageReviewConstant;
import com.quhong.mysql.dao.WelcomeMessageReviewDao;
import com.quhong.mysql.data.WelcomeMessageReviewData;
import com.quhong.operation.server.WelcomeMessageReviewService;
import com.quhong.operation.share.condition.WelcomeMessageCondition;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 欢迎消息审核服务测试
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class WelcomeMessageReviewServiceTest {

    @Resource
    private WelcomeMessageReviewService welcomeMessageReviewService;
    
    @Resource
    private WelcomeMessageReviewDao welcomeMessageReviewDao;

    @Test
    public void testDeleteEarliestRejectedRecord() {
        String testRoomId = "test_room_123";
        
        // 创建测试数据 - 插入一些被拒绝的记录
        for (int i = 0; i < 5; i++) {
            WelcomeMessageReviewData data = new WelcomeMessageReviewData();
            data.setSubmitterUid("test_uid_" + i);
            data.setRoomId(testRoomId);
            data.setMessageContent("测试消息内容 " + i);
            data.setReviewAction(WelcomeMessageReviewConstant.REVIEW_STATUS_REJECTED);
            data.setRejectReason("测试拒绝原因");
            data.setSubmitTime((int) (System.currentTimeMillis() / 1000) - (5 - i) * 3600); // 不同的提交时间
            data.setUpdateTime((int) (System.currentTimeMillis() / 1000));
            welcomeMessageReviewDao.insert(data);
        }
        
        // 查询被拒绝记录的数量
        WelcomeMessageReviewData condition = new WelcomeMessageReviewData();
        condition.setRoomId(testRoomId);
        condition.setReviewAction(WelcomeMessageReviewConstant.REVIEW_STATUS_REJECTED);
        long countBefore = welcomeMessageReviewDao.selectCount(condition);
        System.out.println("删除前被拒绝记录数量: " + countBefore);
        
        // 测试删除最早的被拒绝记录
        int deletedCount = welcomeMessageReviewDao.deleteEarliestRejectedRecord(testRoomId);
        System.out.println("删除的记录数量: " + deletedCount);
        
        // 验证删除后的数量
        long countAfter = welcomeMessageReviewDao.selectCount(condition);
        System.out.println("删除后被拒绝记录数量: " + countAfter);
        
        // 断言
        assert deletedCount == 1 : "应该删除1条记录";
        assert countAfter == countBefore - 1 : "删除后数量应该减少1";
        
        // 清理测试数据
        cleanupTestData(testRoomId);
    }

    @Test
    public void testUpdateReviewActionWithNotification() {
        String testRoomId = "test_room_456";
        String operatorUid = "operator_123";
        
        // 创建测试数据
        WelcomeMessageReviewData data = new WelcomeMessageReviewData();
        data.setSubmitterUid("test_uid");
        data.setRoomId(testRoomId);
        data.setMessageContent("这是一条很长的测试消息内容，用来测试前15个字符的截取功能");
        data.setReviewAction(WelcomeMessageReviewConstant.REVIEW_STATUS_PENDING);
        data.setSubmitTime((int) (System.currentTimeMillis() / 1000));
        data.setUpdateTime((int) (System.currentTimeMillis() / 1000));
        welcomeMessageReviewDao.insert(data);
        
        // 测试审核通过
        WelcomeMessageCondition condition = new WelcomeMessageCondition();
        condition.setRoomId(testRoomId);
        condition.setId(data.getId());
        condition.setReviewAction(WelcomeMessageReviewConstant.REVIEW_STATUS_APPROVED);
        
        try {
            welcomeMessageReviewService.updateReviewAction(operatorUid, condition);
            System.out.println("审核通过测试成功");
        } catch (Exception e) {
            System.out.println("审核通过测试失败: " + e.getMessage());
        }
        
        // 清理测试数据
        cleanupTestData(testRoomId);
    }

    @Test
    public void testMessagePreview() {
        // 这个测试需要通过反射来测试私有方法，或者我们可以通过集成测试来验证
        String longMessage = "这是一条很长的测试消息内容，用来测试前15个字符的截取功能";
        String shortMessage = "短消息";
        
        System.out.println("长消息原文: " + longMessage);
        System.out.println("长消息长度: " + longMessage.length());
        System.out.println("前15个字符: " + longMessage.substring(0, Math.min(15, longMessage.length())));
        
        System.out.println("短消息原文: " + shortMessage);
        System.out.println("短消息长度: " + shortMessage.length());
        System.out.println("前15个字符: " + shortMessage.substring(0, Math.min(15, shortMessage.length())));
    }
    
    @Test
    public void testSelectPageListAll() {
        // 创建测试数据 - 在不同的分表中插入数据
        String[] testRoomIds = {"test_room_0", "test_room_1", "test_room_a", "test_room_f"};

        for (String roomId : testRoomIds) {
            WelcomeMessageReviewData data = new WelcomeMessageReviewData();
            data.setSubmitterUid("test_uid_all");
            data.setRoomId(roomId);
            data.setMessageContent("测试所有分表查询的消息内容 " + roomId);
            data.setReviewAction(WelcomeMessageReviewConstant.REVIEW_STATUS_PENDING);
            data.setSubmitTime((int) (System.currentTimeMillis() / 1000));
            data.setUpdateTime((int) (System.currentTimeMillis() / 1000));
            welcomeMessageReviewDao.insert(data);
        }

        // 测试查询所有分表
        WelcomeMessageReviewData condition = new WelcomeMessageReviewData();
        condition.setReviewAction(WelcomeMessageReviewConstant.REVIEW_STATUS_PENDING);

        // 测试分页查询所有分表
        List<WelcomeMessageReviewData> allRecords = welcomeMessageReviewDao.selectPageListAll(1, 10, condition);
        System.out.println("查询所有分表的记录数量: " + allRecords.size());

        // 测试查询所有分表的总数
        long totalCount = welcomeMessageReviewDao.selectCountAll(condition);
        System.out.println("所有分表的总记录数: " + totalCount);

        // 验证结果
        assert allRecords.size() >= testRoomIds.length : "应该至少查询到测试插入的记录";
        assert totalCount >= testRoomIds.length : "总数应该至少包含测试插入的记录";

        // 验证查询结果包含不同分表的数据
        boolean hasMultipleTables = false;
        String firstRoomId = null;
        for (WelcomeMessageReviewData record : allRecords) {
            if (firstRoomId == null) {
                firstRoomId = record.getRoomId();
            } else if (!firstRoomId.equals(record.getRoomId())) {
                hasMultipleTables = true;
                break;
            }
        }

        System.out.println("查询结果包含多个分表的数据: " + hasMultipleTables);

        // 清理测试数据
        for (String roomId : testRoomIds) {
            cleanupTestData(roomId);
        }
    }

    @Test
    public void testSelectCountAllWithConditions() {
        // 创建不同状态的测试数据
        String[] testRoomIds = {"test_room_2", "test_room_3", "test_room_b", "test_room_c"};

        for (int i = 0; i < testRoomIds.length; i++) {
            WelcomeMessageReviewData data = new WelcomeMessageReviewData();
            data.setSubmitterUid("test_uid_count");
            data.setRoomId(testRoomIds[i]);
            data.setMessageContent("测试计数功能的消息内容 " + i);
            // 交替设置不同的审核状态
            data.setReviewAction(i % 2 == 0 ?
                WelcomeMessageReviewConstant.REVIEW_STATUS_PENDING :
                WelcomeMessageReviewConstant.REVIEW_STATUS_APPROVED);
            data.setSubmitTime((int) (System.currentTimeMillis() / 1000));
            data.setUpdateTime((int) (System.currentTimeMillis() / 1000));
            welcomeMessageReviewDao.insert(data);
        }

        // 测试查询待审核状态的总数
        WelcomeMessageReviewData pendingCondition = new WelcomeMessageReviewData();
        pendingCondition.setReviewAction(WelcomeMessageReviewConstant.REVIEW_STATUS_PENDING);
        long pendingCount = welcomeMessageReviewDao.selectCountAll(pendingCondition);
        System.out.println("所有分表中待审核记录总数: " + pendingCount);

        // 测试查询已通过状态的总数
        WelcomeMessageReviewData approvedCondition = new WelcomeMessageReviewData();
        approvedCondition.setReviewAction(WelcomeMessageReviewConstant.REVIEW_STATUS_APPROVED);
        long approvedCount = welcomeMessageReviewDao.selectCountAll(approvedCondition);
        System.out.println("所有分表中已通过记录总数: " + approvedCount);

        // 测试查询所有状态的总数
        WelcomeMessageReviewData allCondition = new WelcomeMessageReviewData();
        long allCount = welcomeMessageReviewDao.selectCountAll(allCondition);
        System.out.println("所有分表中所有记录总数: " + allCount);

        // 验证结果
        assert pendingCount >= 2 : "待审核记录应该至少有2条";
        assert approvedCount >= 2 : "已通过记录应该至少有2条";
        assert allCount >= pendingCount + approvedCount : "总数应该大于等于各状态数量之和";

        // 清理测试数据
        for (String roomId : testRoomIds) {
            cleanupTestData(roomId);
        }
    }

    /**
     * 清理测试数据
     */
    private void cleanupTestData(String roomId) {
        try {
            // 这里可以添加清理逻辑，删除测试数据
            // 由于是测试环境，可以保留数据用于调试
            System.out.println("清理测试数据: " + roomId);
        } catch (Exception e) {
            System.out.println("清理测试数据失败: " + e.getMessage());
        }
    }
}
